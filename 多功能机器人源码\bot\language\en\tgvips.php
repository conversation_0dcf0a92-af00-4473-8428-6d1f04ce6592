<?php
return [
    '语言包键名' => "Here are the corresponding values. You can add N key names corresponding to N values. When calling, call the key names to obtain the corresponding values. After the development is completed, copy this file to the en directory, and then translate the corresponding values into other languages. The framework has a multi-language system that automatically reads the corresponding key name and obtains the value according to the user's current language.", 
    '启动按钮'=>"💎buyTGvip",
    '开通电报会员'=>"buy TG vip",
    '3个月'=>"3 months",
    '6个月'=>"6 months",
    '12个月'=>"12 months",
    '开通时长'=>"Opening time",
    '订单金额'=>"Order amount",
    '确认支付'=>"Confirm payment",
    '正在赠送错误'=>"<b>You are giving away membership, the target user name is wrong..</b>\n<code>Please re-enter the other party's Telegram user name</code>",
    '正在开通电报会员'=>" You are opening a Telegram Premium User (Membership)...",
    '给自己开通'=>"Open for yourself",
    '赠送给他人'=>"Give to others",
    '个月'=>"Month", 
    '输入它的电报用户名'=>"Enter its Telegram username：", 
    '你正在赠送电报会员'=>"You are giving Telegram membership...", 
    '赠送目标'=>"Gift Target", 
    '用户名'=>"username", 
    '昵称名'=>"Nickname", 
    '你正在开通电报会员'=>"You are signing up for Telegram membership...", 
    '暂时不允许给他人开通会员'=>"It is not allowed to open membership for others at this time！", 
    '未设置用户名无法开通'=>"<b>Sorry, you haven't set up a user name yet, so you can't activate VIP</b>", 
    '余额不足请充值'=>"<b>Sorry, your account balance is insufficient, please recharge..</b>", 
    '开通结果'=>"<b>[Activation results]</b>", 
    '交易凭证'=>"Transaction voucher", 
    '失败原因'=>"Cause of failure", 
    '联系管理员反馈'=>"Please contact the administrator to report this issue!", 
    '开通成功'=>"✅Congratulations, the activation is successful. Please pay attention to the activation status!", 
   ];