<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class shuang  {  
    /**
     * 【参数解答】
     * $message["message"]      =   电报原始消息
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * next (不强制回复·但下一条文本消息将作为本次交互所需要的回复内容)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return $ret;
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        $model = new model\shuang_set;
        $set = $model->getJSON($message['bot']['API_BOT']);  
        if(empty($set['json']['qunid'])){
            return $ret; //没有设置客服群ID
        }
        
     
        #下面开始写代码
        if($message["chatType"] == 'private'){   
                 
             
                
                //判断发消息的人是不是客服
               
                $toID = $set['json']['qunid'];
                
                $anniu['inline_keyboard'] = [ 
                    // [
                    //     [
                    //         "text" => "💬 立即回复Ta",
                    //         "callback_data" => "双向回复消息_{$message['formId']}_{$message['msgId']}"
                    //     ]
                    // ]
                ];  
                $an = json_encode($anniu); 
                $base = new Base;  
                
                if(isset($message['HuiFu']['text'])){
                    $message['text'] = "@{$message['HuiFu']['toUser']}：\n{$message['HuiFu']['text']}\n\n@{$message['formUser']}：\n{$message['text']}";
                }else{
                    $message['text'] = "@{$message['formUser']}：\n{$message['text']}";
                } 
                 
                
                if(!empty($message["photo"][0])){
                    $file = $message["photo"][0]['file_id'];
                    $base->sendUrl("/sendPhoto?chat_id={$toID}&caption=<pre>%23{$message['formId']}%23{$message['msgId']}</pre>\n{$message['text']}&reply_markup={$an}&photo={$file}",0,1);
                }else if(!empty($message["document"])){
                    $file = $message["document"]['file_id'];
                    $base->sendUrl("/sendDocument?chat_id={$toID}&caption=<pre>%23{$message['formId']}%23{$message['msgId']}</pre>\n{$message['text']}&reply_markup={$an}&document={$file}",0,1);
                }else if(!empty($message["text"])){
                    $base->sendUrl("/sendMessage?chat_id={$toID}&text=<pre>%23{$message['formId']}%23{$message['msgId']}</pre>\n{$message['text']}&reply_markup={$an}",0,1);
                }
                    
                      
                 
         
        }else if($message['chatId'] == $set['json']['qunid']){
            if(isset($message['HuiFu']['botUser']) == $message['bot']['API_BOT']){ 
                 if($ss = pipei($message['HuiFu']['text'],"^\#([0-9]+)\#([0-9]+)")){
                        $base = new Base;  
                        if(!empty($message["photo"][0])){
                            $file = $message["photo"][0]['file_id'];
                            $base->sendUrl("/sendPhoto?chat_id={$ss[1]}&caption={$message['text']}&reply_to_message_id={$ss[2]}&photo={$file}",0,1);
                        }else if(!empty($message["document"])){
                            $file = $message["document"]['file_id'];
                            $base->sendUrl("/sendDocument?chat_id={$ss[1]}&caption={$message['text']}&reply_to_message_id={$ss[2]}&document={$file}",0,1);
                        }else if(!empty($message["text"])){
                            $base->sendUrl("/sendMessage?chat_id={$ss[1]}&text={$message['text']}&reply_to_message_id={$ss[2]}",0,1);
                        }
                        
                        $message['HuiFu']['text'] = urlencode($message['HuiFu']['text']);
                        
                        if(isset($message['message']['reply_to_message']['caption'])){
                            $base->sendUrl("/editMessageCaption?chat_id={$message['chatId']}&caption={$message['HuiFu']['text']}\n\n@{$message['formUser']}：\n{$message['text']}&message_id={$message['HuiFu']['msgId']}&parse_mode=HTML");
                        }else{ 
                            $base->sendUrl("/editMessageText?chat_id={$message['chatId']}&text={$message['HuiFu']['text']}\n\n@{$message['formUser']}：\n{$message['text']}&message_id={$message['HuiFu']['msgId']}&parse_mode=HTML");
                        }
                        
                        
                 }
                
            } 
            return $ret; 
        }
        
        // else if(!empty($message['action'])){
        //     if($message['action'] == '双向回复消息'){
        //         if($ss =  pipei($message['oldText'],"双向回复消息_([0-9]+)\_([0-9]+)")){
        //             $base = new Base;  
                    
        //             $anniu['inline_keyboard'] = [ 
        //                 [
        //                     [
        //                         "text" => "💬 已回复(再次回复)",
        //                         "callback_data" => "双向回复消息_{$message['formId']}_{$message['msgId']}"
        //                     ]
        //                 ]
        //             ]; 
        //             $an = json_encode($anniu); 
        //             $toID = '-4194320811'; 
                     
                     
        //             if(!empty($message["photo"][0])){
        //                 $file = $message["photo"][0]['file_id'];
        //                 $base->sendUrl("/sendPhoto?chat_id={$ss[1]}&caption={$message['actionText']}&reply_to_message_id={$ss[2]}&photo={$file}");
        //             }else if(!empty($message["document"][0])){
        //                 $file = $message["document"][0]['file_id'];
        //                 $base->sendUrl("/sendDocument?chat_id={$ss[1]}&caption={$message['actionText']}&reply_to_message_id={$ss[2]}&document={$file}");
        //             }else if(!empty($message["actionText"])){
        //                 $base->sendUrl("/sendMessage?chat_id={$ss[1]}&text={$message['actionText']}&reply_to_message_id={$ss[2]}");
        //             }
        //         }
        //     }
            
        // }
        
        
        
        #代码结束
        return $ret;     
    }
} 
