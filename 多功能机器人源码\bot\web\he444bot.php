<?php
namespace bot\web;

use app\model;                          #模型  
use bot\mdb;                          #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

use support\Request; 

//后端api文件 GET无需鉴权，POST需jwt鉴权(自动的不用管) 
class he444bot { 
   
    public function index(Request $request){
        $name = "he444bot 的 index 控制器方法,返回的文本字符"; 
        return response("你好这是：" . $name);
    } 
    
    #function 对应后台模块 >设置菜单>子菜单 中的（api方法）
    public function list(Request $request){   
        return viewTP( "/he444bot/list", ["name" => "97bot"]); #说明：views返回视图html，其中的list代表： bot/web/he444bot/list.html
    }
    
    #一个默认的获取对应模块的配置设置视图 
    public function set(Request $request){   
        return viewTP( "/he444bot/set", ["name" => "97bot"]); #说明：views返回视图html，其中的set代表： bot/web/he444bot/set.html
    }
     
    
    //获取：bot_account数据表,数据接口的例子 GET 
    public function api_list(Request $request){  
        $data = $request->get();
        $page = ($data["page"]-1)*$data["limit"]; 
        $jsonVal = array();
        $so =[];  
          
        //前置条件id字段大于0
        array_push($so,"id");
        array_push($so,'>');
        array_push($so,0); 
           
          
        //按状态匹配数据 
        if(!empty($data["state"])){
            if($data["state"] == 1){
              array_push($so,"zt");
              array_push($so,"=");
              array_push($so,1);   
            }else{
              array_push($so,"zt");
              array_push($so,"=");
              array_push($so,0);  
            } 
     
        } 
        //按字段进行模糊搜索数据
        if(!empty($data["keyword"])){
           array_push($so,$data["t"]?$data["t"]:"API_BOT");
           array_push($so,"like");
           array_push($so,"%".$data["keyword"]."%");
        }
          
        //定义事件日期的搜索
        if(!empty($data["timea"])){
           array_push($so,"create_time");
           array_push($so,">");
           array_push($so,substr($data["timea"],0,10));
           array_push($so,"create_time");
           array_push($so,"<");
           array_push($so,substr($data["timeb"],0,10));
        }
          
        $so = array_chunk($so,3);//拆分  
        
        $count = Db::name("he444bot_list")->where([$so])->count(); 
        $list = Db::name("he444bot_list")->where([$so])->limit($page,$data["limit"])->order("id desc")->select();  
     
        $jsonVal["count"] = $count;
        $jsonVal["list"] = $list;   
        return json(["code" => 1,"msg"=>"请求成功","data" => $jsonVal ]);  
    }
    
    
    #获取机器人配置 
    public function setget(Request $request){   
        $data = $request->get();  
        if(empty($data['bot'])){
            $energy_set =  mdb\he444bot::where('bot', 0)->find();  
        }else{
            if($data['bot'] == "全局设置"){
                $data['bot'] = 0;
            }
            $energy_set =  mdb\he444bot::where('bot', $data['bot'])->find();  
        } 
        return json(["code" => 1,  "msg" => "获取成功","data"=>$energy_set]);  
    }
    
    // #获取机器人配置 
    // public function setget(Request $request){   
    //     $data = $request->get();  
    //     if(empty($data['bot'])){
    //         $he444bot_set =  mdb\he444bot::where('bot', 0)->find();  
    //         if(empty($he444bot_set)){
    //             return json(["code" => 0,  "msg" => "请选择一个机器人","data"=>$he444bot_set ]); 
    //         }else{
    //             return json(["code" => 1,  "msg" => "获取总机器人数据成功","data"=>$he444bot_set]); 
    //         }
    //     }else{ 
    //         $he444bot_set =  mdb\he444bot::where('bot', $data['bot'])->find();  
    //         if(empty($he444bot_set)){//没有机器人数据  
    //             $he444bot_set =  mdb\he444bot::where('bot', 0)->find();//获取总管机器人数据
    //             if($he444bot_set){//获取成功
    //                 unset($he444bot_set['id']);
    //                 $he444bot_set['bot']=$data['bot'];
    //                 $he444bot_set['type']=0; 
    //                 return json(["code" => 1,  "msg" => "该机器人未配置,默认获取总管机器人的配置数据","data"=>$he444bot_set]);  
                    
    //             }else{//未设置总管机器人 获取失败
    //                 return json(["code" => 0,  "msg" => "该机器人似乎没有配置数据哟" ]); 
    //             }
                
    //         }
    //     } 
    //     return json(["code" => 1,  "msg" => "获取成功","data"=>$he444bot_set]);  
    // }
    
    
    
    //通用的post新建数据接口
    public function create(Request $request){  
        $data = $request->post();
        return json(["code" => 1, "msg" => "新建成功" ,"data"=>"返回给前端的数据"]);
    }
    
    
    //通用的post数据更新接口
    public function update(Request $request){  
        $data = $request->post();
        if(empty($data['type'])){
            return json(["code" => 0,  "msg" => "缺少更新方法标识：type"]);  
        }
        if($data["data"]['bot'] == "全局设置"){
                $data["data"]['bot'] = 0;
        }
        switch ($data['type']) {
            default:
                return json(["code" => 0,  "msg" => "更新方法：{$data['type']} 未写代码,请在/bot/web/he444bot.php文件的function update 下增加一个case方法"]);  
                break;
                
            case 'set':
                $data["data"]["value"] = str_replace("\n", "|", $data["data"]["value"]);//换行
                $data["data"]["value"] = preg_replace("/\s+/", "", $data["data"]["value"]);//空格 
                $data["data"]["value"] = preg_replace("/\|{2,}/", "|", $data["data"]["value"]);//2个以上替换
                $data["data"]["value"] = preg_replace("/^\|/", "", $data["data"]["value"]);//开始
                $data["data"]["value"] = preg_replace("/\|$/", "", $data["data"]["value"]);//结束 
                if(empty($data["data"]["id"])){ 
                    mdb\he444bot::create($data["data"]);
                }else{
                    Cache::delete("he444bot_{$data["data"]['bot']}");
                    $he444bot_set =  mdb\he444bot::where("id",$data["data"]["id"])->find();
                    $he444bot_set->save($data["data"]); 
                } 
                break; 
        }
        
        return json(["code" => 1, "msg" => "更新成功" ,"data"=>"返回给前端的数据"]);
    }
    
    
    //通用的post删除数据接口
    public function del(Request $request){  
        $data = $request->post();
        if(empty($data)){
            return json(["code" => 0, "msg" => "删除失败" ,"data"=>"返回给前端的数据"]);
        }
        if($data['type'] == "shanchuqun"){
            mdb\he444bot_list::where("qunid",$data['qunid'])->delete();
        } 
        return json(["code" => 1, "msg" => "删除成功" ,"data"=>"返回给前端的数据"]);
    }
    
    
}