<?php
namespace bot\res\jizhang;

use app\model\menu;
use support\Redis;                      #redis

class Install{ 
    
    //安装菜单   
    public static function install($plugin){   
        $menu1["app"] = "jizhang";
        $menu1["name"] = "记账机器人";
        $menu1["path"] = "";
        $menu1["lv"] = 200;
        $menu1["icon"] = "el-icon-_keyboard";
        
        $menu2 = '[{"icon":"el-icon-_setting","name":"群设置列表","path":"list","lv":"2"},{"icon":"el-icon-setting","name":"记账设置","path":"set","lv":"0"}]'; 
        
        $ins = new menu;
        $ins->install($menu1,$menu2); 
        
        
        // $list1 = Redis::keys("*inc");
        // $list2 = Redis::keys("*dec");
        // $list3 = Redis::keys("*totalz*");
        // $list = array_merge($list1, $list2,$list3);
        // foreach ($list as $value) {
        //     Redis::del($value);
        // }
        
    } 
    
}
