<?php
return [
    'startText' => "<u><b>TRC20 Address Monitoring</b></u> Supports USDT TRX income and expenditure", 
    'startBtn' => "🔔 Add listen address", 
    '添加监听地址界面' => "Supports USDT TRX income and expenditure monitoring of TRON chain", 
    '新增地址' => "add address", 
    '我的地址' => "my address", 
    '请输入你的钱包地址' => "Enter your wallet", 
    '新增TRC20监听地址' => "Add TRC20 listening address", 
    '你尚未增加监听地址' => "You haven't added listening address yet", 
    '删除地址' => "Delete address", 
    '请输入要删除的钱包地址' => "Enter the wallet address to be deleted", 
    '请输入要删除的TRC监听地址' => "Please enter the TRC listening address you want to delete", 
    '免费监听地址已达上限：5 个' => "Free listening addresses have reached the upper limit: 5", 
    '你已监听该地址,无需重复添加' => "You have already monitored this address, no need to add it again", 
    '添加成功,已监听地址数量：' => "Added successfully, the number of monitored addresses:", 
    '继续新增地址' => "continue to add address", 
    '新增监听地址失败,地址错误' => "Failed to add listening address, wrong address", 
    '监听地址删除成功' => "Listening address deleted successfully", 
    '删除监听地址失败,地址错误' => "Failed to delete the listening address, the address is wrong",  
    '电报通知' => "Telegram",  
    'POST通知' => "POST Msg",  
   ];