-- 表结构：tb_tgvips_set
CREATE TABLE `tb_tgvips_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
-- 表数据：tb_tgvips_set
INSERT INTO `tb_tgvips_set` VALUES ('1','0','','0','','{\"hash\": \"39a4705879d0df9457\", \"word\": \"请填写助词器 24个单词\", \"cookie\": \"stel_ssid=14acd2b8da10dd2d86_15966808820889802637; stel_dt=-480; stel_ton_token=s0U_qhgau1y2D2Byxm4jm3vD9e-PhUxSTkoBpys6E53n_K1hsoSTQ1msocN7yOV3IPZrfpkyfkTkndb_uiCp1zi70Wvc1oTs5CWpDzGtoUBgeFKOhM8O9LJ84PuC6yAezbXbcE8x8t0-iCYfB9jhodfTuuNq2QIKw0u6DhbY-INm6YRLxoQ\", \"3个月\": \"16\", \"6个月\": \"20\", \"address\": \"\", \"12个月\": \"35\", \"给他人开通\": 1}','0','1710052547');
