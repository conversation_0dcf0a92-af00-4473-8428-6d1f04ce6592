<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class usdtjt  {  
    /**
     * 【参数解答】
     * $message['bot']          =   机器人配置信息
     * $message['msgId']        =   聊天消息唯一ID
     * 
     * $message['chatType']     =   聊天类型 群=supergroup  私聊=private
     * $message['chatId']       =   聊天窗口ID
     * $message['chatUser']     =   聊天窗口用户名(@xxx)
     * $message['chatName']     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message['formId']       =   发消息的人ID
     * $message['formUser']     =   发消息的人用户名
     * $message['formName']     =   发消息的人昵称
     * $message['fromVip']      =   发消息的人是否是电报VIP 0否 1是
     * $message['fromLang']     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message['text']         =   消息文本内容
     * $message['time']         =   消息到达-服务器时间戳  
     * $message['tgTime']       =   消息到达-电报官方时间戳 
     * 
     * $message['photo']        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message['document']     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message['video']        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message['gif']          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message['isHuiFu']       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message['HuiFu']['msgId']  =   被回复的消息ID   
     *      $message['HuiFu']['isBot']  =   被回复的目标是否为机器人 1是0否
     *      $message['HuiFu']['botUser']=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message['HuiFu']['toId']   =   被回复消息的人ID
     *      $message['HuiFu']['toUser'] =   被回复消息的人用户名
     *      $message['HuiFu']['toVip']  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message['HuiFu']['text']   =   被回复消息的内容
     * 
     * $ret支持回调参数：sendText(文本消息) sendPhoto(发送照片) sendVideo(发送视频)  anniu(消息按钮)   [ jianpan(回复键盘) && jianpanText(文字消息) || jianpanPhoto(照片消息)]
     * 
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret['key']=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret['level']=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面 
        if(!isset($message['bot']['appStore'][$ret['key']])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return $ret; 
                }
            }
        }
        #-----------------以上核心代码勿动 level 视情况调整改动--------------------------
        if($message['text'] == '🔔地址监听'){
            $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
            if($message["chatType"] == "supergroup" ){
                $chatText = "本群";
            }else{
                $chatText = "你";
            }
            
            
            $ret["sendText"] = "📮 <b>[USDT监控]</b>  <b>[TRX监控]</b>  <b>[多签监控]</b>\n";
            $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->select();
            if($address->isEmpty()){
                $ret['sendText'] .= "\n{$chatText}尚未添加任何监听地址";
            }else{
                $ret['sendText'] .= "\n🧾 <b>{$chatText}已监听的地址列表：</b>\n"; 
                foreach ($address as $value) { 
                    if($value['type'] == 1){
                        $ret["sendText"] .= "\n🔔 "."<code>{$value['address']}</code>";  
                    }else if($value['type'] == 2){
                        $ret["sendText"] .= "\n🌐 "."<code>{$value['address']}</code>";
                    }  
                }
            }
            if(!empty($user['json']['url'])){
                $ret["sendText"] .= "\n\n<blockquote>POST通知地址：{$user['json']['url']}</blockquote>";
            }
             
            $ret['anniu'] = [
                                [
                                    [
                                        "text" =>"⚙️设置监听通知地址POST",
                                        "callback_data" => "设置监听通知地址POST"
                                    ]
                                ],
                                [
                                    [
                                        "text" =>"➕" .trans('新增地址', [], 'usdtjt'),
                                        "callback_data" => "新增监听地址"
                                    ], 
                                    [
                                        "text" =>"⛔️". trans('删除地址', [], 'usdtjt'),
                                        "callback_data" => "删除TRC20监听地址"
                                    ]
                                ]
                                 
                            ]; 
            return $ret; 
        }
        
        if(!empty($message['action'])){
            
            switch ($message['action']) { 
                default:
                    // code...
                    break;
                    
                case '新增TRC20监听地址': 
                    if(!pipei($message['actionText'],"^T\w{33}$")){
                        $ret["sendText"] = "⚠️<b>新增监听钱包地址错误,请重新输入：</b>"; 
                        return $ret; 
                    } 
                    
                    $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                    if(empty($usdtjt_set)){
                       $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                    }
                    $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                    $jtnum = $usdtjt_set['json']['免费监听地址数量'];
                    if($userdz){  
                        $jtnum  =  $usdtjt_set['json']['免费监听地址数量'] + $userdz['num'];
                    } 
                
                    $hexaddress = $GLOBALS['Gate']->call("Gate.TronDecode",$message['actionText']);//地址转换成hex 
                    $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",1)->where("tgid",$message['chatId'])->select();//取得用户的所有监听地址
                    if(count($address) >= $jtnum){
                        $ret['sendText'] = "免费监听地址数量达到上限：{$jtnum} 个";
                        return $ret;   
                    }
                    $address = array_column($address->toArray(), null, 'address');  
         
                    if(isset($address[$message['actionText']])){
                        $ret['sendText'] = trans('你已监听该地址,无需重复添加', [], 'usdtjt');
                        return $ret; 
                    } 
                    $sql['bot']=$message['bot']['API_BOT'];
                    $sql['type']=1;
                    $sql['coin']="TRC";
                    $sql['username']=$message['formUser'];
                    $sql['tgid']=$message['chatId'];
                    $sql['address']=$message['actionText'];
                    $sql['addressHex']=$hexaddress;  
                    $insert = model\bot_address_jt::create($sql);
                    if($insert){
                        Redis::HSETNX("address",$hexaddress,1);
                    }
                    $ret['nextdel']=1;
                    $ret['sendText'] = "<code>{$message['actionText']}</code>\n".trans('添加成功,已监听地址数量：', [], 'usdtjt').count($address) + 1;
                    $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "已监听列表",
                                            "callback_data" => "我的TRC20监听地址"
                                        ],
                                        [
                                            "text" => trans('继续新增地址', [], 'usdtjt'),
                                            "callback_data" => "新增监听地址"
                                        ]
                                    ]
                                ]; 
                    return $ret;   
                    break;
                    
                    
                    
                    
                    
                    
                case '新增TRC20监听地址POST': 
                    if(!pipei($message['actionText'],"^T\w{33}$")){
                        $ret["sendText"] = "⚠️<b>新增监听钱包地址错误,请重新输入：</b>"; 
                        return $ret; 
                    } 
                    
                    $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                     
                    
                    $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                    if(empty($usdtjt_set)){
                       $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                    }
                    $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                    $jtnum = $usdtjt_set['json']['免费监听地址数量'];
                    if($userdz){  
                        $jtnum  =  $usdtjt_set['json']['免费监听地址数量'] + $userdz['num'];
                    } 
                
                    $hexaddress = $GLOBALS['Gate']->call("Gate.TronDecode",$message['actionText']);//地址转换成hex 
                    $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",2)->where("tgid",$message['chatId'])->select();//取得用户的所有监听地址
                    if(count($address) >= $jtnum){
                        $ret['sendText'] = "免费监听地址数量达到上限：{$jtnum} 个";
                        return $ret;   
                    }
                    $address = array_column($address->toArray(), null, 'address');  
         
                    if(isset($address[$message['actionText']])){
                        $ret['sendText'] = trans('你已监听该地址,无需重复添加', [], 'usdtjt');
                        return $ret; 
                    } 
                    $sql['bot']=$message['bot']['API_BOT'];
                    $sql['type']=2;
                    $sql['coin']="TRC";
                    $sql['url']=$user['json']['url'];
                    
                    $sql['username']=$message['formUser'];
                    $sql['tgid']=$message['chatId'];
                    $sql['address']=$message['actionText'];
                    $sql['addressHex']=$hexaddress;  
                    $insert = model\bot_address_jt::create($sql);
                    if($insert){
                        Redis::HSETNX("address",$hexaddress,1);
                    }
                    $ret['nextdel']=1;
                    $ret['sendText'] = "<code>{$message['actionText']}</code>\n".trans('添加成功,已监听地址数量：', [], 'usdtjt').count($address) + 1;
                    $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "已监听列表",
                                            "callback_data" => "我的TRC20监听地址"
                                        ],
                                        [
                                            "text" => trans('继续新增地址', [], 'usdtjt'),
                                            "callback_data" => "新增监听地址"
                                        ]
                                    ]
                                ]; 
                    return $ret;   
                    break;  
                    
                    
                    
                    
                    
                    
                    
                case '删除TRC20监听地址': 
                    if(!pipei($message['actionText'],"^T\w{33}$")){
                        $ret["sendText"] = "⚠️<b>删除监听钱包地址错误,请重新输入：</b>"; 
                        return $ret; 
                    }
                    $del = model\bot_address_jt::where("address",$message['actionText'])->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->delete();
                    $addsy = model\bot_address_jt::where("address",$message['actionText'])->count();
                    if($addsy < 1 ){
                        $hexaddress = $GLOBALS['Gate']->call("Gate.TronDecode",$message['actionText']);
                        Redis::Hdel("address",$hexaddress,1); 
                    } 
                    $ret['nextdel']=1;
                    $ret['sendText'] = "<code>{$message['actionText']}</code>\n".trans('监听地址删除成功', [], 'usdtjt'); 
                    $ret['anniu'] = [
                                [
                                        [
                                            "text" => "已监听列表",
                                            "callback_data" => "我的TRC20监听地址"
                                        ], 
                                    ],
                                [
                                    [
                                        "text" =>"➕" .trans('新增地址', [], 'usdtjt'),
                                        "callback_data" => "新增监听地址"//新增TRC20监听地址
                                    ], 
                                    [
                                        "text" =>"⛔️". trans('删除地址', [], 'usdtjt'),
                                        "callback_data" => "删除TRC20监听地址"
                                    ]
                                ]
                            ]; 
                    return $ret; 
                    break; 
                    
                    
                case '设置监听通知地址POST':    
                    if(!pipei($message['actionText'],"^https?:\/\/")){
                        $ret["sendText"] = "⚠️<b>POST通知地址错误,请输入包含http或https完整地址\n请重新输入：</b>"; 
                        $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "❌ 取消操作",
                                            "callback_data" => "取消操作"
                                        ]
                                    ],
                                ]; 
                        return $ret; 
                    }
                    if(strlen($message['actionText']) > 63){
                        $ret["sendText"] = "⚠️<b>POST通知地址超过64位字符,请重新输入：</b>"; 
                        $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "❌ 取消操作",
                                            "callback_data" => "取消操作"
                                        ]
                                    ],
                                ]; 
                        return $ret;
                    }
                    $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                    
                    $json = $user['json'];
                    
                    $json['url'] =  $message['actionText'];
                    
                    Db::name("bot_account")->where("id",$user['id'])->update(['json'=>json_encode($json)]); 
                    model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",2)->where("tgid",$message['chatId'])->update(['url'=>$message['actionText']]);//取得用户的所有监听地址
                    $ret['nextdel'] = 1;
                    $ret['sendText'] = "<b>✅POST通知地址设置成功！</b>";
                    $ret['sendText'] .= "\n\n<code>当你添加的监听地址设置为POST时,地址发生交易时会给以下地址发送POST通知！</code>\n{$message['actionText']}";
                    return $ret; 
                    break; 
                    
            }
            
        } 
         
          
        return $ret;  
    }
    
     
        
    
 
}