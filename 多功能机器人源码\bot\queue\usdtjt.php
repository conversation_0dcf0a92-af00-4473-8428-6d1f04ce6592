<?php
namespace bot\queue;
use Webman\RedisQueue\Consumer;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列
use Exception;
 

#guzzle 操作
use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

class usdtjt implements Consumer{ 
    public $queue = "queue_usdtjt";  //消费队列名字：queue_文件名  注：投递队列消息时：RQamsg::send("queue_energy","队列消息内容");
    public $connection = "tgbot";   //连接名 固定别修改
    
    #消费代码
    public function consume($data){    
        if(empty($data['url'])){
           var_dump("bot/queue/usdtjt.php 收到队列消费消息 缺少url参数",$data); 
        }
        if(!is_phar()){
            var_dump("bot/queue/usdtjt.php 收到队列消费消息 ",$data); 
            return true; 
        }
         
        $client = new Guzz_Client(['timeout' => 5,'http_errors' => false,'verify' => false]); 
 
        try {
            $response = $client->request('POST', $data['url'], [
                'form_params' => $data
            ]);
            $status_code = $response->getStatusCode();
            if ($status_code == 200) {
                return true;
            } else {
                throw new \Exception("监听地址异步POST通知失败 状态码错误：{$status_code}"); 
            }
        } catch (\Throwable $e) {  
            throw new \Exception("监听地址异步POST通知失败 {$e->getMessage()}");  
        }
       
        
    }
}
