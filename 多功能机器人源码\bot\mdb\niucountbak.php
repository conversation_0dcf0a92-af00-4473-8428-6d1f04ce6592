<?php
namespace bot\mdb;

use think\Model;

class niucountbak extends Model {
    //数据表名
    protected $name = 'niucountbak';
    // 模型数据不区分大小写
    protected $strict = true;
    // 数据转换为驼峰命名
    protected $convertNameToCamel = false;
    //自动时间戳
    protected $autoWriteTimestamp = true; 
    // 设置json类型字段
    protected $json = ['json'];
    // 设置JSON数据返回数组
    protected $jsonAssoc = true;
    
    // public  function getJSON($bot){
    //     $set = $this->where('bot', $bot)->cache("niucount_{$bot}")->find(); 
    //     if(empty($set)){
    //         $set = $this->where('bot', 0)->cache("niucount_0")->find(); 
    //     }  
    //     return $set;
    // }
}