<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class acoin  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * next (不强制回复·但下一条文本消息将作为本次交互所需要的回复内容)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        #下面开始写代码
        if($Coinret = pipei($message["text"],"^1\s?([a-zA-Z]+)$")){
            $coins = strtoupper($Coinret[1]);
            if(strlen($coins) <2){
               return $ret; 
            }
             
            
            try { 
                $coin = $this->getJSON2("https://www.okx.com/priapi/v5/market/mult-tickers?instIds={$coins}-USDT");
                 
                // $price = $coin['data']['c']; 
                // $cnymoney = number_format($cny["data"]["USDT"]*$price, 6, '.', '');  
                
                // $zoushi =number_format($coin['data']['r8']*100, 2, '.', ''); 
                $ret['sendText'] = "<b>{$coins} 实时走势 [okx]</b>";
                
                $ret['sendText'] .="\n\n<code>今日最高： {$coin['data'][0]['high24h']} USDT</code>"; 
                $ret['sendText'] .="\n<code>今日最低： {$coin['data'][0]['low24h']} USDT</code>"; 
                
                
                $zoushi =  round((($coin['data'][0]['bidPx'] - $coin['data'][0]['open24h']) / $coin['data'][0]['open24h']) * 100,2);
                
                $ret["sendText"] .= "\n\n今日涨幅：<b>{$zoushi}%</b>";
                
                $ret["sendText"] .= "\n\n当前币价：＄<b> {$coin['data'][0]['bidPx']}</b>";
                // $ret["sendText"] .= "\n当前价值：￥<b> 0 </b>";
                 
                
                
                 
                return $ret; 
                
                
            } catch (\Exception  $e) { 
                $cny = Redis::get("cnylist");
                if(empty($cny)){
                    $cny = $this->getJSON2("https://www.mexc.com/api/platform/common/coin/price/cny");
                    if(isset($cny["data"]["USDT"])){
                        Redis::setnx("cnylist",serialize($cny),300);
                    }
                }else{
                    $cny = unserialize($cny);
                }
                $coin = $this->getJSON2("https://www.mexc.com/api/platform/spot/market-v2/web/symbol/ticker?symbol={$coins}_USDT");
                if(isset($coin['data']['c'])){
                    $price = $coin['data']['c']; 
                    $cnymoney = number_format($cny["data"]["USDT"]*$price, 6, '.', '');  
                    $zoushi =number_format($coin['data']['r8']*100, 2, '.', ''); 
                    $ret['sendText'] = "<b>{$coins} 实时走势 [mexc]</b>";
                    
                    $ret['sendText'] .="\n\n<code>今日最高： {$coin['data']['h']} USDT</code>"; 
                    $ret['sendText'] .="\n<code>今日最低： {$coin['data']['l']} USDT</code>"; 
                    
                    if($zoushi >0.01){
                        $ret["sendText"] .= "\n\n今日涨幅：<b>＋ {$zoushi}%</b>";
                    }else{
                        $zoushi = str_replace("-","－ ",$zoushi);
                        $ret["sendText"] .= "\n\n今日涨幅：<b>{$zoushi}%</b>";
                    }
                    
                    
                    $ret["sendText"] .= "\n\n当前币价：＄<b> {$price}</b>";
                    $ret["sendText"] .= "\n当前价值：￥<b> {$cnymoney}</b>";  
                    return $ret; 
                }
            } 
            
            
             
   
        }
        
        
        #代码结束
        return $ret;     
    }
    
    
    public function getJSON2($url,$body=[],$headers=[]){
        $client = new Guzz_Client(['timeout' => 6,'http_errors' => false,'verify' => false]);
        try { 
            $response = $client->request('GET', $url, [
              'body' =>  json_encode($body),
              'headers' => $headers,
            ]);
            return json_decode($response->getBody()->getContents(),true);  
        } catch (\Exception  $e) { 
            dump($e->getMessage());
            return null;
        } 
    }
} 
