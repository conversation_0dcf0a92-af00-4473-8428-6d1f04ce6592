-- 表结构：tb_jizhang_set
CREATE TABLE `tb_jizhang_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4;
-- 表数据：tb_jizhang_set
INSERT INTO `tb_jizhang_set` VALUES ('1','0','test','1','<a href=\"http://www.97bot.com\">[ 这是广告文字 ]</a>  <a href=\"https://t.me/9777\">[ 供需频道 ]</a>','{\"qie\": \"1701\", \"date\": 20240819, \"anniu\": [[{\"text\": \"下载账单\", \"type\": \"callback_data\", \"callback_data\": \"下载账单\"}, {\"text\": \"查看详情\", \"type\": \"callback_data\", \"callback_data\": \"入款详情\"}]], \"riqie\": 0, \"btntext\": \"<b>【首先需要把机器人添加到群组】</b>\\n<pre><code class=\'language-记账命令说明\'>\\n设置汇率7.2\\n设置手续费1\\n记账设置(管理发送弹出设置菜单)\\n下载账单(下载完整表格账单)\\n\\n+10000\\n+1000/8.2 (该笔以8.2汇率记账)\\n入款10000\\n入款10000/8.2 (该笔以8.2汇率记账)\\n撤回 (回复原始记账消息进行撤回) \\n\\n【下发默认为USDT货币】\\n-100\\n下发100\\n下发1000R\\n下发1000RMB\\n下发1000人民币\\n</code></pre>\", \"btntips\": 2, \"账单风格\": 1, \"进群提示\": 0, \"开始记账说明\": \"<pre><code class=\'language-记账命令说明\'>\\n设置汇率7.2\\n设置手续费1\\n记账设置(管理发送弹出设置菜单)\\n下载账单(下载完整表格账单)\\n\\n+10000\\n+1000/8.2 (该笔以8.2汇率记账)\\n入款10000\\n入款10000/8.2 (该笔以8.2汇率记账)\\n撤回 (回复原始记账消息进行撤回) \\n\\n【下发默认为USDT货币】\\n-100\\n下发100\\n下发1000R\\n下发1000RMB\\n下发1000人民币\\n</code></pre>\\n<b>🟢 记账功能已启动...</b>\", \"进群提示内容\": \"我是一个可以记账的机器人\"}','1970','1724133647');
