<?php
namespace bot\mdb;

use think\Model;
use think\facade\Cache;                 #缓存

class jizhang_qunset extends Model {
    //数据表名
    // protected $name = '';
    // 模型数据不区分大小写
    protected $strict = true;
    // 数据转换为驼峰命名
    protected $convertNameToCamel = false;
    //自动时间戳
    #protected $autoWriteTimestamp = true; 
    // 设置json类型字段
    protected $json = ['json'];
    // 设置JSON数据返回数组
    protected $jsonAssoc = true; 
    
    public  function getJSON($bot,$qunid){
        #Cache::delete("{$bot}_{$qunid}_jizhang_qunset");
        $set = $this->where('bot', $bot)->where('qunid', $qunid)->cache("{$bot}_{$qunid}_jizhang_qunset")->find();  
        if(empty($set)){
            $date = date("Ymd",time()+86400); 
            $data['rmb'] = 0;
            $data['lei'] = 'bank';
            $data['weitiao'] = 0;
            $data['dangwei'] = 2;
            $data['zt'] = 1;
            $data['huilv'] = 7.2;
            $data['shouxufei'] = 0;
            $data['admin'] = "";
            $data['qie'] = 0;
            $data['time'] = "0000";
            $data['ztxianshi'] = 1;
            $data['ztmoney'] = 0;
            $data['ztvalue'] = 0;
            $set = $this->create(["ref"=>1,"bot"=>$bot,"date"=>$date,"qunid"=>$qunid,"json"=>$data]);  
        }   
        return $set;
    }
    
    public  function setJSON($bot,$qunid){ 
        #Cache::delete("{$bot}_{$qunid}_jizhang_qunset");
        $set = $this->where('bot', $bot)->where('qunid', $qunid)->cache("{$bot}_{$qunid}_jizhang_qunset")->find(); 
        if(empty($set)){
            $date = date("Ymd",time()+86400); 
            $data['rmb'] = 0;
            $data['lei'] = 'bank';
            $data['weitiao'] = 0;
            $data['dangwei'] = 2;
            $data['zt'] = 1;
            $data['huilv'] = 7.2;
            $data['shouxufei'] = 0;
            $data['admin'] = "";
            $data['qie'] = 0;
            $data['time'] = "0000";
            $data['ztxianshi'] = 1;
            $data['ztmoney'] = 0;
            $data['ztvalue'] = 0;
            $set = $this->create(["ref"=>1,"bot"=>$bot,"date"=>$date,"qunid"=>$qunid,"json"=>$data]);  
        }  
        return $set;
    }
     
    
}