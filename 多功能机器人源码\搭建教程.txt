服务器Linux请使用： Debian 11+
务必使用root账户启动机器人，阿里云 腾讯云等等 安装系统后在云面板修改一下root的密码即可开启root账户！

数据库名称，账号，密码 建议都用：tgbot (搭建出现错误请重启服务器，删除数据库，重新建库，再次启动)
 
 
本系统所有功能预览：https://www.97bot.com/tgbot



【搭建图片教程】【搭建图片教程】
【搭建图片教程】【搭建图片教程】
https://www.97bot.com/jiaocheng/140.html
 
后台密码忘记请执行命令：
./97bot repass 97bot 123456

菜单丢失或不全请执行命令：
./97bot cksql load





命令说明：
调试模式启动： ./97bot start
后台模式启动：./97bot start -d
  

【重启项目时建议下面这样操作】
强行暴力终止项目：./97bot kill
然后输入启动命令：./97bot start -d
 
  



更详细的机器人说明：
https://www.97bot.com/code/113.html


超简单 自己开发功能模块 视频教程：
https://www.97bot.com/jiaocheng/114.html

