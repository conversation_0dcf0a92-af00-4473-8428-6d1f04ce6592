
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<!--<link rel="stylesheet" href="/app/keepbot/layui/css/layui.css">-->
		<link rel="stylesheet" href="/assets/css/diy.css">
		<link rel="stylesheet" href="/assets/css/list2.css">
		<link rel="stylesheet" href="/assets/font/iconfont.css">
		<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> 
		<title>网页账单</title>
		<style>
		td{padding:1px 8px;}
		fieldset {border-width: 1px;}
		.layui-table td, .layui-table th{padding: 5px;}  
		</style>
	</head>
	<body style="margin:20px">
	<div id="app">
		<div style="margin:20px 0">
		
		<select v-model="date"  @change="seltab($event)">
			<option value="0">今天</option>
			<option value="<?=htmlspecialchars($zuotian)?>" ><?=htmlspecialchars($zuotian)?></option>
			<option value="<?=htmlspecialchars($qiantian)?>" ><?=htmlspecialchars($qiantian)?></option>			
		</select>
			
			<a :href="'/app/keepbot/telegram/down_inc_csv?qunid='+qunid +'&date='+date"  target="_blank" style="margin-left:20px;color:blue;">下载Excel数据</a>
		</div> 
		<div class="layui-row">
<div class="layui-col-xs12 layui-col-md4">
	<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
		<legend>入款（{{count1}}笔）</legend>
	</fieldset>
	<table class="layui-table maintable" style="white-space:nowrap;">
		<tr>
		 
			<th>时间</th>
			<th>金额</th>
			<th>操作人</th>
			<th>被回复人</th>
		</tr>
		<tr v-for="(item, index) in log" :key="index">
		 
			<td style="margin:0 20px">{{timedate(item.time)}}</td>
			<td>{{ parseInt(item.money)}}</td>
			<td style='color:#999'>{{item.from}}</td>
			<td >{{ tojosn(item.reply) }}</td>
		</tr> 
	</table>
	 
	
	<br />
	<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
		<legend>下发（{{count2}}笔）</legend>
	</fieldset>
	<table class="layui-table maintable" style="white-space:nowrap;">
		<tr> 
			<th>时间</th>
			<th>金额</th>
			<th>操作人</th>
			<th>被回复人</th>
		</tr>
		<tr v-for="(item, index) in logc" :key="index"> 
			<td style="margin:0 20px">{{timedate(item.time)}}</td>
			<td>{{item.usdt}} ({{ Math.round(item.usdt*item.huilv)}})</td>
			<td style='color:#999'>{{item.from}}</td>
			<td >{{ tojosn(item.reply) }}</td>
		</tr>
	</table>
	<br />
	<br>
	<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
		<legend>总计</legend>
	</fieldset>
	<table class="layui-table maintable">
		<tr>
			<td>总入款：</td>
			<td>{{totalz.def}}</td>
		</tr>
		<tr>
			<td>费率：</td>
			<td>{{keep_setup.feilv}}%</td>
		</tr>
		<tr>
			<td>USD汇率：</td>
			<td>{{keep_setup.huilv}}</td>
		</tr>
		
		<template v-if="keep_setup.rmb == 1">
		<tr>
			<td>应下发：</td>
			<td>{{total.incmoney}}｜{{total.incusdt}} USD</td>
		</tr>
		<tr>
			<td>已下发：</td>
			<td>{{total.decmoney}}｜{{total.decusdt}} USD</td>
		</tr>
		<tr>
			<td>未下发：</td>
			<td>{{ parseInt(total.incmoney)  - parseInt(total.decmoney) }}｜{{ parseInt(total.incusdt)  - parseInt(total.decusdt) }} USD</td>
		</tr>
		</template>
		
		<template v-else>
		<tr>
			<td>应下发：</td>
			<td>{{totalz.usdt}} USD</td>
		</tr>
		<tr>
			<td>已下发：</td>
			<td>{{totalz.decusdt}} USD</td>
		</tr>
		<tr>
			<td>未下发：</td>
			<td >{{ parseInt(totalz.usdt)  - parseInt(totalz.decusdt) }} USD</td>
		</tr>
		</template> 
		
	</table>
	
	 
	<br />
	
	
	<div class="layui-collapse">
			<h2 class="layui-colla-title" style="padding: 0 15px 0 6px;">入款操作人统计</h2>
			<div class="layui-colla-content layui-show" style="padding: 0px;">
				<table class="layui-table maintable" style="margin: 0px;">
					<tr>
						<th>操作人</th>
						<th>笔数</th>
						<th>金额</th>
						<th>USD ({{keep_setup.huilv}})</th>
					</tr>
					<tr  v-for="(item, index) in userl" :key="index">
						<td>{{ item.username}}</td>
						<td>{{ item.num1 }}</td>
						<td>{{ item.money1 }}</td>
						<td>{{ (item.money1 / keep_setup.huilv).toFixed(2) }}</td>
					</tr>
				</table>
			</div>
 
	</div>
	
	<br />
	
	<div class="layui-collapse">
			<h2 class="layui-colla-title" style="padding: 0 15px 0 6px;">回复入款操作人统计</h2>
			<div class="layui-colla-content layui-show" style="padding: 0px;">
				<table class="layui-table maintable" style="margin: 0px;">
					<tr>
						<th>操作人</th>
						<th>笔数</th>
						<th>金额</th>
						<th>USD ({{keep_setup.huilv}})</th>
					</tr>
					<tr  v-for="(item, index) in userr" :key="index">
						<td>{{ item.username}}</td>
						<td>{{ item.num1 }}</td>
						<td>{{ item.money1 }}</td>
						<td>{{ (item.money1 / keep_setup.huilv).toFixed(2) }}</td>
					</tr>
				</table>
			</div>
 
	</div>
	
	
	
	<br />
 



	
	
	
    </div>
  </div> 	
</div>

<!--系统js 你可以把js放到：/bot/res/xxx/目录下然后在下引用 引用时只需要：/xxx/你的.js-->
<script src="/assets/js/vue.js"></script>  
<script src="/assets/js/axios.js"></script> 
<script src="/assets/js/clipboard.js"></script> 
 

<!--第三方js 如果你要加载其它js请填写到下面-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>  

<script> 
var app = new Vue({
  el: '#app',
  data: {
    message: 'Hello Vue!',
    log:[],
    logc:[],
    keep_setup:{},
    total:{def:0,incmoney:0,decmoney:0,incusdt:0,decusdt:0},
    totalz:{def:0,money:0,decmoney:0,usdt:0,decusdt:0},
    userl:[],
    userr:[],
    count1:0,
    count2:0,
    qunid:0,
    date:0, 
    
    
  },
  mounted() { 
      this.load()   
  },
   methods: {
       load(){
          this.qunid = this.getid("qunid");
          
          axios.get('?qunid='+ this.qunid +'&limit=10&page=1&date=' +this.date ).then(res => {
              if(res.data.data.keep_setup){
                  this.keep_setup = res.data.data.keep_setup 
              }
              
              if(res.data.data.totalz ){ 
                  this.totalz = res.data.data.totalz 
              }
              
              if(res.data.data.total ){ 
                  this.total = res.data.data.total 
              }
            
          }).catch(function (error) {
                console.log(error);
          });
          
          axios.get('?qunid='+ this.qunid +'&limit=10&page=1&date=' +this.date).then(res => {
              if(res.data.data.list){
                this.log = res.data.data.list; 
                this.count1 = res.data.data.count;
              }
            
          }).catch(function (error) {
                console.log(error);
          });
          
          axios.get('?qunid='+ this.qunid +'&limit=10&page=1&date=' +this.date).then(res => {
               if(res.data.data.list){
                this.logc = res.data.data.list; 
                this.count2 = res.data.data.count;
               }
            
          }).catch(function (error) {
                console.log(error);
          });
          
          axios.get('?qunid='+ this.qunid +'&limit=10&page=1&date=' +this.date).then(res => {
               if(res.data.data.list){
                this.userl = res.data.data.list; 
                this.userr = res.data.data.list2; 
               }
            
          }).catch(function (error) {
                console.log(error);
          });
           
       }, 
        timedate(time) {
            var now = new Date(parseInt(time) * 1000)
            let y = now.getFullYear()
            let m = now.getMonth() + 1
            let d = now.getDate()
            return now.toTimeString().substr(0, 8)
        },
        tojosn(json){
           let a =  JSON.parse(json);
           if(!a || !a.first_name){
             return '';  
           }else{
             return a.first_name;
           }
        },
        getid(name){ 
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]); return null; 
        },
        seltab(){ 
            this.load()  
            
        }
       
   }
})
		</script>
	 
	</body>
</html>