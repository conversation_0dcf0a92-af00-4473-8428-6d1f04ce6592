<?php
namespace bot\_info; 

 
class autobot  {  
    public function index(){    
         
        $info['core']       = 240310;
        
        $info['lv']         = 888;
           
        $info['key']        = "autobot";
         
        $info['name']       = "托管(克隆)机器人";
        
        $info['text']       = "用户发送他自己的机器人<span class='hong cu'>Token</span>,自动托管克隆上级机器人,<span class='hong miaobian'>支持设置收费托管</span>"; 
         
        $info['version']    = "2.2";
        
        $info['new']        = "支持机器人自助功能开通<br>如果需要这个功能部分模块可能需要卸载重装";
        
        $info['menuId']     = [230, 231];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["bot1.png", "bot2.png"]; 
           
        $info['need']       = ["money"];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "KUwSttyDoe+jMvnZLLAkwgUArccsuoRC+0W1yx3Bawhd1V5NJ6unKFj3MdQ/VzufX8Od2kjTO87a59MeEqMx/04eGOmX0desLEReQ3e1OK6Zh+pOIX17xhBM5NwWiiMzDWmlN/uOVuj8bQV5FX55rQomLWFhFFMhZepKtpQEriw=";
 
        $info['tables']     = ["autobot_set", "bot_list_tuoguan"];
        
        $info['btn']       = "🤖我的机器人";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      