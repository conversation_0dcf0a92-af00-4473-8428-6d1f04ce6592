.eles-body {
    padding: 6px 12px 6px 6px;
} 

.el-card__body {
padding: 6px 8px !important;
}
 

.ele-table-tool {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 10px;
}
.ele-table-tool .ele-table-tool-title {
    flex: auto;
    margin-top: 5px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}
.ele-table-tool .ele-tool {
    margin: 5px 0 5px auto;
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap;
}
.ele-space {
    display: inline-flex;
    align-items: center;
    flex-wrap: wrap;
}


.el-button + .el-link, .el-button + .el-dropdown, .el-button + .ele-action, .el-link + .el-button, .el-link + .el-link, .el-link + .el-dropdown, .el-link + .ele-action, .el-dropdown + .el-button, .el-dropdown + .el-link, .el-dropdown + .el-dropdown, .el-dropdown + .ele-action, .ele-action + .el-button, .ele-action + .el-link, .ele-action + .el-dropdown, .ele-action + .ele-action, .el-tag + .el-tag {
    margin-left: 10px;
}
.ele-btn-icon.el-button--mini, .ele-btn-icon.is-round.el-button--mini {
    padding-left: 8px;
    padding-right: 8px;
}
.ele-btn-icon, .ele-btn-icon.is-round {
    padding-left: 12px;
    padding-right: 12px;
}

.el-input-group {
    line-height: normal;
    display: inline-table;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.ele-body .el-pagination {
    margin: 20px 0 5px 0;
}

.el-pagination::before, .el-pagination::after {
    display: table;
    content: "";
}

.el-pagination.ele-pagination-circle .el-pager li:not(.active), .el-pagination.ele-pagination-circle .btn-prev:not(.active), .el-pagination.ele-pagination-circle .btn-next:not(.active) {
    background: none;
}
.el-pagination.is-background .btn-prev:disabled, .el-pagination.is-background .btn-next:disabled {
    color: #d2d2d2;
}
.el-pagination.ele-pagination-circle .el-pager li, .el-pagination.ele-pagination-circle .btn-prev, .el-pagination.ele-pagination-circle .btn-next {
    border-radius: 50%;
    min-width: 28px;
    margin-left: 0;
    margin-right: 0;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #1890ff;
    color: #1890ff;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #1890ff;
}

.ele-pagination-circle{
    white-space: unset !important;
}

 
   .border {
    font-size: 13px;
    border: 1px solid;
    padding: 0px 2px;
    font-weight: 800;
  }

   .overflow {
    overflow-x: auto;
  }

   .shenglue {
    display: inline-block;
    /* width: 180px; */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    direction: rtl;

  }

   .deani {
    /* width: min-content; */
    height: 13px;
    position: relative;
    float: right;
    text-align: center;
    font-size: 10px;
    font-weight: unset;
    /* color: #6d4af9; */
    padding: 2px 1px;
    margin-left: 6px;
    border: 1px solid;
    line-height: 0.9rem;
    -webkit-text-size-adjust: none;
  }

   .bt-content {
    display: block;
    -webkit-text-size-adjust: none;
    white-space: nowrap;
  }


 

   .el-pagination__editor.el-input .el-input__inner,
   .el-pagination.ele-pagination-circle .el-pager li {
    border-radius: 2px;
    min-width: 22px;
    height: 22px;
  }

  /*  .el-input--medium .el-input__inner,
   .el-pager li {
    line-height: 24px;
  } */


  /* 全局圆角 直角设定 */
  /*  .el-card,
   .el-select-dropdown,
   .el-input,
   .el-input__inner,

   .el-tag,
   .el-button,
   .el-button--mini,
   .el-radio-button,

   .el-input-group__append,
   .el-input-group__prepend,
   .ele-admin-tab-card .ele-admin-tabs .el-tabs__item,
   .el-radio-button:last-child .el-radio-button__inner,
   .el-radio-button:first-child:last-child .el-radio-button__inner,
   .el-radio-button:first-child .el-radio-button__inner {
    border-radius: 0;
  } */

  /* ---------------------------------------------------- */

   .ele-table-tool .ele-table-tool-title {
    font-weight: lighter;
  }

   .el-link [class*=el-icon-]+span {
    margin-left: 2px;
  }



  table {
    width: 100%;
  }

   .table {
    margin: 0px 0;
    border-collapse: collapse;
    border-spacing: 0;
    empty-cells: show;
    border: 1px solid #e0e0e0;
  }

   .table caption {
    color: #333;
    font-style: italic;
    font-size: 85%;
    line-height: 1;
    padding: 1em 0;
    text-align: center;
  }

  table tr,
  table th,
  table td {
    border: none;
    border-bottom: 1px solid #e4ebeb;
    /* font-family: 'Lato', sans-serif; */
    font-size: .875rem;
  }


   table tbody tr:hover {
    background: linear-gradient(to right, #eafbed4d, var(--color-primary-1),
        #fff);
  }



  /* .table tr:active{
  background: linear-gradient(to right,#fff,#ffff0010,#ffff0010);
} */


  /*  .overflow table th {
    background: #fafafa;
 
    text-transform: uppercase;
  } */

  table tr td {
    color: #999999;
  }




  /* 表格边框 竖线 */
   .table td,
   .table th {
    border-left: 1px solid #ededed;
    border-width: 0 0 0 1px;
    font-size: inherit;
    margin: 0;
    overflow: visible;
    /* padding: 0.5em 1em; */
    padding: 1em 12px;
    /* font-weight: normal; */
  }

  /* 表格边框 横线 */
   .table tr,
   .table th,
   .table td {
    border-bottom: 1px solid #ededed;
  }

   .table td:first-child,
   .table th:first-child {
    border-left-width: 0;
  }

  /* 表格表头文字背景 + 颜色 */
   .table thead,
   .table tfoot {
    background-color: #fafafa;
    color: #303133;
    text-align: left;
    vertical-align: bottom;
  }

  /*  .table thead td,
   .table tfoot td,
   .table thead th,
   .table tfoot th {
    border-color: #fff;
  } */





   .popper_wrap {
    /* 背景色 */
    background: #ebffdb !important;
    color: rgb(252, 58, 74) !important;
    border: 1px solid rgb(185, 185, 185) !important;
  }

  .popper_wraps {
    /* 背景色 */
    background: #5c7080 !important;
    color: #eeee4d !important;
    border: 1px solid #fff !important;
  }


   .itemhong .el-form-item__label {
    color: red;
  }





  /* .el-switch__label--left:after{
  content: "";
  position: absolute;
  top: 3px;
  right: 55px;
  border-radius: 2px;
  transition: all 0.3s;
  width: 14px;
  height: 14px;
  background-color: #afaeae;
}

.el-switch__label--right:after{
  content: "";
  position: absolute;
  top: 2px;
  left: 28px;
  border-radius: 2px;
  transition: all 0.3s;
  width: 16px;
  height: 16px;
  background-color: #ffffff;
}

.el-switch__core:after { 
    display: none; 
    width: 0px;
    transition: all 0.3s;
    height: 0px; 
} */



  @media screen and (max-width: 1079px) {

    /*-----------------------1080响应式---------------------------*/
     .ele-body,
     .el-card__body {
      padding: 0;
    }

     .ele-table-tool {
      display: block;
      /* padding:2px 12px; */
      margin-bottom: 1px;
      text-align: center;
      margin-top: 10px;
    }

     .ele-table-tool .ele-tool {
      margin: 0;
      /* float: right; */
      padding: 1px 0px;
    }

     .el-pagination {
      padding: 10px 0;
    }

     .ele-admin-tabs .el-tabs__item {
      padding: 0 8px !important;
    }

     .el-radio-button,
     .el-radio-button__inner:hover {
      color: #00000000;

    }

     .el-button,
     .el-button--mini,
     .el-input-group__append,
     .el-input-group__prepend,
     .ele-admin-tab-card .ele-admin-tabs .el-tabs__item,
     .el-radio-button:last-child .el-radio-button__inner,
     .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 0;
    }

     .ele-pagination-circle .el-select>.el-input {
      display: none;
    }

    /* 响应式自定义css */

     .mobile-zuo {
      float: right;
      margin-right: 10px;
    }

    /* 下面是表格有关响应式 */

     .table {
      border: 0 solid #e0e0e0;
    }

     .table th {
      display: none;
    }

    /* 移动端表格宽度 */
     .table td::before {
      content: attr(data-th);
      display: inline-block;
      -webkit-flex-shrink: 0;
      -ms-flex-shrink: 0;
      flex-shrink: 0;
      /* font-weight:bold; */
      font-weight: 100;
      width: 68px;
    }

     .table tbody td {
      border: 0;
      display: block;
      display: -webkit-box;
      display: -webkit-flex;
      display: -ms-flexbox;
      display: flex;
      /* vertical-align:top; */
      float: left;
      width: 100%;
      padding: 0;
      font-size: .8125rem;
    }

     .table tbody td:before {
      background: #56a2cf;
      color: white;
      margin-right: 8px;
      padding: 10px 10px;
      /* text-align: right; */
    }

     .table tbody td .bt-content {
      vertical-align: top;
      display: inline-block;
      padding: 10px 0 0 0;
      white-space: unset;

    }


     .table-wrapper.active {
      max-height: 310px;
      overflow: auto;
      -webkit-overflow-scrolling: touch
    }

  }





  /*指定尺寸以下隐藏*/


  @media screen and (max-width: 1000px) {
     .hidden-1100-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }

  }

  @media screen and (max-width: 1200px) {
     .hidden-1200-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1300px) {
     .hidden-1300-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1400px) {
     .hidden-1400-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1500px) {
     .hidden-1500-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1600px) {
     .hidden-1600-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1700px) {
     .hidden-1700-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1800px) {
     .hidden-1800-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }

  @media screen and (max-width: 1900px) {
     .hidden-1900-down {
      display: none;
      content-visibility: hidden;
      position: absolute;
      top: -9999px;
      left: -99999px;
    }
  }