<?php
namespace bot\_info; 

 
class z0usdt  {  
    public function index(){    
         
        $info['core']       = 240331;
        
        $info['lv']         = 70;
           
        $info['key']        = "z0usdt";
         
        $info['name']       = "z0交易所U汇率查询";
        
        $info['text']       = "群内或私聊发送<span class='hong cu url'>w0</span> , <span class='hong cu url'>k0</span> , <span class='hong cu url'>z0</span>查询交易所USDT实时汇率(数字大于0则计算对应价值)"; 
         
        $info['version']    = "2.2";
        
        $info['new']        = "增加备用查询币价线路<br>解决apikey不对或未配置时出现的错误问题";
        
        $info['menuId']     = [0];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["z01.png", "z02.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "TMfkeYTRLOSZrHzVYJangKUykzFP4KjdfiPic8FCKZq5Xki96sdYDpCZVAgnKAcxECtHrvlaNSuwm5GjnMnUrJFCuLQJ3t+DS7hgp8XJdUX87wk7B1bQDJ33QlNPh28CDJQ50t00OWO8MISSWNs221GRwxHf5kzVE9ykPmv5P3k=";
 
        $info['tables']     = [];
        
        $info['btn']       = "👁查Ｕ汇率";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      