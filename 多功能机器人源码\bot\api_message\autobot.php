<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class autobot  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $ret支持回调参数：sendText(发送文字讯息) sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件) huifu=1(强制用户回复) huifuTips(回复提示文字)   anniu(消息按钮)   [ jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)] back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        
        #下面开始写代码
        
        //你定义了启动按钮：🤖我的机器 请在下面写消息触发事件
        if($message["text"] == "🤖我的机器人"){
            $tuoguan = Db::name("bot_list_tuoguan")->where("upbot",$message['bot']['API_BOT'])->where("myadmin",$message['formId'])->select();
            $botl = [];
            if($tuoguan->isEmpty()){
                $ret["sendText"] = "<b>你似乎没有克隆机器人哦</b>\n\n<pre><code class='language-机器人托管说明：'>你可以私信把你的机器人Token发送给我,只需3秒钟自动为你创造一个和我一样功能的机器人！</code></pre>";
            }else{
                $ret['sendPhoto'] = run_path()."/bot/res/autobot/auto.jpg";
                $ret["sendText"] = "<b>你托管的机器人数量</b>：<b>".count($tuoguan)."</b>"; 
                $ret["sendText"] .= "\n\n<blockquote><u>点击对应机器人可进行管理,续费,增加功能,删除托管等等</u></blockquote>";
                foreach ($tuoguan as $bot) {
                    $a["text"] = "@{$bot['mybot']}";
                    $a["callback_data"] = "mybot_{$bot['mybot']}"; 
                    array_push($botl,[$a]); 
                }
            } 
            $ret['nodel'] = 1;
            $ret["anniu"] = $botl; 
            return $ret;  
        }else if($message["chatType"] == "private" && pipei($message["text"],"^[0-9]{10}:.{35}$")){
            $code = tuoguan($message["text"],$message["formId"],$message["bot"]['API_BOT'],$message["bot"]['Admin']);
            $ret["sendText"] = $code['msg']??"未知错误";
            return $ret; 
        }else if($message["formId"] == $message["bot"]['Admin']){
            if(pipei($message["text"],"^设置启动消息")){
                Cache::delete("tgbot_{$message["bot"]['API_BOT']}"); //删除缓存
                $fenge = explode("设置启动消息",$message["text"]);
                if(!empty($fenge[1])){
                    Db::name("bot_list")->where("API_BOT",$message["bot"]['API_BOT'])->update(["start"=>$fenge[1]]); 
                    $ret["sendText"] = "<b>恭喜您,设置启动消息成功</b>✅\n你可以尝试发送：/start 命令进行测试！";
                }else{
                    $ret["sendText"] = "<b>设置启动消息是啊比</b>\n缺少消息内容！";
                } 
            }
            return $ret; 
        }
        
        

        
        
        
        #代码结束
        return $ret;     
    }
} 
