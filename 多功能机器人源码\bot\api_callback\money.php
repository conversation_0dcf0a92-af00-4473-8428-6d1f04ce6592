<?php
namespace bot\api_callback;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
 
class money  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["callId"]       =   点击唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $message["text"]         =   消息文字内容
     * $message["time"]         =   消息到达-服务器时间戳
     * $message["msgTime"]      =   消息发布时间戳  电报官方时间
     * $message["editTime"]     =   消息最后编辑时间戳 0未编辑过
     * 
     * $message["btnData"]      =   消息按钮对应 消息事件
     * 
     * $message["gamaId"]       =   游戏标识ID   
     * $message["gameName"]     =   游戏唯一标识 游戏才有
     
     * $message["photo"]        =   点击按钮时消息内容中-如果有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   点击按钮时消息内容中-有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   点击按钮时消息内容中-有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   点击按钮时消息内容中-有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     *  ■■ $ret ■■ 返回参数说明：
     *  alert (最高优先级 - 下面参数无效) back=0 代表编辑按钮时禁止增加返回按钮
     *  delMessage=1 代表点击按钮后删除原消息 huifu=1 强制用户回复 huifuTips 回复占位符提示文字
     *  jianpan(键盘按钮) + jianpanText(文本消息) 或 jianpanPhoto(发送照片) （第2优先级 - 下面参数无效）
     *  sendText(发送文本消息),sendPhoto(发送照片),sendVideo(发送视频),sendFile(发送文件)  ||  editText(编辑消息内容) ||  anniu(消息按钮)
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){ 
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        #------------------------以上代码默认请勿修改 level 视情况调整改动---------------------   
        switch ($message['btnData']) {
            default:  
                break;
                
            case '账户余额':
                $user = Db::name("bot_account")->where("del",0)->where("bot",$message['bot']['API_BOT'])->where("tgid",$message["formId"])->find();
                if(empty($user)){
                   $ret["editText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
                   $ret["anniu"] = [ 
                            [
                                [
                                    "text" => "🔴 点击启用机器人",
                                    "url" => "https://t.me/{$message['bot']['API_BOT']}"
                                ]
                            ],
                        ];
                   return $ret;  
                }
                $ret["editText"] = "👤 用  户  名：@{$message["formUser"]}\n";
                $ret["editText"] .= "💠 电报ＩＤ：<code>{$message["formId"]}</code>\n";
                $ret["editText"] .= "🗓 注册日期：<code>".date("Y-m-d H:i:s",$user['create_time'])."</code>\n\n";
                $ret["editText"] .= "T R X 余额：<b>{$user['trx']}</b>\n";
                $ret["editText"] .= "USDT余额：<b>{$user['usdt']}</b>\n";
                $ret["anniu"] = [
                            [
                                [
                                    "text" => "💰余额充值",
                                    "callback_data" => "余额充值"
                                ],
                                [
                                    "text" => "📊流水记录",
                                    "callback_data" => "流水记录|1"
                                ]
                            ], 
                            [
                                [
                                    "text" => "♻️余额转账",
                                    "callback_data" => "余额转账"
                                ],
                                [
                                    "text" => "🧳余额提现",
                                    "callback_data" => "余额提现"
                                ]
                            ],
                            
                        ];         
                return $ret;  
                break;
                
                
                
            case '余额充值':  
                $set = model\money_set::where('bot',$message['bot']['API_BOT'])->find();   
                if(empty($set)){
                    $set = model\money_set::where('bot',0)->find();
                }
                if(empty($set['json']['充值地址'])){
                    $ret['alert'] = "充值功能未开启\n管理员没有设置收款地址！";
                   return $ret;
                }
                
                #检查一次充值地址是否被监听
                $pays = Db::name("bot_address_jt")->where("address",$set['json']['充值地址'])->where("type",3)->where("bot",$set['value'])->find();  
                #更新给监听 
                if(empty($pays)){
                    $pays['bot'] = $message['bot']['API_BOT'];
                    $pays['username'] = $message['bot']['AdminName'];
                    $pays['tgid'] = $message['bot']['Admin'];
                    $pays['type'] = 3;
                    $pays['coin'] = "TRC";
                    $pays['address'] = $set['json']['充值地址'];
                    $pays['addressHex'] = $GLOBALS['Gate']->call("Gate.TronDecode",$pays['address']);
                    $pays['create_time'] = time();
                    Db::name("bot_address_jt")->insert($pays);
                    
                    $redisI = Redis::hget("address",$pays['addressHex']);
                    if(empty($redisI)){
                        Redis::hset("address",$pays['addressHex'],1);
                    } 
                } 
                
                 
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->where("del",0)->find(); 
                if(empty($user)){  
                   $ret["editText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
                   $ret["anniu"] = [ 
                        [
                            [
                                "text" => "🔴 点击启用机器人",
                                "url" => "https://t.me/{$message['bot']['API_BOT']}"
                            ]
                        ],
                    ];
                   return $ret;
                }
                if($message["chatType"] != "private"){
                   $ret['alert'] = "请私聊机器人充值余额";
                   return $ret; 
                }
                
                $ret['editText'] = "<code>💰你正在进行充值...</code>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择充值余额类型：</b>";
                $ret['anniu'] = [
                            [
                                [
                                    "text" => "充值TRX",
                                    "callback_data" => "充值余额TRX"
                                ],
                                [
                                    "text" => "充值USDT",
                                    "callback_data" => "充值余额USDT"
                                ]
                            ],
                        ]; 
                
                return $ret;
                break;
            
            case '充值余额TRX':
                $ret['editText'] = "<code>💰你正在进行充值...</code>\n\n充值货币：<b>TRX\n\n请输入充值数量：</b>";
                $ret['next'] = "充值余额TRX"; 
                $ret['back'] = 0;
                return $ret;
                break;
                
            case '充值余额USDT':
                $ret['editText'] = "<code>💰你正在进行充值...</code>\n\n充值货币：<b>USDT\n\n请输入充值数量：</b>";
                $ret['next'] = "充值余额USDT"; 
                $ret['back'] = 0;
                return $ret;    
                break;
                
                
                
            case '余额转账': 
                if($message["chatType"] != "private"){
                   $ret['alert'] = "请私聊机器人使用转账功能";
                   return $ret; 
                }
                
                $set = model\money_set::where('bot',0)->find(); 
                if(empty($set['json']['允许转账'])){
                    $ret['alert'] = "系统当前禁止转账";
                    return $ret; 
                }
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                
                if($user['trx']<$set['json']["最低转账TRX"] &&  $user['usdt']<$set['json']["最低转账USDT"]   ){
                   $ret['alert'] = "你的账户没有可用的转账资金";
                   return $ret;  
                }
                
                $ret['editText'] = "<code>🔂你正在进行转账...</code>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择要转账的货币类型：</b>"; 
                 
                        
                $bbb = [];
                if($user['trx'] > $set['json']["最低转账TRX"]){
                    $btn['text'] = "TRX转账";
                    $btn['callback_data'] = "TRX转账";
                    $bbb[] = $btn;
                }
                if($user['usdt'] > $set['json']["最低转账USDT"]){
                    $btn['text'] = "USDT转账";
                    $btn['callback_data'] = "USDT转账";
                    $bbb[] = $btn;
                } 
                if(count($bbb)>0){
                    $ret['anniu'][] = $bbb;   
                }  
                return $ret;   
                break; 
                
            case  'TRX转账':   
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                $ret['editText'] = "<code>🔂你正在进行转账...</code>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请输入要转出的TRX数量：</b>"; 
                $ret['next'] = "转出TRX"; 
                return $ret;   
                break; 
            
            case  'USDT转账':   
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                $ret['editText'] = "<code>🔂你正在进行转账...</code>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请输入要转出的USDT数量：</b>"; 
                $ret['next'] = "转出USDT"; 
                return $ret;   
                break;     
                
            
            case '余额提现': 
                if($message["chatType"] != "private"){
                   $ret['alert'] = "请私聊机器人使用提现功能";
                   return $ret; 
                }
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                if($user['trx']<0.5 &&  $user['usdt']<0.5    ){
                   $ret['alert'] = "你的账户可提现资金不足";
                   return $ret;  
                }
                $ret['editText'] = "<b>你的可提现余额：</b>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择提现货币：</b>"; 
                $bbb = [];
                if($user['trx'] > 0.5){
                    $btn['text'] = "提现TRX";
                    $btn['callback_data'] = "TRX余额提现";
                    $bbb[] = $btn;
                }
                if($user['usdt'] > 0.5){
                    $btn['text'] = "提现USDT";
                    $btn['callback_data'] = "USDT余额提现";
                    $bbb[] = $btn;
                } 
                if(count($bbb)>0){
                    $ret['anniu'][] = $bbb;   
                }  
                return $ret;  
                 
                
                break; 
                
            case  'TRX余额提现':
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
                $set = model\money_set::where('bot',0)->find(); 
                if(empty($set['json']['允许提现'])){
                    $ret['alert'] = "当前不允许提现";
                    return $ret;
                }else if($user['trx'] < $set['json']['最低提现TRX']){
                    $ret['editText'] = "账户余额不足\n最低TRX提现数量：".$set['json']['最低提现TRX'];
                    return $ret;
                } 
                $ret['editText'] = "<b>你的可提现余额：</b>\n\nT R X：<b>{$user['trx']}</b>\n\n<code>请输入你要提现的数量：</code>";
                $ret['next'] = "余额提现TRX"; 
                $ret['back'] = 0; 
                return $ret;
                break;
                
            case  'USDT余额提现':
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
                $set = model\money_set::where('bot',0)->find(); 
                if(empty($set['json']['允许提现'])){
                    $ret['alert'] = "当前不允许提现";
                    return $ret;
                    break;
                }else if($user['usdt'] < $set['json']['最低提现USDT']){
                    $ret['editText'] = "账户余额不足\n最低USDT提现数量：".$set['json']['最低提现USDT'];
                    return $ret;
                    break;
                } 
                $ret['editText'] = "<b>你的可提现余额：</b>\n\nUSDT：<b>{$user['usdt']}</b>\n\n<code>请输入你要提现的数量：</code>";
                $ret['next'] = "余额提现USDT"; 
                $ret['back'] = 0;     
                return $ret;
                break;
                
                
            case '设置充值地址':  
                if($message['formId'] == $message['bot']['Admin']){ 
                    $ret['editText'] = "<b>尊敬的管理员你好,你正在设置本机器人的收款钱包地址。</b>\n\n<pre>所有使用本机器人的用户在充值余额时将展示你的钱包地址,用户转账后自动完成充值到账</pre>\n\n<code>请输入你的TRON钱包地址：</code>";
                    $ret['next'] = "设置充值地址"; 
                    $ret['back'] = 0;     
                    return $ret;
                }
                break;
           
                    
                
                
        }  
        
        if(pipei($message['btnData'],"^流水记录")){
            $where = explode("|",$message['btnData']);
            if(empty($where[1])){
                $p = 1; 
            }else{
                $p = $where[1];  
            } 
            $limit = 10;
            $page = ($p-1)*$limit;
            $so =[];  
            array_push($so,"bot");
            array_push($so,'=');
            array_push($so,$message['bot']['API_BOT']); 
            array_push($so,"tgid");
            array_push($so,'=');
            array_push($so,$message['formId']); 
            array_push($so,"zt");
            array_push($so,'=');
            array_push($so,1); 
            $so = array_chunk($so,3);//拆分 
            $count = Db::name("moneylog")->where([$so])->count(); 
            $list = Db::name("moneylog")->where([$so])->limit($page,$limit)->order('id desc')->select();
            $ret['editText'] = "<b>记录总数：{$count}</b>  「<code>第{$p}页</code>」\n";
            $ret['anniu'] = []; 
            foreach ($list as $value) {
                //1充值 2提现 3转账 4消费 5收款 6发红包 
                $type[1]="充值";
                $type[2]="提现";
                $type[3]="转账";
                $type[4]="消费";  
                $type[5]="收款";   
                $type[6]="红包";   
                if($value['money'] > 0){
                  $value['money'] = "%2b".$value['money'];  
                }
                $ret['editText'].= "\n<code>".date("m-d H:i:s",$value['create_time'])." {$type[$value['type']]} {$value['money']}</code>";
            } 
             
            if($p > 1){ 
                $ret['back'] = 0;
                $x[] = ["text" => "上一页","callback_data" => "流水记录|".($p-1)."|back"];
            } 
            
            if($count > $page + $limit ){
                $x[] = ["text" => "下一页","callback_data" => "流水记录|".($p+1)]; 
            }  
            if(!empty($x)){    
                array_push($ret['anniu'],$x);
            }  
            if(!empty($where[2])){  
                $ret['back'] = 0;
                if($p == 1){
                    $backBtn = [
                        [
                            [
                                "text" => trans('返回'),
                                "callback_data" => "返回上一步"
                            ],
                            [
                                "text" => trans('删除消息'),
                                "callback_data" => "删除消息"
                            ]
                        ]
                    ]; 
                    
                    $ret['anniu']  = array_merge($ret['anniu'],$backBtn); 
                }
                
            }
            return $ret;        
        }
        
        if(pipei($message['btnData'],"^余额转账类型")){
            $fg = explode("|",$message['btnData']);
            $u = str_replace("请选择转账类型：", "转账类型：<b>{$fg[1]}</b>\n\n<b>请输入转账数量：</b>", $message['text']);  
            $ret['delMessage'] = 1;
            $ret['sendText'] = $u;
            $ret['huifu'] = 1;
            $ret['huifuTips'] = "输入转账{$fg[1]}数量";
            return $ret; 
        }
        
        if(pipei($message['btnData'],"^充值订单查询")){
            $fg = explode("|",$message['btnData']);
            $paylist = Db::name("pay_list")->where("id",$fg[1])->find();  
            if($paylist['zt'] == 1){  
                $ret['delMessage'] = 1;
                $ret['sendText'] = "<b>恭喜您,余额充值成功</b>✅";
                $ret['sendText'] .= "\n\n订单编号：<b>{$paylist['id']}</b>";
                $ret['sendText'] .= "\n充值账户：@{$paylist['user']}";
                $ret['sendText'] .= "\n充值货币：<b>{$paylist['coin']}</b>";
                $ret['sendText'] .= "\n充值数量：<b>{$paylist['value']}</b>";
                $ret["anniu"] = [
                            [
                                 
                                [
                                    "text" => "📊查看流水",
                                    "callback_data" => "流水记录"
                                ],
                                [
                                    "text" => "😼继续充值",
                                    "callback_data" => "余额充值"
                                ]
                            ]
                            
                        ]; 
                return $ret; 
            
            }else if($paylist['zt'] == 0){
                $ret['alert'] = "订单未支付\n请尽快支付";
                return $ret;
            }else if($paylist['zt'] == -1){
                $ret['delMessage'] = 1;
                $ret['alert'] = "订单已关闭,请勿支付";
                return $ret;
            }
            
        }
        
        
 
 
        return $ret;
    }
}
