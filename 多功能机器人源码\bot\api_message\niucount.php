<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

use app\service\Charts;


class niucount  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * next (不强制回复·但下一条文本消息将作为本次交互所需要的回复内容)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        #下面开始写代码
        if($message['chatType'] != 'supergroup'){
            return $ret;
        }
         
        
        if($message["text"] == "清空统计"){
            $tongji = Redis::hget("{$message["chatId"]}niuniu","tongji"); 
            Redis::del("{$message["chatId"]}niuniu");
            Redis::hset("{$message["chatId"]}niuniu","tongji",$tongji);
            
            mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->delete();
            $ret['sendText'] = "<b>牛牛统计数据清理完成</b>";
            $ret['sendText'] .= "\n\n<b>下注次数：0</b>\n<b>口数输赢：<u>0</u></b>\n<b>总共输赢：<u>0</u></b>";
            return $ret;
        }elseif($message["text"] == "清空所有"){ 
            Redis::del("{$message["chatId"]}niuniu");  
            mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->delete();
            mdb\niucountbak::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->delete();
            $ret['sendText'] = "<b>所有数据删除成功(含总共统计丶历史数据)</b>";
            $ret['sendText'] .= "\n\n<b>下注次数：0</b>\n<b>口数输赢：<u>0</u></b>\n<b>总共输赢：<u>0</u></b>";
            return $ret;
        }else if($message["text"] == "撤回"){
             $list = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->order("id","desc")->find(); 
             if($list){
                 $cishu = Redis::hget("{$message["chatId"]}niuniu","cishu") - 1;
                 if($cishu){ 
                     $tongji = Redis::hget("{$message["chatId"]}niuniu","tongji") - $list['money'];   
                     $yingnum = Redis::hget("{$message["chatId"]}niuniu","yingnum") -1; 
                     $shunum = Redis::hget("{$message["chatId"]}niuniu","shunum") -1;  
                     
                    Redis::hset("{$message["chatId"]}niuniu","cishu",$cishu);   
                    Redis::hset("{$message["chatId"]}niuniu","tongji",$tongji);  
                    Redis::hset("{$message["chatId"]}niuniu","yingnum",$yingnum);  
                    Redis::hset("{$message["chatId"]}niuniu","shunum",$shunum);  
                 } 
                mdb\niucountlist::destroy([$list['id']]); 
                mdb\niucountbak::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->order("id","desc")->limit(1)->delete();
                
                $ret['sendText'] = "<b>撤回记账：第{$list['num']}口</b> <code>{$list['money']}</code>\n\n"; 
                 
                $listall = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$message['bot']['API_BOT'])->order("id","desc")->select();
                $listall = array_reverse($listall->toArray());
                foreach ($listall as $json) {   
                    if($json['money'] > 0){
                        $ret['sendText'] .= "\n<b>{$json['num']}口</b>  <code>{$json['json']['text']}</code>  <b>赢</b>";
                    }else{
                        $ret['sendText'] .= "\n<b>{$json['num']}口</b>  <code>{$json['json']['text']}</code>  输";
                    }
                     
                } 
                $ret['sendText'] .= "\n\n<b>下注次数：{$cishu}</b>\n<b>当前输赢：<u>{$tongji}</u></b>";
                
                $ret['sendText'] = urlencode($ret['sendText']); 
                
                
                
             }
             
             
            return $ret; 
        }elseif( $message['text']  == "授权"){
            $set = new mdb\niucount; 
            $set =  $set->getJson($message['bot']['API_BOT']);  
            if($message['formId'] != $message['bot']['Admin']){
               $ret['sendText'] = "请联系老板 @{$message['bot']['AdminName']} 进行授权使用！";
               return $ret;  
            } 
            $set['value'] .=  "\n" .$message['chatId'];
            $set->save(); 
            $ret['sendText']  = "<u>{$message['chatName']}</u> <b>授权成功!</b>\n\n<b>指令说明：</b>\n\n<b>+10</b>  +代表赢\n<b> -10</b>  - 代表输\n\n<b>撤回</b> 代表撤回上笔记账\n<b>清空统计</b> 代表清空口数据(不清历史数据)\n<b>清空所有</b> 代表删除所有数据包含历史记账数据";
            Cache::delete("niucount_0");
            Cache::delete("niucount_{$message['bot']['API_BOT']}");
            $ret['sendText'] =urlencode($ret['sendText']);
            return $ret; 
        }else if(pipei($message['text'],"^(10(\.0)?|([0-9](\.[0-9])?|10\.5))\/(10(\.0)?|([0-9](\.[0-9])?|10\.5))$")){
            Redis::lpush("{$message['bot']['API_BOT']}{$message['chatId']}zoushi",$message['text']); 
            Redis::ltrim("{$message['bot']['API_BOT']}{$message['chatId']}zoushi", 0, 9); 
            $zoushi_list = Redis::LRANGE("{$message['bot']['API_BOT']}{$message['chatId']}zoushi",0,9); 
            $xdata = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
            
            $zhuang =null; 
            $xian =null; 
            
             
            
            $zoushi_list = array_reverse($zoushi_list);
            
             
     
            
            for ($i = 0; $i < 10; $i++) { 
                
                if(isset($zoushi_list[$i])){
                    $tt = explode("/",$zoushi_list[$i]);
                    if(!isset($tt[1])){
                         continue;
                    }
                    $zhuang[] = $tt[0];
                    $xian[] = $tt[1];
                }else{
                    $zhuang[] = 0;
                    $xian[] = 0;
                } 
                 
                
            } 
            
            $ydata = array($zhuang,$xian); 
            $seriesName = array("a","b");
            $title = "实时走势图→";
            $Img = new Charts($title,$xdata,$ydata,$seriesName);
            $imageContent = $Img->paintLineChart(); 
            $filet = chuangjian (run_path().'/runtime/niuniu')."/{$message['msgId']}.png";
            $result = file_put_contents($filet, $imageContent);
            if ($result !== false) {
                $ret['sendPhoto'] = $filet;
            } 
            return $ret; 
            
        }else if($message['text'] == '牛牛走势'){
            $zoushi_list = Redis::LRANGE("{$message['bot']['API_BOT']}{$message['chatId']}zoushi",0,9); 
            $xdata = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
            
            $zhuang =null; 
            $xian =null; 
            
            
            $zoushi_list = array_reverse($zoushi_list);
            
             
            
     
            
            for ($i = 0; $i < 10; $i++) {  
                if(isset($zoushi_list[$i])){
                    $tt = explode("/",$zoushi_list[$i]); 
                    if(!isset($tt[1])){
                         continue;
                    }
                    $zhuang[] = $tt[0];
                    $xian[] = $tt[1];
                }else{ 
                    $zhuang[] = 0;
                    $xian[] = 0;
                } 
                 
                
            } 
             
            
            $ydata = array($zhuang,$xian); 
            $seriesName = array("a","b");
            $title = "实时走势图→";
            $Img = new Charts($title,$xdata,$ydata,$seriesName);
            $imageContent = $Img->paintLineChart(); 
            $filet = chuangjian (run_path().'/runtime/niuniu')."/{$message['msgId']}.png";
            $result = file_put_contents($filet, $imageContent);
            if ($result !== false) {
                $ret['sendPhoto'] = $filet;
            } 
            return $ret; 
        }else if($message['text'] == '清空走势'){
            $ret['sendText'] = "群ID：{$message['chatId']} \n群标题：<code>{$message['chatName']}</code>\n\n<b>走势数据图已清空</b>！";
            Redis::del("{$message['bot']['API_BOT']}{$message['chatId']}zoushi");
            return $ret; 
        }else if($message['text'] == '撤回走势'){
            Redis::LPOP("{$message['bot']['API_BOT']}{$message['chatId']}zoushi");
            $zoushi_list = Redis::LRANGE("{$message['bot']['API_BOT']}{$message['chatId']}zoushi",0,19); 
            $xdata = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
            
            $zhuang =null; 
            $xian =null; 
            
            
            $zoushi_list = array_reverse($zoushi_list); 
            
            for ($i = 0; $i < 10; $i++) {  
                if(isset($zoushi_list[$i])){
                    $tt = explode("/",$zoushi_list[$i]); 
                    if(!isset($tt[1])){
                         continue;
                    }
                    $zhuang[] = $tt[0];
                    $xian[] = $tt[1];
                }else{ 
                    $zhuang[] = 0;
                    $xian[] = 0;
                } 
                 
                
            } 
             
            
            $ydata = array($zhuang,$xian); 
            $seriesName = array("a","b");
            $title = "实时走势图→";
            $Img = new Charts($title,$xdata,$ydata,$seriesName);
            $imageContent = $Img->paintLineChart(); 
            $filet = chuangjian (run_path().'/runtime/niuniu')."/{$message['msgId']}.png";
            $result = file_put_contents($filet, $imageContent);
            if ($result !== false) {
                $ret['sendPhoto'] = $filet;
            } 
            return $ret; 
        }
            
        
        
        
        
         
        
         
        
        if($msg = pipei($message['text'],"^([-|+])\s?([0-9]+)\s?$")){ 
            $set = new mdb\niucount; 
            $set =  $set->getJson($message['bot']['API_BOT']); 
            
            if(!pipei($set['value'],$message['chatId'])){
                $ret['sendText'] = "群ID：{$message['chatId']} \n群标题：<code>{$message['chatName']}</code>\n\n<b>很抱歉,该群未被授权使用</b>\n\n请联系老板 @{$message['bot']['AdminName']} 进行授权使用！";
                return $ret; 
            }
            
             
            
            
            $bot = $message['bot']['API_BOT'];
            $list = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$bot)->order("id","desc")->find();
            
            
            $jia =0;
            $del=0;
            $ynum=0;
            $ymoney =0;
            $delarr=[];
            $snum=0;
            $smoney=0;
            
            
            $cishu = Redis::hget("{$message["chatId"]}niuniu","cishu")+1; 
            if($msg[1] == '+'){
                $tongji = Redis::hget("{$message["chatId"]}niuniu","tongji") +$msg[2]; 
                $yingnum = Redis::hget("{$message["chatId"]}niuniu","yingnum")+1; 
                $yingmoney = Redis::hget("{$message["chatId"]}niuniu","yingmoney")+$msg[2]; 
                
                $shunum = Redis::hget("{$message["chatId"]}niuniu","shunum"); 
                $shumoney = Redis::hget("{$message["chatId"]}niuniu","shumoney"); 
                
                Redis::hset("{$message["chatId"]}niuniu","yingnum",$yingnum);
                Redis::hset("{$message["chatId"]}niuniu","yingmoney",$yingmoney);
            
            }else{
                $tongji = Redis::hget("{$message["chatId"]}niuniu","tongji") - $msg[2]; 
                $shunum = Redis::hget("{$message["chatId"]}niuniu","shunum")+1; 
                $shumoney = Redis::hget("{$message["chatId"]}niuniu","shumoney")+$msg[2]; 
                
                $yingnum = Redis::hget("{$message["chatId"]}niuniu","yingnum"); 
                $yingmoney = Redis::hget("{$message["chatId"]}niuniu","yingmoney"); 
                
                Redis::hset("{$message["chatId"]}niuniu","shunum",$shunum);
                Redis::hset("{$message["chatId"]}niuniu","shumoney",$shumoney);  
            }
            Redis::hset("{$message["chatId"]}niuniu","cishu",$cishu); 
            Redis::hset("{$message["chatId"]}niuniu","tongji",$tongji); 
             
             
                
            unset($message['bot']);      
            
            $bak['bot'] = $bot;
            unset($message['bot']);   
            $bak['qunid'] = $message['chatId'];
            $bak['num'] = $cishu;
            $bak['tgid'] = $message['formId'];
            $bak['val'] = $msg[1];
            $bak['money'] = $message['text']; 
            $bak['json'] = $message;
            $bak['total'] = $tongji;
            $dd = new mdb\niucountbak; 
            $listbak = $dd->create($bak);  
                
               
            if(empty($list)){
                $sql['bot'] = $bot; 
                $sql['qunid'] = $message['chatId'];
                $sql['num'] = 1;
                $sql['tgid'] = $message['formId'];
                $sql['val'] = $msg[1];
                $sql['money'] = $message['text']; 
                $sql['json'] = $message;
                $sql['total'] = $message['text']; 
                $cc = new mdb\niucountlist; 
                $list = $cc->create($sql);   
                
                 
                
            }else{ 
                  
                
                
                if($list['num'] >=3){
                    $list2 = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$bot)->order("id","desc")->limit(3)->select(); 
                    foreach ($list2 as $value) {
                        if($value['val'] == '+'){
                            $ymoney +=$value['money'];
                            $ynum +=1;
                            array_push($delarr,$value['id']);
                        }else{
                            $smoney -= $value['money'];
                            $snum +=1;
                            array_push($delarr,$value['id']);
                        }
                    } 
                }
                if($msg[1] == '-'){
                    $smoney += $msg[2];
                             
                }else{
                    $ymoney += $msg[2];
                }
                
                echo "赢{$ymoney}  输：{$smoney}"; 
                
                
                if(isset($list2) && count($list2) ==3 && $ymoney >= $smoney){
                    mdb\niucountlist::destroy($delarr);
                    #$list = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$bot)->order("id","desc")->find();
                }else{
                    $sql['bot'] = $bot;
                    unset($message['bot']);   
                    $sql['qunid'] = $message['chatId'];
                    $sql['num'] = $list['num'] + 1;
                    $sql['tgid'] = $message['formId'];
                    $sql['val'] = $msg[1];
                    $sql['money'] = $message['text']; 
                    $sql['json'] = $message;
                    if($msg[1] == '+'){
                        $sql['total'] = $list['total'] + $msg[2]; 
                    }else{ 
                        $sql['total'] = $list['total'] - $msg[2];  
                    } 
                    $cc = new mdb\niucountlist; 
                    $list = $cc->create($sql);   
                }  
            }
         
             
            
            $listall = mdb\niucountlist::where("qunid",$message["chatId"])->where("bot",$bot)->order("id","desc")->select();
            
            $ret['sendText'] = "";
            $i=0;
            $moeny = 0;
            $listall = array_reverse($listall->toArray());
            foreach ($listall as $json) {  
                $moeny += $json['money'];
                if($json['money'] > 0){
                    $ret['sendText'] .= "\n<b>{$json['num']}口</b>  <code>{$json['json']['text']}</code>  <b>赢</b>";
                }else{
                    $ret['sendText'] .= "\n<b>{$json['num']}口</b>  <code>{$json['json']['text']}</code>  输";
                }
                 
            } 
            $ret['sendText'] .= "\n\n<b>下注次数：{$cishu}</b>  [<code>赢{$yingnum} 输{$shunum}]</code>\n<b>口数输赢：<u>{$moeny}</u></b>\n<b>总共输赢：<u>{$tongji}</u></b>";
            
            $ret['sendText'] = urlencode($ret['sendText']);
            
        }
        
         
        
        
        
        #代码结束
        return $ret;     
    }
} 
