<?php
namespace bot\_info; 

 
class chaid  {  
    public function index(){    
         
        $info['core']       = 240331;
        
        $info['lv']         = 86;
           
        $info['key']        = "chaid";
         
        $info['name']       = "查Ta的电报ID";
        
        $info['text']       = "查看指定的用户丶群组丶频道丶机器人的电报ID"; 
         
        $info['version']    = "2.2";
        
        $info['new']        = "支持查看他人的用户名，昵称，电报ID";
        
        $info['menuId']     = [0];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["cha1.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "exDfkEMwCEQwJuUEnn2L1RxtKoZF1fSWn3np2W7u8YQfM/N+MBW8gcxdjHZsRt72q9Pxz4QsCfNuu3u1CDj/pEYNrhz0CP5Ancioig7bWMjM8HfUIj3Ndm7/0A/Q0fRmpqg8HvJfba3NN8r5BsQKcY+lzQYl3TKV00RR6Vi3kyk=";
 
        $info['tables']     = [];
        
        $info['btn']       = "💡查电报ID";
        
        $info['btnType']   = 1;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      