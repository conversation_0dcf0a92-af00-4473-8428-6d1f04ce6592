<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>plugin</title>
  <!--系统CSS 你可以把css放到：/bot/res/xxx/目录下然后在下引用 引用只需要：/xxx/你的.css -->
  <link rel="stylesheet" href="/assets/css/diy.css">
  <link rel="stylesheet" href="/assets/css/list2.css">
  <link rel="stylesheet" href="/assets/font/iconfont.css">
  <!--第三方css-->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> 
  <style type="text/css"> 
    [v-cloak] { 
      display: none; 
    } 
  </style>  
</head>
<body>
<div id="app" v-cloak>
<div class="loca"> 
<div class="ele-body">
<el-row :gutter="10" class="row-bg">
<!--********以上代码不懂不要随意修改,下面开始写element vue代码*******-->

        <el-col :lg="16" :xs="24">
          <el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> 验群设置</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>

            <el-form ref="setform" :model="setForm" label-width="128px" label-suffix="：" style="padding:10px 0"> 
            <el-row :gutter="10" class="row-bg">
            <el-col :lg="24" :xs="24">
                <el-form-item label="设置机器人" size="small" prop="value">
                  <el-select v-model="setForm.bot" allow-create filterable placeholder="请选择机器人" @change="remoteSeleok" style="width:180px">
                    <el-option v-for="item in botlist" :key="item" :label="item" :value="item">
                    </el-option>
                  </el-select> 
                   &nbsp;
                  <el-tooltip content="当其它机器人没有单独设置数据时,则默认使用该机器人的配置信息">
                    <el-checkbox v-model="setForm.type" :true-label="99"  :false-label="0"  >设为总管机器人</el-checkbox>
                  </el-tooltip>
                </el-form-item> 
                </el-col>
                <br>
                 <el-divider><i class="el-icon-mobile-phone"></i>私聊验群编号 客服设置</el-divider>
            
            <el-col :lg="12" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     真群回复内容
                    <el-tooltip content="私聊机器人发送编号时，真群回复内容">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="当群存在时回复内容" v-model="setForm.json['zhen']"> </el-input> 
              </el-form-item>
             </el-col>  
             <el-col :lg="12" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                    假群回复内容
                    <el-tooltip content="私聊机器人发送编号时，假群回复内容">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="当群不存在时回复内容" v-model="setForm.json['jia']"> </el-input> 
              </el-form-item>
              
            </el-col>  
             <el-col :lg="12" :xs="24">    
              <el-form-item   size="small" >
                  <template slot="label">
                    官方人员列表
                    <el-tooltip content="1行1个输入官方人员电报号不带@,只有这些客户号可以设置群编号 初始化等(同时私聊机器人发送这些客服号会提示是真官方人员)">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="1行1个输入官方人员电报号 不带@" v-model="setForm.json['kefu']"> </el-input> 
              </el-form-item>
            </el-col>    
               
            </el-row>  

              <el-form-item>
                  <!--<el-button  @click="load()">刷新</el-button> -->
                <el-button type="primary" @click="update()">保存设置</el-button>  
              </el-form-item>
              
              
              
              
              <div class=" ">
                <span class="qianlan ">私聊机器人发送群编号可以验证真假群</span> <br>
                <span class="qianlan ">私聊机器人发送客服用户名可以验证真假客服</span>
              </div>

            </el-form>

          </el-card>
        </el-col>

 
         
    
    
    
        
<!--*****************************代码结束*************************-->
</el-row>
</div>
</div>
</div>
</body> 
<!--系统js 你可以把js放到：/bot/res/xxx/目录下然后在下引用 引用时只需要：/xxx/你的.js-->
<script src="/assets/js/vue.js"></script>  
<script src="/assets/js/axios.js"></script> 
<script src="/assets/js/clipboard.js"></script> 
 

<!--第三方js 如果你要加载其它js请填写到下面-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>  
 

<!--VUE js-->
<!--VUE js-->
<script>
var table = 'he444bot';
{literal}
new Vue({
  el: '#app',
  //页面参数定义
  data() {
    return { 
        loading: true,  //加载状态 
        setForm:
            {json:{
                zhen:"",
                jia:""
                }  
            }, 
        botlist:[],
    }
  },
  //数据监听
  watch: {
      
  },
  
  
  
  //初始化1·渲染前
  created() {
      this.load() 
  },
  
  
  
  //初始化2·渲染后
  mounted() { 
      this.loading = true;
        axios.get("/app/appStore/bot/index ",{}).then((res) => {
            this.loading = false;
            this.botlist = res.data.data
            //this.botlist.unshift("全局设置")
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        }); 
      
     
  }, 
  
  
  
  //按钮点击事件方法
  methods: { 
    copy(obj,text){
        const clipboard = new ClipboardJS(obj, {
        text: function (trigger) {
                return text;
            }
        });
        clipboard.on('success', e => { 
            clipboard.destroy()
            this.$message.success('复制成功');
            return ;
        });
        clipboard.on('error', e => {
            clipboard.destroy()
            this.$message.error('复制失败'); 
            return ;
        });
    },   
    //以上按钮事件必备
    load(){ 
        axios.get("/web/"+table+"/setget",{params:{bot:this.setForm.bot}}).then((res) => { 
            if(!res.data.data){
                this.$message.error(res.data.msg || "似乎没有配置数据哟");
                this.setForm.type = 0;
                return false
            }
            
            
            // if (res.data.data.bot == 0 || res.data.data.bot == '0') {
            //     res.data.data.bot = "全局设置"  
            // }
            this.setForm = res.data.data
            this.$message.success(res.data.msg);
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });    
    },
    update(){
        axios.post("/web/"+table+"/update",{type:"set",data:this.setForm}).then((res) => { 
            
            this.$message.success(res.data.msg);
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });  
        
    },
    remoteSeleok(){
       this.load() 
    }
     
  }, 
  
  
  
  //离开页面前事件
  beforeDestroy () {
  },
})
{/literal}
</script>
</html>