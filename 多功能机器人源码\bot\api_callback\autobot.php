<?php
namespace bot\api_callback;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 
use Webman\Event\Event;

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
 
class autobot  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["callId"]       =   点击唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $message["text"]         =   消息文字内容
     * $message["time"]         =   消息到达-服务器时间戳
     * $message["msgTime"]      =   消息发布时间戳  电报官方时间
     * $message["editTime"]     =   消息最后编辑时间戳 0未编辑过
     * 
     * $message["btnData"]      =   消息按钮对应 消息事件
     * 
     * $message["gamaId"]       =   游戏标识ID   
     * $message["gameName"]     =   游戏唯一标识 游戏才有
     
     * $message["photo"]        =   点击按钮时消息内容中-如果有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   点击按钮时消息内容中-有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   点击按钮时消息内容中-有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   点击按钮时消息内容中-有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     *  ■■ $ret ■■ 返回参数说明：
     *  alert (最高优先级 - 下面参数无效) back=0 代表编辑按钮时禁止增加返回按钮
     *  delMessage=1 代表点击按钮后删除原消息 huifu=1 强制用户回复 huifuTips 回复占位符提示文字
     *  jianpan(键盘按钮) + jianpanText(文本消息) 或 jianpanPhoto(发送照片) （第2优先级 - 下面参数无效）
     *  sendText(发送文本消息),sendPhoto(发送照片),sendVideo(发送视频),sendFile(发送文件)  ||  editText(编辑消息内容) ||  anniu(消息按钮)
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){ 
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        #------------------------以上代码默认请勿修改 level 视情况调整改动--------------------- 
        if($message['btnData'] == '暂不支持开通'){
            $ret['alert'] = "该功能暂时无法自助开通\n请联系老板更新模块后尝试！";
            return $ret;
            
        }else if($bots = pipei($message["btnData"],"^mybot_(\w+)$")){
            $bot_list = model\bot_list::where("API_BOT",$bots[1])->where("del",0)->where("Admin",$message['formId'])->find();
            if(empty($bot_list) || empty($bot_list['zt'])){
                $ret['alert'] = "@{$bots[1]}\n已被取消托管或停用";
                return $ret;
            } 
            $gnname="";
            $myapp = array_flip($bot_list['appStore']);  
            $applist = Event::emit('bot\_info', 0); 
            $applist = array_column($applist, null, 'key');   
            foreach ($applist as $sb) { 
                if(isset($myapp[$sb['key']])){ 
                    $gnname .= "☑️ <code>{$sb['name']}功能</code>\n";
                } 
            } 
            $ret['editText'] = "{$gnname} \n 机  器  人：@{$bots[1]}\n到期时间：<code>".date("Y-m-d H:i:s",$bot_list['outime'])."</code>";
            $ret['anniu'] = [
                                 
                                [
                                     
                                    [
                                        "text" => "🔲为机器人开通更多功能..",
                                        "callback_data" => "mybot~{$bots[1]}"
                                    ]
                                ],
                                [
                                    [
                                        "text" => "💰余额充值",
                                        "callback_data" => "余额充值"
                                    ],
                                    [
                                        "text" => "⏱续费机器人",
                                        "callback_data" => "续费机器人·{$bots[1]}"
                                    ]
                                ],
                            ];
            
            
            // $aa[] = ["text" => "💰余额充值","callback_data" => "设置"];
            // $aa[] = ["text" => "💰余额充值","callback_data" => "设置"];
            // $ret['anniu'][] =$aa;                
                            
                            
            return $ret;                
        }else if($bots = pipei($message["btnData"],"^mybot~(\w+)~?([0-9]+)?")){ 
            
            $bot_list = model\bot_list::where("API_BOT",$bots[1])->where("del",0)->where("Admin",$message['formId'])->find();
            if(empty($bot_list) || empty($bot_list['zt'])){
                $ret['alert'] = "@{$bots[1]}\n已被取消托管或停用";
                return $ret;
            } 
            
            
            
            $myapp = array_flip($bot_list['appStore']);   
            
              
            
            $applist = Event::emit('bot\_info', 0); 
            #$applist = array_column($applist, null, 'key');   
            if(empty($bots[2])){ 
               $page = 1;
               $limit = ($page-1)*10;
            }else{   
               $page = $bots[2]; //1=0  2=10   3=20
               $limit = ($page-1)*10;
            }
            $updownBtn =[];
                            
            $gnname = "";  
            //0 10 | 10 20 | 20 30 
            for ($i = $limit; $i < $limit+10; $i++) { 
                if(empty($applist[$i]['key'])){
                    continue;
                } 
           
                
                if(isset($myapp[$applist[$i]['key']])){ 
                    $gnname .= numImg('ok')." <code>{$applist[$i]['name']}功能</code>\n"; 
                    $ret['anniu'][] = [["text"=>"⛔️ 删除{$applist[$i]['name']}功能","callback_data"=>"删除机器人功能-{$bots[1]}-{$applist[$i]['key']}"]];
                }else{
                    if(empty($applist[$i]['autobuy'])){
                        $gnname .= "🚫 <code>{$applist[$i]['name']}功能</code>\n";
                        $ret['anniu'][] = [["text"=>"⚠️暂不支持开通：{$applist[$i]['name']}","callback_data"=>"暂不支持开通"]];
                        
                    }else{ 
                        $gnname .= "🔲 <code>{$applist[$i]['name']}功能</code>\n";
                        $ret['anniu'][] = [["text"=>"🔲 开通{$applist[$i]['name']}功能","callback_data"=>"开通机器人功能-{$bots[1]}-{$applist[$i]['key']}"]];
                    }
                }
            }
            if($page >  1){  
                $ret['back'] = 0;
                $updownBtn[] = ["text"=>"上一页","callback_data"=>"mybot~{$bots[1]}~".$page-1];
            } 
            
            if(count($applist) > $limit+10){  
                $updownBtn[] = ["text"=>"下一页","callback_data"=>"mybot~{$bots[1]}~".$page+1];
            } 
            if(!empty($updownBtn)){  
                $ret['anniu'][] =  $updownBtn;
            }
            
            if(isset($bots[2]) && $bots[2] == 1){
                $ret['back'] = 0;
                $backBtn = [
                    [
                        [
                            "text" => trans('返回'),
                            "callback_data" => "返回上一步"
                        ],
                        [
                            "text" => trans('删除消息'),
                            "callback_data" => "删除消息"
                        ]
                    ]
                ];  
                $ret['anniu']  = array_merge($ret['anniu'],$backBtn); 
            }
              
            
            // foreach ($applist as $sb) { 
            //     if(isset($myapp[$sb['key']])){ 
            //         $gnname .= "✅ <code>{$sb['name']}功能</code>\n";
            //     }else{
            //         $gnname .= "🔲 <code>{$sb['name']}功能</code>\n";
            //     }
            // }
            
            $ret['editText'] = "<b>机器人当前功能：\n{$gnname}\n机 器 人 ：@{$bot_list['API_BOT']}\n到期时间：<code>".date("Y-m-d H:i:s",$bot_list['outime'])."</code></b>\n\n<b>点击以下按钮开通对应功能：</b>";
              
        }else if($key = pipei($message['btnData'],"^开通机器人功能-(\w+)-(\w+)$")){ 
            $autobot_set = model\autobot_set::where("bot",$key[2])->find(); 
            if($key[2] == 'tgvips'){
                $ret['alert'] = "该功能暂时不支持自助开通...";
                return $ret;
            }
            if(empty($autobot_set)){
                $bot_list = model\bot_list::where("API_BOT",$key[1])->where("del",0)->where("Admin",$message['formId'])->find(); 
                if(empty($bot_list)){
                    $ret['alert'] = "获取机器人数据失败...";
                    return $ret;
                }
                $aa = $bot_list['appStore'];
                $aa[] = $key[2]; 
                model\bot_list::where("id",$bot_list['id'])->update(['appStore'=>$aa]);  
                
                $tuoguan = Db::name("bot_list_tuoguan")->where("upbot",$message['bot']['API_BOT'])->where("myadmin",$message['formId'])->select();
                $botl = []; 
                $ret["editText"] = "<b>你托管的机器人数量</b>：<b>".count($tuoguan)."</b>"; 
                $ret["editText"] .= "\n<pre><code class='language-机器人管理说明：'>点击对应机器人可进行管理,续费,增加功能,删除托管等等</code></pre>";
                foreach ($tuoguan as $bot) {
                    $a["text"] = "@{$bot['mybot']}";
                    $a["callback_data"] = "mybot_{$bot['mybot']}"; 
                    array_push($botl,[$a]); 
                }
                 
                $ret['back'] = 0;
                $ret["anniu"] = $botl; 
                
                #更新机器人  
                $queueData['plugin'] = "tgbot";
                $queueData['bot'] = $bot_list['API_BOT'];
                $queueData['type'] = "更新菜单命令";  
                RQamsg::send('TG_queue',$queueData,1); 
                return $ret;
            }else{//付费开通  
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();
                if($user['usdt'] < $autobot_set['u']){
                    if(empty($autobot_set['json'])){
                        $ret['alert'] = "开通该功能需要：{$autobot_set['u']} U,你的账户余额不足！"; 
                        return $ret;
                    } 
                    if(!in_array($key[1], $autobot_set['json'])){
                        $ret['alert'] = "开通该功能需要：{$autobot_set['u']} U,你的账户余额不足！"; 
                        return $ret;
                    }
                     
                }
                $bot_list = model\bot_list::where("API_BOT",$key[1])->where("del",0)->where("Admin",$message['formId'])->find(); 
                if(empty($bot_list)){
                    $ret['alert'] = "获取机器人数据失败...";
                    return $ret;
                }
                 
                
                
                
                $aa = $bot_list['appStore'];
                $aa[] = $key[2]; 
                model\bot_list::where("id",$bot_list['id'])->update(['appStore'=>$aa]);  
                if(empty($autobot_set['json']) || !in_array($key[1], $autobot_set['json'])){ 
                    $autojson = $autobot_set['json'];
                    $autojson[] = $key[1]; 
                    model\autobot_set::where("id",$autobot_set['id'])->update(['json'=>$autojson]); 
                    
                    $buylog['type']=4; //1充值 2提现 3转账 4消费
                    $buylog['bot']=$message['bot']['API_BOT'];
                    $buylog['zt']=1;
                    $buylog['uid']=$user['id'];
                    $buylog['user']=$user['user'];
                    $buylog['tgid']=$user['tgid'];
                    $buylog['name']=$user['name']; 
                    $buylog['coin']="USDT";
                    $buylog['lmoney']=$user['usdt'];
                    $buylog['money']= -$autobot_set['u'];
                    $buylog['nmoney']=$user['usdt']-$autobot_set['u'];
                    $buylog['value']="<span class='hong'>@{$bot_list['API_BOT']}</span> 开通功能：{$key[2]}";
                    $buylog['create_time']=$message["msgTime"];
                    Db::name("moneylog")->insert($buylog);
                    $total = new model\total;
                    $total->inc("buy",$autobot_set['u']);   
                    
                    $user['usdt'] = $user['usdt'] - $autobot_set['u'];
                    $user->save();
                }
                
                
                
                $tuoguan = Db::name("bot_list_tuoguan")->where("upbot",$message['bot']['API_BOT'])->where("myadmin",$message['formId'])->select();
                $botl = []; 
                $ret["editText"] = "恭喜你\n\n<b>开通功能成功</b>：<b>".$key[2]."</b>"; 
                #$ret["editText"] .= "\n\n<pre><code class='language-机器人管理说明：'>点击对应机器人可进行管理,续费,增加功能,删除托管等等</code></pre>";
                foreach ($tuoguan as $bot) {
                    $a["text"] = "@{$bot['mybot']}";
                    $a["callback_data"] = "mybot_{$bot['mybot']}"; 
                    array_push($botl,[$a]); 
                }
                 
                $ret['back'] = 0;
                $ret["anniu"] = $botl; 
                
                #更新机器人  
                $queueData['plugin'] = "tgbot";
                $queueData['bot'] = $bot_list['API_BOT'];
                $queueData['type'] = "更新菜单命令";  
                RQamsg::send('TG_queue',$queueData,1); 
                return $ret;
                
            }
            return $ret;
             
        }else if($key = pipei($message['btnData'],"^删除机器人功能-(\w+)-(\w+)$")){    
            $bot_list = model\bot_list::where("API_BOT",$key[1])->where("del",0)->where("Admin",$message['formId'])->find(); 
            if(empty($bot_list)){
                $ret['alert'] = "获取机器人数据失败...";
                return $ret;
            }
            $aa = $bot_list['appStore'];
            
            $sokey = array_search($key[2], $aa);  // 查找 "xxo" 在数组中的键
            if ($sokey !== false) {
                unset($aa[$sokey]);  //如果找到了,就删除该元素
            }  
            model\bot_list::where("id",$bot_list['id'])->update(['appStore'=>$aa]);
            
            
            $tuoguan = Db::name("bot_list_tuoguan")->where("upbot",$message['bot']['API_BOT'])->where("myadmin",$message['formId'])->select();
            $botl = []; 
            $ret["editText"] = "恭喜你\n\n<b>删除功能成功</b>：<b>".$key[2]."</b>"; 
            #$ret["editText"] .= "\n\n<pre><code class='language-机器人管理说明：'>点击对应机器人可进行管理,续费,增加功能,删除托管等等</code></pre>";
            foreach ($tuoguan as $bot) {
                $a["text"] = "@{$bot['mybot']}";
                $a["callback_data"] = "mybot_{$bot['mybot']}"; 
                array_push($botl,[$a]); 
            } 
            $ret['back'] = 0;
            $ret["anniu"] = $botl; 
            
            #更新机器人   
            $queueData['plugin'] = "tgbot";
            $queueData['bot'] = $bot_list['API_BOT'];
            $queueData['type'] = "更新菜单命令";  
            RQamsg::send('TG_queue',$queueData,1); 
            return $ret;
            
        }else if(pipei($message['btnData'],"^续费机器人·")){
            $bs = explode("·",$message['btnData']);
            $user = model\bot_account::where("tgid",$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
           
            $autobot = model\autobot_set::where("bot",$message['bot']['API_BOT'])->find(); 
            if(empty($autobot)){
                $autobot = model\autobot_set::where("bot",0)->find();  
            }
            
    
            
            $ret['editText'] = "被续费机器人：@{$bs[1]}\n\n你的USDT余额：<b>{$user['usdt']}</b> USDT\n\n<b>请选择续费时长</b>";
            $ret['anniu'] = [
                                 
                                [
                                     
                                    [
                                        "text" => "🟢 续费1个月：{$autobot['json']['1个月']} USDT",
                                        "callback_data" => "确认续费机器人·1·{$autobot['json']['1个月']}·{$bs[1]}"
                                    ]
                                     
                                ],
                                [
                                     
                                    [
                                        "text" => "🟡 续费6个月：{$autobot['json']['6个月']} USDT",
                                        "callback_data" => "确认续费机器人·6·{$autobot['json']['6个月']}·{$bs[1]}"
                                    ]
                                ],
                                [
                                     
                                    [
                                        "text" => "🟣 续费1 年：{$autobot['json']['12个月']} USDT",
                                        "callback_data" => "确认续费机器人·12·{$autobot['json']['12个月']}·{$bs[1]}"
                                    ]
                                ],
                            ]; 
            return $ret;                
        }else if(pipei($message['btnData'],"^确认续费机器人·")){ 
            $f3 = explode("·",$message['btnData']);//分割结果：0命令，1时长，2价格，3机器人用户名 
            $user = model\bot_account::where("tgid",$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();   
            if($user['usdt'] < $f3[2]){
                $ret['alert'] = "续费失败\n您的账户USDT不足：{$f3[2]}";
                return $ret;
            }  
            echo "1";
            $bot_list = model\bot_list::where("API_BOT",$f3[3])->where("Admin",$message['formId'])->where("upbot",$message['bot']['API_BOT'])->find();
            if(empty($bot_list)){
                $ret['alert'] = "续费失败\n获取你的机器人数据失败..";
                return $ret;
            } 
            
            $dbb  = Db::name("bot_account")->dec("usdt",$f3[2])->where("id",$user['id'])->update();
            if($dbb){
                if($bot_list['outime'] < $message["time"]){
                   $outime = $message["time"] +  ($f3[1] * 31 *86400);  
                }else{
                   $outime = $bot_list['outime'] +  ($f3[1] * 31 *86400); 
                } 
                Db::name("bot_list")->where("id",$bot_list['id'])->cache("tgbot_{$bot_list['API_BOT']}")->update(["outime" => $outime]);
            }
            $ret['delMessage'] = 1;
            $ret['sendText'] = "<b>恭喜你,续费成功".numImg('ok')."</b>\n机  器  人：@{$f3[3]}\n到期时间：<code>".date("Y-m-d H:i:s",$outime)."</code>"; 
            $ret['anniu'] = [
                                 
                                [
                                     
                                    [
                                        "text" => "🔲为机器人开通更多功能..",
                                        "callback_data" => "mybot~{$f3[3]}"
                                    ]
                                ],
                                [
                                    [
                                        "text" => "💰余额充值",
                                        "callback_data" => "余额充值"
                                    ],
                                    [
                                        "text" => "⏱续费机器人",
                                        "callback_data" => "续费机器人·{$f3[3]}"
                                    ]
                                ],
                            ];
            $buylog['type']=4; //1充值 2提现 3转账 4消费
            $buylog['bot']=$message['bot']['API_BOT'];
            $buylog['zt']=1;
            $buylog['uid']=$user['id'];
            $buylog['user']=$user['user'];
            $buylog['tgid']=$user['tgid'];
            $buylog['name']=$user['name']; 
            $buylog['coin']="USDT";
            $buylog['lmoney']=$user['usdt'];
            $buylog['money']= -$f3[2];
            $buylog['nmoney']=$user['usdt']-$f3[2];
            $buylog['value']="<span class='hong'>@{$f3[3]}</span> 机器人续费：{$f3[1]}个月";
            $buylog['create_time']=$message["msgTime"];
            Db::name("moneylog")->insert($buylog);
            $total = new model\total;
            $total->inc("buy",$f3[2]);
            #写消费日志
             
        }
 
 
        return $ret;
    }
}
