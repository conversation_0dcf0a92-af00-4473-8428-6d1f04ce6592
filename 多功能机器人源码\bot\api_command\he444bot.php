<?php
namespace bot\api_command;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

class he444bot  {  
    /**
     * 【参数解答】
     * $message["message"]      =   电报原始消息
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     *  
     */
     
    
    #默认执行函数 
    public function index($message){  
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if(preg_match("/\/(\w+)\s*(.*)/i", $message["text"], $com)){
            if(count($com) != 3){ 
                return $ret;
            } 
        }else{
            return $ret;
        }
        $type   = $com[1]; //命令
        $value  = $com[2]; //参数
        
        #多机器人群内同命令时 只处理被@的机器人命令
        if(!empty($value)){
            if($value[0] == "@"){
                if(substr($value, 1) !=  $message["bot"]["API_BOT"]){
                    return $ret;
                } 
            }
        }
        #判断命令是否机器人admin才能用
        if(is_admin($type)){
            if($message["formId"] != $message["bot"]["Admin"]){
                return $ret;
            }
            
        }
        #-----------上面代码固定不要修改 level 视情况调整改动----------------  
        #下面开始写代码 
        
        switch ($type) {
            default: 
                break;
                
                
            case "start": 
                $set = new mdb\he444bot;
                $set = $set->getJson($message['bot']['API_BOT']);
                if(empty($set['json']['start'])){
                    $ret["sendText"] = "{$message['bot']['API_BOT']} 未设置start启动内容"; 
                    break; 
                }
                $ret["sendText"] = $set['json']['start']; 
                break; 
             
        }
        
         
        
        
        #代码结束 
        return $ret;  
    }
    
     
        
    
 
}
