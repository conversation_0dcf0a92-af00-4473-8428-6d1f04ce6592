<?php
namespace bot\web;

use support\Request;
use process\Monitor;
use Workerman\Timer;
use Workerman\Worker;
use Webman\Event\Event;  

use app\model;                          #模型  
use bot\mdb;                            #模型2 
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列  
use Webman\Push\Api as push;            #push推送
use Tntma\Tntjwt\Auth;                  #jwt 


use app\model\bot_address_jt;


class usdtjt{ 
    
    #views 返回网页视图
    public function set(Request $request){    
        return views( '/usdtjt/set', ['name' => '97bot']); 
    }
    
    public function buylist(Request $request){    
        return views( '/usdtjt/buylist', ['name' => '97bot']); 
    }
    
    
    
    #读取列表数据
    public function index(Request $request){   
        $data = $request->get();
        $page = ($data["page"]-1)*$data["limit"]; 
        $jsonVal = array();
        $so =[];  
        
        array_push($so,"id");
        array_push($so,">");
        array_push($so,0);     
          if(!empty($data["state"])){
              if($data["state"] == 1){
                  array_push($so,"type");
                  array_push($so,"=");
                  array_push($so,1);   
              }else if($data["state"] == 2){
                  array_push($so,"type");
                  array_push($so,"=");
                  array_push($so,2);   
              } 
        
          } 
        if(!empty($data["keyword"])){
          array_push($so,$data["t"]?$data["t"]:"bot");
          array_push($so,"like");
          array_push($so,"%".$data["keyword"]."%");
        }
        
        if(!empty($data["timea"])){
          array_push($so,"create_time");
          array_push($so,">");
          array_push($so,substr($data["timea"],0,10));
          array_push($so,"create_time");
          array_push($so,"<");
          array_push($so,substr($data["timeb"],0,10));
        }
        
        $so = array_chunk($so,3);//拆分 
        
        $count = bot_address_jt::where([$so])->count(); 
        $list = bot_address_jt::where([$so])->limit($page,$data["limit"])->order("id desc")->select();  
 
 
        $jsonVal["count"] = $count;
        $jsonVal["list"] = $list;   
        return json(["code" => 1,"msg"=>"获取成功","data" => $jsonVal ]);  
    }
    
    //对应数据接口的例子 GET 
    public function api_buylist(Request $request){  
        $data = $request->get();
        $page = ($data["page"]-1)*$data["limit"]; 
        $jsonVal = array();
        $so =[];  
          
        //前置条件id字段大于0
        array_push($so,"id");
        array_push($so,'>');
        array_push($so,0); 
           
          
        //按状态匹配数据 
        if(!empty($data["state"])){
            if($data["state"] == 1){
              array_push($so,"zt");
              array_push($so,"=");
              array_push($so,1);   
            }else{
              array_push($so,"zt");
              array_push($so,"=");
              array_push($so,0);  
            } 
     
        } 
        //按字段进行模糊搜索数据
        if(!empty($data["keyword"])){
           array_push($so,$data["t"]?$data["t"]:"API_BOT");
           array_push($so,"like");
           array_push($so,"%".$data["keyword"]."%");
        }
          
        //定义事件日期的搜索
        if(!empty($data["timea"])){
           array_push($so,"create_time");
           array_push($so,">");
           array_push($so,substr($data["timea"],0,10));
           array_push($so,"create_time");
           array_push($so,"<");
           array_push($so,substr($data["timeb"],0,10));
        }
          
        $so = array_chunk($so,3);//拆分  
        
        $count = Db::name("usdtjt_user_set")->where([$so])->count(); 
        $list = Db::name("usdtjt_user_set")->where([$so])->limit($page,$data["limit"])->order("id desc")->select();  
     
        $jsonVal["count"] = $count;
        $jsonVal["list"] = $list;   
        return json(["code" => 1,"msg"=>"请求成功","data" => $jsonVal ]);  
    }
    
    
    
    #获取机器人配置
    public function setget(Request $request){   
        $data = $request->get(); 
        if(empty($data['bot'])){
            $usdtjt_set =  mdb\usdtjt::where('bot', 0)->find();  
        }else{ 
            $usdtjt_set =  mdb\usdtjt::where('bot', $data['bot'])->find();  
        } 
        return json(["code" => 1,  "msg" => "获取成功","data"=>$usdtjt_set]);  
    }
    
    
    #删除数据
    public function del(Request $request){   
        $data = $request->post();
        if(empty($data["id"]) || !is_array($data["id"])){
            return json(["code" => 0,  "msg" => "数据异常"]);  
        } 
        $del = bot_address_jt::destroy($data["id"]);
        #这里应该将redis 中数据删除 
        return json(["code" => 1,  "msg" => "删除成功","data"=>$del]);  
    }
    
    
    #更新数据
    public function update(Request $request){   
        $data = $request->post(); 
        if(empty($data['type'])){
            return json(["code" => 0,  "msg" => "缺少type"]);  
        }
        switch ($data['type']) {
            default:
                return json(["code" => 0,  "msg" => "不支持的更新类型"]);  
                break;
                
            case 'set':
                if(isset($data['data']['bot'])){
                    if($data['data']['bot'] == "全局设置"){
                        $data['data']['bot'] = 0;
                    }
                    mdb\usdtjt::where('bot', $data['data']['bot'])->update($data['data']); 
                    Cache::delete("usdtjt_{$data['data']['bot']}");//这里务必按机器人删除缓存
                    return json(["code" => 1,  "msg" => "更新成功","data"=>$data['data']]);  
                }
                
                break;
            
             
        }
        
        
        
        return json(["code" => 1,  "msg" => "操作完成","data"=>0]);  
    }
    
    
    
    #新增数据
    public function create(Request $request){   
        $data = $request->post();  
        return json(["code" => 1,  "msg" => "新增成功","data"=>0]);  
    }
    
    
    
    
    
    #其它功能自己添加
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
    
}