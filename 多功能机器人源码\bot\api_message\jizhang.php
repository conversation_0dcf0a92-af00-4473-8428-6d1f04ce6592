<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
use zjkal\TimeHelper;

use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Common\Entity\Style\Color;
use Dcat\EasyExcel\Excel;


class jizhang  {  
    /**
     * 【参数解答】
     * $message["message"]      =   电报原始消息
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * $message["oldText"]      =   当有next动作交互时记录的当时的text文本  没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮-数组)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * next (文本 - 下一条文本消息将作为本次交互所需要的回复内容,比如让用户输入用户名 金额等 储存到action )
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * back=0 禁止增加消息下方的返回按钮删除按钮 delMessage=1 代表删除被回复的消息 nodel=1 代表即将发送的消息禁止被自动删除
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return $ret; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        #下面开始写代码
        if($message['text'] == "🧮记账功能"){ 
            $Model2 = new model\jizhang_set;
            $jizhangset = $Model2->getJSON($message['bot']['API_BOT']); 
            if(empty($jizhangset['json']['btntips'])){
                $ret['sendPhoto'] = run_path() ."/bot/res/jizhang/jizhang.png";
                $ret['sendText'] = "<code>请把我添加到群内使用...</code>";
                
            }else if($jizhangset['json']['btntips'] == 1){
                if(empty($jizhangset['json']['btntext'])){
                   $ret['sendText'] = "请在后台设置记账按钮提示文字"; 
                }else{
                   $ret['sendText'] = $jizhangset['json']['btntext'];
                } 
            }else if($jizhangset['json']['btntips'] == 2){
                $ret['sendPhoto'] = run_path() ."/bot/res/jizhang/jizhang.png";
                if(empty($jizhangset['json']['btntext'])){
                   $ret['sendText'] = "请在后台设置记账按钮提示文字"; 
                }else{
                   $ret['sendText'] = $jizhangset['json']['btntext'];
                }
            }  
            $ret['nodel']=1;//禁止删除稍后发送的消息
            
            $ret["anniu"] = [  
                        [
                            [
                                "text" => "➕点击添加机器人到群组",
                                "url" => "https://t.me/{$message['bot']['API_BOT']}?startgroup=true"
                            ], 
                        ],
                    ];
             
            return $ret; 
        }
        
        
            //回复消息交互事件    来源于：回调参数 ret["next"] 
            if(!empty($message["action"])){
                $Model = new mdb\jizhang_qunset;
                $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
                
                switch ($message["action"]) {
                    default: 
                        break;
                        
                        
                    case '设置日切时间':  
                        if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                            return $ret;
                        } 
                        if(strlen($message['actionText']) != 4  || !pipei($message['actionText'],"^\d+$")){
                            $ret['sendText'] = "【⚠️<b>输入错误</b>】\n晚上00:00切换则输入：<code>0000</code>\n半夜03:00切换则输入：<code>0300</code>\n\n<b>请重新输入日切时间(时分)：</b>";
                            break;  
                        }
                        if($message['actionText'] > 2359){
                            $ret['sendText'] = "⚠️<b>输入错误,时间不能大于：<u>2359</u></b>\n晚上00:00切换则输入：<code>0000</code>\n半夜03:00切换则输入：<code>0300</code>\n\n<b>请重新输入日切时间(时分)：</b>";
                            break; 
                        } 
                        $qunsetjson = $qunset['json']; 
                        $qunsetjson['time'] = $message['actionText'];
                        
                        if($message['actionText'] > date("Hi")){
                            $dd = date("Ymd"); 
                            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(["date"=>$dd,'json'=>json_encode($qunsetjson)]); 
                        }else{
                            $dd = date("Ymd",$message['tgTime']+86400); 
                            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(["date"=>$dd,'json'=>json_encode($qunsetjson)]);
                        } 
                         
                        $ret['nextdel'] = 1;
                         
                        
                        if(!empty($qunset['json']['qie'])){
                            $qie = "🔲";
                        }else{
                            $qie = "✅";
                        }
                        
                        if(empty($qunsetjson['ztjs'])){
                            $ztjs = "🔲";
                        }else{
                            $ztjs = "✅";
                        }
                        
                        $ret['sendText'] = "下一账单日期：<b>{$dd}</b>\n自动切换时间：<code>".substr($message['actionText'],0,2).":".substr($message['actionText'],-2)."</code>"; 
                        
                        $ret['anniu'] = [ 
                            [
                                [
                                    "text" => "设置日切时间",
                                    "callback_data" => "设置日切时间"
                                ]
                            ],
                            [
                                [
                                    "text" => "允许每日自动切换账单{$qie}",
                                    "callback_data" => "禁止日切"
                                ]
                            ],
                            [
                                [
                                    "text" => "昨日未下发 → 转到今日{$ztjs}",
                                    "callback_data" => "昨日未结款转移"
                                ]
                            ],
                        ];
                        
                        if(!empty($qunset['json']['ztjs'])){
                            if(empty($qunset['json']['ztxianshi'])){
                                $ztxianshi = "🔲";
                            }else{
                                $ztxianshi = "✅";
                            }
                           $ret['anniu'][] =  [
                                        [
                                            "text" => "昨日转移款 → 账单显示{$ztxianshi}",
                                            "callback_data" => "昨日转移款显示"
                                        ]
                                    ]; 
                        }
                      break;  
                }
                return $ret;
            }
 
        
        
        if($message['chatType'] == 'supergroup'){  
            if($res = pipei($message['text'],"^(删除账单|查看账单|显示账单|保存账单|开始记账|停止记账|查看操作人|设置实时汇率|撤回|下载账单|记账设置)$")){   
                if($res[1] == "显示账单"){
                    $res[1] = '查看账单';
                }
                
            }else if($res = pipei($message['text'],"^(设置汇率|设置手续费|设置费率)\s?([.0-9]+)\s?$")){
            
            }else if($res = pipei($message['text'],"^(设置操作人|添加操作人|删除操作人)\s*(@.+)$")){    
                
            }else if($res = pipei($message['text'],"^([+-]{1}|入款|下发|下发人民币)\s?([.0-9]+)\s?[\/]?([.0-9]+)?(R|RMB|人民币)?\s?")){
                 
                if($res[1] == "入款"){
                    $res[1] = "+";
                }else if($res[1] == "下发"){
                    $res[1] = "-";
                }else if($res[1] == "下发人民币"){
                    $USDTprice = Redis::GET("USDTprice");
                    $Model = new mdb\jizhang_qunset;
                    $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']);
                    $res[1] = "-";
                    if(empty($qunset['json']['ss'])){
                        $res[2] = round($res[2]/$qunset['json']['huilv'],2);
                    }else{
                        $res[2] = round($res[2]/($USDTprice+$qunset['json']['weitiao']),2);
                    }
                     
                } 
                
                if(isset($res[3])){  
                    if(empty($qunset)){
                        $USDTprice = Redis::GET("USDTprice");
                        $Model = new mdb\jizhang_qunset;
                        $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
                    }
                     
                    if($res[1] == "-"){  
                        if(empty($qunset['json']['ss'])){
                            $res[2] = round($res[2]/$qunset['json']['huilv'],2);
                        }else{
                            $res[2] = round($res[2]/($USDTprice+$qunset['json']['weitiao']),2);
                        }
                         
                    }else if($res[1] == "+"){   
                        $huilv = $res[3];   
                        //单笔自定义费率后 隐藏未下发RMB显示（否则会出现RMB 和 USDT 对不上等情况）
                        if(empty($qunset['json']['rmbclose'])){
                            $qunsetjson = $qunset['json'];
                            $qunsetjson['rmbclose'] = 1;
                            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>0,'json'=>json_encode($qunsetjson)]);
                            $Model = new mdb\jizhang_qunset;
                            $qunset = $Model->getJSON($message['bot']['API_BOT'],$message['chatId']); 
                        }
                         
                    }
                     
                }
                if(empty($res[2])){
                    $res[1] = "查看账单";
                }
                
            }else{ 
                return $ret;
            }
            
             
            
            
            
            #通用变量
            $date = date("Ymd",$message["tgTime"]); 
            $dateqie = date("YmdHi",$message["tgTime"]); 
            if(empty($qunset)){
                $USDTprice = Redis::GET("USDTprice");
                $Model = new mdb\jizhang_qunset;
                $qunset = $Model->getJSON($message['bot']['API_BOT'],$message['chatId']); 
            }
            
            
 
              
            
            #对号入座
            switch ($res[1]) { 
                default: 
                    return $ret;
                    break;    
                      
                
                case '记账设置': 
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    } 
                    if(empty($qunset['json']['time'])){
                        $qunsetjson = $qunset['json']; 
                        $qunsetjson['time'] = '0000';
                        $qunsetjson['qie'] = '0'; 
                        $dateqie = date("Ymd",$message['tgTime'] + 86400) ;  
                        Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['date'=>$dateqie,'json'=>json_encode($qunsetjson)]); 
                        $ret['sendText'] = "下次日切时间：{$dateqie} - 00:00";
                    }else{ 
                        $ret['sendText'] = "下一账单日期：<b>{$qunset['date']}</b>\n自动切换时间：<code>".substr($qunset['json']['time'],0,2).":".substr($qunset['json']['time'],-2)."</code>";
                    }
                    
                    
                    if(!empty($qunset['json']['qie'])){
                        $qie = "🔲";
                    }else{
                        $qie = "✅";
                    }
                    
                    if(empty($qunset['json']['ztjs'])){
                        $ztjs = "🔲";
                    }else{
                        $ztjs = "✅";
                    }
                    
                    
                    $ret['anniu'] = [
                         
                        [
                            [
                                "text" => "设置日切时间",
                                "callback_data" => "设置日切时间"
                            ]
                        ],
                        [
                            [
                                "text" => "允许每日自动切换账单{$qie}",
                                "callback_data" => "禁止日切"
                            ]
                        ],
                        [
                            [
                                "text" => "昨日未下发 → 转到今日{$ztjs}",
                                "callback_data" => "昨日未结款转移"
                            ]
                        ],
                    ];  
                    
                    if(!empty($qunset['json']['ztjs'])){
                        if(empty($qunset['json']['ztxianshi'])){
                            $ztxianshi = "🔲";
                        }else{
                            $ztxianshi = "✅";
                        }
                       $ret['anniu'][] =  [
                                    [
                                        "text" => "昨日转移款 → 账单显示{$ztxianshi}",
                                        "callback_data" => "昨日转移款显示"
                                    ]
                                ]; 
                    }
                    break; 
                
                case '开始记账':  
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $qunsetjson = $qunset['json']; 
                    $qunsetjson['zt'] =1;
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
                    
                    $Model2 = new model\jizhang_set;
                    $jizhangset = $Model2->getJSON($message['bot']['API_BOT']); 
                    
                    if(empty($jizhangset['json']['开始记账说明'])){
                        $ret['sendText'] = "<b>🟢 记账功能已启动...</b>";
                        
                    }else{
                        $ret['sendText'] = $jizhangset['json']['开始记账说明'];
                    } 
                    return $ret;
                    break; 
                    
                
                case '设置操作人':
                case '添加操作人':
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $value  = str_replace(' ', '', $res[2]); 
                    $caozuolist = explode("@",$value);   
                    $v2 = ""; 
                    foreach ($caozuolist as $val) { 
                        if(strripos($qunset['json']['admin'],"@{$val}")){
                            continue;   
                        } 
                        if(empty($val)){
                            continue;
                        }
                        $v2 .="@{$val} ";
                    }
                    $admin = "{$qunset['json']['admin']}{$v2}"; 
                    $qunsetjson = $qunset['json']; 
                    $qunsetjson['admin'] = $admin;
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]); 
                    $ret['sendText'] = "添加成功,本群记账操作人：\n{$admin}";
                    break;
                    
                case '删除操作人':
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $admin  = str_replace(' ', '', $qunset['json']['admin']); 
                    $caozuolist = explode('@',$admin);  
                    $v1 = "";
                    $v2 = "";   
                    
                    foreach ($caozuolist as $val) {  
                        if(empty($val)){
                            continue;     
                        }
                        
                        if(strripos("#".$res[2],"@{$val}")){ 
                            $v1 .= "@{$val} ";
                            continue;   
                        }
                        $v2 .="@{$val} ";
                    }
                    $qunsetjson = $qunset['json']; 
                    $qunsetjson['admin'] = $v2;
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]); 
                    $ret['sendText'] = "删除成功,本群记账操作人：\n{$qunsetjson['admin']}";
                    break;
                    
                case '查看操作人':  
                    $ret['sendText'] = "<b>本群记账操作人：</b>\n{$qunset['json']['admin']}";
                    break;
                    
                    
                case '停止记账':  
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $qunsetjson = $qunset['json']; 
                    $qunsetjson['zt'] =0;
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
                    $ret['sendText'] = "🔴<b>已停止记账</b>\n\n你可能需要以下命令：\n<code>设置汇率 7.2</code>\n<code>设置费率 1.5</code>\n<code>开始记账</code>\n<code>查看账单</code>\n<code>删除账单</code>";
                    return $ret;
                    break; 
                    
                
                case '撤回': 
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){ 
                        return $ret;
                    }
                    
                    if(empty($message["isHuiFu"])){ 
                        $ret['sendText'] = "<b>⚠️无目标,请回复操作人记账的原始消息进行撤回！</b>";
                        return $ret;
                        break; 
                            
                        // $reddel = Redis::Lpop("{$message['bot']['API_BOT']}{$message['chatId']}inc");
                        // if($reddel){
                        //     mdb\jizhang_list::where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->order("id DESC")->limit(1)->update(["del"=>1]);
                        //     $delinfo = unserialize($reddel); 
                        //     $totalhh  = Db::name('jizhang_totalz')->where('del',0)->where('bot', $delinfo['bot'])->where('date', $delinfo['date'])->where('qunid', $delinfo['qunid'])->order("id DESC")->find();
                        //     if($totalhh){
                        //         Db::name('jizhang_totalz')->where('id',$totalhh['id'])->dec("incnum",1)->dec("incvalue",$delinfo['value'])->dec("incmoney",$delinfo['money'])->update();
                        //     }
                        // }
                        
                    }else{
                        if(!empty($message["HuiFu"]["isBot"])){
                            $ret['sendText'] = "<b>⚠️请回复操作人记账的原始消息进行撤回,而不是回复机器人！</b>";
                            return $ret;
                            break; 
                        }
                        $reply = $message['message']['reply_to_message'];
                        
                        $listok = mdb\jizhang_list::where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("msgid",$reply['message_id'])->find();
                        if(empty($listok)){
                            $ret['sendText'] = "<b>⚠️撤回记账出错了</b> 该笔记账数据不存在！";
                            return $ret;
                            break; 
                        }
                        if($listok['del'] !== 0){
                            $ret['sendText'] = "<b>⚠️撤回记账出错了</b> 该笔记账数据已删除或撤回！";
                            return $ret;
                            break; 
                        }
                        
                        if($listok['date'] != $date){
                            $ret['sendText'] = "<b>⚠️撤回记账失败</b> 无法撤回今日之前的数据！";
                            return $ret;
                            break; 
                        }
                        
                        
                        $totalzR  = Db::name('jizhang_totalz')->where('del',0)->where("tgid",0)->where('bot', $listok['bot'])->where('date', $listok['date'])->where('qunid', $listok['qunid'])->order("id DESC")->find();
                        $totalzZ  = Db::name('jizhang_totalz')->where('del',0)->where("tgid",0)->where('bot', $listok['bot'])->where('date', 0)->where('qunid', $listok['qunid'])->order("id DESC")->find();
                        if(empty($totalzR) || empty($totalzZ)){
                            $ret['sendText'] = "<b>⚠️撤回记账无效</b>\n该笔记账的当日记账数据不存在或已删除！";
                            return $ret;
                            break;
                        }
                        if($listok['fuhao'] == "+"){
                            Db::name('jizhang_totalz')->where('id',$totalzR['id'])->dec("incnum",1)->dec("incvalue",$listok['value'])->dec("incmoney",$listok['money'])->update();
                            Db::name('jizhang_totalz')->where('id',$totalzZ['id'])->dec("incnum",1)->dec("incvalue",$listok['value'])->dec("incmoney",$listok['money'])->update();
                            mdb\jizhang_list::where("id",$listok['id'])->update(['del'=>2]);
                            mdb\jizhang_list::where("id",">",$listok['id'])->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("date",$listok['date'])->dec("yu",$listok['money'])->update();
                            
                            $inc_list = Redis::LRANGE("{$message['bot']['API_BOT']}{$message['chatId']}inc",0,9);  
                            Redis::del("{$message['bot']['API_BOT']}{$message['chatId']}inc"); 
                            $newJson =array();
                            foreach ($inc_list as $value) { 
                                $json =unserialize($value); 
                                if($json['msgid'] != $listok['msgid']){
                                    Redis::RPUSH("{$message['bot']['API_BOT']}{$message['chatId']}inc",serialize($json)); 
                                }  
                            }
                            $base = new Base();
                            $base->sendUrl("/deleteMessage?chat_id={$message['chatId']}&message_id={$reply['message_id']}",0,1);
                            
                            
                        }else{
                            Db::name('jizhang_totalz')->where('id',$totalzR['id'])->dec("decnum",1)->dec("decvalue",$listok['money'])->dec("decmoney",$listok['value'])->update();
                            Db::name('jizhang_totalz')->where('id',$totalzZ['id'])->dec("decnum",1)->dec("decvalue",$listok['money'])->dec("decmoney",$listok['value'])->update();
                            mdb\jizhang_list::where("id",$listok['id'])->update(['del'=>2]);
                            mdb\jizhang_list::where("id",">",$listok['id'])->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("date",$listok['date'])->inc("yu",$listok['value'])->update();
                           
                            $dec_list = Redis::LRANGE("{$message['bot']['API_BOT']}{$message['chatId']}dec",0,9);  
                            Redis::del("{$message['bot']['API_BOT']}{$message['chatId']}dec"); 
                            $newJson =array();
                            foreach ($dec_list as $value) { 
                                $json =unserialize($value); 
                                if($json['msgid'] != $listok['msgid']){
                                    Redis::RPUSH("{$message['bot']['API_BOT']}{$message['chatId']}dec",serialize($json)); 
                                }  
                            }
                            $base = new Base();
                            $base->sendUrl("/deleteMessage?chat_id={$message['chatId']}&message_id={$reply['message_id']}",0,1);
                            
                            
                        }
                         
                        
                        
                        
                        
                        
                         
                        
                        
                    }
                    
                    
                      
                     
                    
                
                case '查看账单':    
                    $Model2 = new model\jizhang_set;
                    $jizhangset = $Model2->getJSON($message['bot']['API_BOT']); 
                     
                    // $totalzR = Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("del",0)->where("tgid",0)->where("date",$date)->cache("{$message['bot']['API_BOT']}{$message['chatId']}totalz{$date}")->find();//查看日账单
                    // if(empty($totalzR)){ 
                    //     $totalzR['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$message['bot']['API_BOT'],'ref'=>$qunset['ref'],'qunid'=>$message['chatId'],"date"=>$date]);//写日账单  
                        
                    //     $totalzZ = Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("del",0)->where("date",0)->where("tgid",0)->cache("{$message['bot']['API_BOT']}{$message['chatId']}totalz0")->find();//查看总账单
                    //     if(empty($totalzZ)){
                    //         $totalzZ['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$message['bot']['API_BOT'],'ref'=>$qunset['ref'],'qunid'=>$message['chatId'],"date"=>0]);//写总账单 
                    //     }
                    // }
                    
                    $list['create_time'] = $message['tgTime'];
                    $list['date'] = $date;
                    $list['dateqie'] = $dateqie;
                    $totalzR['id'] = $this->total($message['bot']['API_BOT'],$message['chatId'],$qunset,$list);
                    
                    $ret['sendText'] = $this->rlist($message['bot']['API_BOT'],$message['chatId'],$totalzR['id'],$qunset['json'],$USDTprice,$jizhangset);
                    if(isset($jizhangset['json']['anniu'])){
                        $ret['anniu'] = $jizhangset['json']['anniu']; 
                    }
                    return $ret;
                    break; 
                    
                case '删除账单': 
                    //权限判断 
                     
                    
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    } 
                    
                     
                    Cache::delete("{$message['bot']['API_BOT']}{$message['chatId']}totalz{$date}");   
                    Redis::del("{$message['bot']['API_BOT']}{$message['chatId']}inc");
                    Redis::del("{$message['bot']['API_BOT']}{$message['chatId']}dec");
                    
                    
                    
                    Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("date",$date)->cache("{$message['bot']['API_BOT']}{$message['chatId']}totalz{$date}")->update(["del"=>1]); 
                    
                    Db::name("jizhang_list")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("date",$date)->update(["del"=>1]); 
                    
                    
                    #删除账单后 - 重置群设定 
                    $qunsetjson = $qunset['json']; 
                    $qunsetjson['rmb'] =0;
                    $qunsetjson['rmbclose']=0;
                    $qunsetjson['ztvalue']=0;
                    $qunsetjson['ztmoney']=0;
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->inc("ref",1)->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
                    $ret['sendText'] = "<b>今日账单已删除⛔️</b>\n\n如非必要请不要随意删除账单,如果是测试使用,建议删除总账单避免记账不正确！";
                    $ret['anniu'] = [
                                [
                                    [
                                        "text" => "点击删除总账单",
                                        "callback_data" => "删除总账单{$message['chatId']}"
                                    ]
                                ],
                            ];
                    return $ret;
                    break; 
                
                
                case '设置实时汇率':   
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $qunsetjson = $qunset['json'];  
                    if(empty($qunsetjson['dangwei'])){
                        $qunsetjson['dangwei'] = 0;
                    }
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    }
                    if(empty($qunsetjson['lei'])){
                        $qunsetjson['lei'] = 'bank';
                    }
                    $text = $this->bijia($qunsetjson,$qunsetjson['lei'],$qunsetjson['dangwei']);    
                    $ret['sendText']= "{$text['text']}\n\n档位价格：{$text['sshuilv']}\n微调价格：{$qunsetjson['weitiao']}\n实时汇率：<b>".$USDTprice+$qunsetjson['weitiao']."</b>";
                    $ret['anniu'] = null; 
                    $ret['anniu'][] = $text['a21'];
                    $ret['anniu'][] = $text['a22'];
                    $ret['anniu'][] = $text['a11'];
                    $ret['anniu'][] = $text['a31'];
                    $ret['anniu'][] = $text['a32'];
                    $ret['anniu'][] = $text['a4'];   
                    #$qunsetjson['ss'] = 1; 
                     
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]);
                    break; 
                    
                case '设置汇率':  
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $qunsetjson = $qunset['json'];   
                    if(empty($res[2]) || $res[2] <= 1){
                        $qunsetjson['huilv'] = 1; 
                        $ret['sendText'] = "<b>本群汇率已设置为1:1 <u>(人民币入账/出账模式)</u></b> ✅";  
                        $qunsetjson['rmb'] =1;
                        $qunsetjson['ss'] = 0;
                        Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>0,'json'=>json_encode($qunsetjson)]);
                        return $ret;
                        break; 
                    } 
                     
                      
                    
                    $qunsetjson['huilv'] = $res[2]; 
                    $qunsetjson['ss'] = 0;
                     
                    
                    $totalx = Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("del",0)->where("tgid",0)->where("date",0)->find();
                    if(!empty($totalx['incnum']) && $qunset['json']['huilv'] != $qunsetjson['huilv']){
                        $qunsetjson['rmbclose'] = 1;//多次设定不同汇率,关闭人民币显示
                    }
                    
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>0,'json'=>json_encode($qunsetjson)]);
                    if($qunsetjson['huilv'] == 1){
                        $ret['sendText'] = "<b>本群汇率已设置为：<u>{$qunsetjson['huilv']}</u></b> ✅";
                    }else{
                       $ret['sendText'] = "<b>本群汇率已设置为：<u>{$qunsetjson['huilv']}</u></b> ✅"; 
                    }
                    return $ret;
                    break; 
                    
                    
                case '设置费率':    
                case '设置手续费':   
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                        return $ret;
                    }
                    $qunsetjson = $qunset['json'];
                    $qunsetjson['shouxufei'] = $res[2]; 
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);  
                    $ret['sendText'] = "✅ <b>本群手续费率已设置为：<u>{$qunsetjson['shouxufei']}%</u></b> \n\n<b>手续费计算规则:</b>\n<code>记账金额 - (记账金额 * 手续费率 ÷ 100)</code>\n\n<b>举例:</b>\n<code>%2b100元 (手续费1.5) 实际记账金额：98.5</code>";
                    return $ret;
                    break; 
                    
                
                 
                    
                    
                    
                case '+':
                case '-':  
                    if(empty($qunset['json']['zt'])){ 
                        return $ret;
                        break; 
                    }
                     $Model2 = new model\jizhang_set;
                     $jizhangset = $Model2->getJSON($message['bot']['API_BOT']); 
                    //权限判断
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){ 
                        return $ret;
                    }
                    
        
                    
                    $list['bot']=$message['bot']['API_BOT'];  
                    $list['msgid']=$message['msgId'];  
                    $list['date']=$date;  
                    $list['dateqie']=$dateqie;  
                    $list['qunid']=$message['chatId'];  
                    $list['tgid']=$message['formId'];  
                    $list['name']=$message['formName'];  
                    $list['user']=$message['formUser'];  
                    $list['userinfo'] = $message["message"];
                    $list['fuhao']=$res[1]; 
                    $list['ref']=$qunset['ref']; 
                    if(empty($qunset['json']['ss'])){
                       $list['huilv']= $qunset['json']['huilv'];  
                    }else{
                       $list['huilv']= $USDTprice+$qunset['json']['weitiao'];  
                    }
                     
                    
                    if($list['fuhao'] == "-"){
                        $list['money']=$res[2];  
                        $list['value']=$res[2]; 
                        $list['money']=round($list['value']*$list['huilv'],2); 
                    }else{ 
                        $list['value']=$res[2]; 
                        if(!empty($huilv)){
                            $list['huilv'] =$huilv ; 
                        } 
                        $list['feilv'] = $qunset['json']['shouxufei'];
                        $list['shouxufei'] = round($res[2]*$qunset['json']['shouxufei']/100,2);
                        $list['money']=round(($list['value'] - $list['shouxufei'])/$list['huilv'],2); 
                    }    
                    
                    $list['create_time']=$message["tgTime"];  
                    if(isset($message["HuiFu"])){
                        $list['reply'] = $message["HuiFu"];
                    }
                    $listok = mdb\jizhang_list::create($list);  
                    $list['id'] = $listok['id'];
                  
                     
                    $totalzID = $this->total($message['bot']['API_BOT'],$message['chatId'],$qunset,$list);
                    $ret['nodel'] = 1;
                    $ret['sendText'] = $this->rlist($message['bot']['API_BOT'],$message['chatId'],$totalzID,$qunset['json'],$USDTprice,$jizhangset);
                    
                    $totalok = Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$message['chatId'])->where("del",0)->where("tgid",0)->where("ref",$qunset['ref'])->find();
                     
                    Db::name('jizhang_list')->where('id', $listok['id'])->update(["yu"=>floatval(round($totalok['incmoney'] - $totalok['decmoney'],2))]);
                    
                    if(isset($jizhangset['json']['anniu'])){
                        $ret['anniu'] = $jizhangset['json']['anniu']; 
                    }
                    return $ret; 
                    break; 
                    
                    
                case '下载账单':  
                    if(!$this->isok($qunset,$message['formUser'],$message['formId'])){ 
                        return $ret;
                    }
                    
                    $user = Db::name("bot_account")->where("del",0)->where("bot",$message['bot']['API_BOT'])->where("tgid",$message["formId"])->find();
                    if(empty($user)){
                       $ret["sendText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
                       $ret["anniu"] = [ 
                                [
                                    [
                                        "text" => "🔴 点击启用机器人",
                                        "url" => "https://t.me/{$message['bot']['API_BOT']}"
                                    ]
                                ],
                            ];
                       return $ret;  
                    }
                    
                    $jsonVal = array();
                    $so =[];
                    array_push($so,"del");
                    array_push($so,'=');
                    array_push($so,0); 
                    
         
                    
                    array_push($so,"bot");
                    array_push($so,'=');
                    array_push($so,$message['bot']['API_BOT']); 
                    
                    array_push($so,"date");
                    array_push($so,'=');
                    array_push($so,$date); 
                    
                    array_push($so,"qunid");
                    array_push($so,'=');
                    array_push($so,$message['chatId']); 
                    
                    $so = array_chunk($so,3);//拆分 
                    #$count = mdb\jizhang_list::where([$so])->field("count(*)as count,count(fuhao='-' or null) as jian,count(fuhao='+' or null) as jia,SUM(shouxufei) as shouxufei,SUM(CASE WHEN fuhao = '+' THEN value ELSE 0 END) AS jiaM,SUM(CASE WHEN fuhao = '-' THEN money ELSE 0 END) AS jianM,SUM(CASE WHEN fuhao = '+' THEN money ELSE 0 END) AS jiaU,SUM(CASE WHEN fuhao = '-' THEN value ELSE 0 END) AS jianU")->find(); 
                    
                    $count = Db::name("jizhang_totalz")->where([$so,["tgid","=",0]])->order('id desc')->find(); 
                    
                    if(empty($count['incnum'])){ 
                        $ret['sendText'] = "今日似乎没有账单哦"; 
                        break; 
                    } 
                    
                    $DataList = mdb\jizhang_list::where([$so])->order('id asc')->select();  
                     
                    
                    $oklist=array(); 
                    foreach ($DataList as $value) {  
                        
                        $json['name'] = $value['name'];
                        $json['user'] = $value['user'];
                        
                        $json['fuhao'] = $value['fuhao'];
                        $json['huilv'] = $value['huilv'];
                        $json['shouxufei'] = $value['shouxufei']; 
                        $json['create_time'] = $value['create_time']; 
                        $json['yu'] = $value['yu']; 
                        
                        if($value['fuhao'] == '-'){
                            $json['fuhao'] = "下发";
                            $json['value'] = -$value['money'];
                            $json['money'] = -$value['value'];
                            
                        }else{
                            $json['fuhao'] = "入款";
                            $json['value'] = $value['value'];
                            $json['money'] = $value['money'];
                        }  
                        
                        if(isset($value['reply']['toName'])){
                            $json['reply'] = "@{$value['reply']['toUser']} {$value['reply']['toName']}";
                        }else{
                            $json['reply'] = "";
                        } 
                        
                        $oklist[] = $json; 
                    } 
                    
                     
                    array_unshift($oklist,[]); //加入空行 
                    
                    $array_push = ['reply'=>"统计：",'name'=>"",'user'=>"",'fuhao'=>"入{$count['incnum']}/出{$count['decnum']}",'huilv'=>$count['incnum']+$count['decnum']."笔",'shouxufei'=>"≈{$count['shouxufei']}元",'value'=>"≈{$count['incvalue']}元",'money'=>"≈{$count['incmoney']}U",'yu'=>""];//定义数组
                    array_unshift($oklist,$array_push); 
                    
                    array_unshift($oklist,[]); //加入空行
                    
                     
                
                    
                    
                    $headings = ['reply'=>'回复Ta','name'=>'操作人','user'=>'用户名','huilv'=>'币价','fuhao'=>'记账类型','value'=>'人民币','shouxufei'=>'扣手续费','money'=>'≈USDT','create_time'=>'记账时间','yu'=>'未下发'];
                    
                    $fileurl = run_path()."/bot/res/down/{$message['chatId']}/";//路径
                    $filename = "账单_{$date}_{$qunset['ref']}.xlsx"; 
                    if(!is_dir($fileurl)){
                      mkdir($fileurl,0755,true);  
                    } 
                    Excel::xlsx($oklist)->headings($headings)->store($fileurl.$filename); 
                
                
                    //Excel::csv($DataList)->download($fileurl.$filename);
                    $sendText = "文件名称：<u>账单_{$date}_{$qunset['ref']}.xlsx</u>\n来自群组：<code>{$message['chatName']}</code>\n群组编号：<code>{$message['chatId']}</code>\n下载时间：<code>".date("Y-m-d H:i:s")."</code>"; 
                    $sendFile = $fileurl.$filename; 
                    
                    $ret['sendText'] = $sendText."\n\n<b>完整账单已私信发送给您,请查收！</b>✅"; 
                    
                    $base = new Base();
                    $base->sendUrl("/sendDocument?chat_id={$message['formId']}&document=file://{$sendFile}&caption=".$sendText,0,1); 
                    break; 
                    
            }
            
            
            
            
        }
        
        
        #代码结束
        return $ret;     
    }
    
    // 202408181714 目前
    // 202408191700 明天
    // 1715
    // 0000
    
    
    
    public function total($bot,$chatId,$qunset,$list){   
        if(!empty($qunset['json']['time'])){
            $ref = $qunset['ref'];
            $xitong = $qunset['date'].$qunset['json']['time'];   
             
            if($xitong <= $list['dateqie'] && empty($qunset['json']['qie'])){ 
                $ref = $qunset['ref']+1;
                $dateqie = date("Ymd",$list['create_time'] + 86400) ; 
                Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$qunset['bot']}_{$qunset['qunid']}_jizhang_qunset")->update(['ref'=>$ref,'date'=>$dateqie]);   
                if(isset($list['id'])){
                    Db::name("jizhang_list")->where("id",$list['id'])->update(['ref'=>$ref,'date'=>$list['date']]);//更新刚才的订单ref    
                }
                $totalzZT = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",0)->where("ref",$qunset['ref'])->find();//取上一账单
            }  
            $totalzR = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",0)->where("ref",$ref)->find();//取日账单
            $totalzZ = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",0)->where("date",0)->cache("{$bot}{$chatId}totalz0")->find();//取总账单 
            if(empty($totalzR) ){
                #昨日账单 剩余未结算金额
                if(!empty($totalzZT)){  
                    if(!empty($qunset['json']['ztjs'])){
                        $rr['incmoney'] = $totalzZT['incmoney'] - $totalzZT['decmoney'];
                        $rr['incvalue'] = $totalzZT['incvalue'] - $totalzZT['decvalue'];
                        $qunsetjson = $qunset['json'];
                        $qunsetjson['ztmoney'] = $rr['incmoney']; 
                        $qunsetjson['ztvalue'] = $rr['incvalue']; 
                        Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$qunset['bot']}_{$qunset['qunid']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);   
                    }else{
                        $qunsetjson = $qunset['json'];
                        $qunsetjson['ztmoney'] = 0; 
                        $qunsetjson['ztvalue'] = 0; 
                        Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$qunset['bot']}_{$qunset['qunid']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);  
                    }
                }
                $rr['bot'] = $bot;
                $rr['ref'] = $ref;
                $rr['qunid'] = $chatId;
                $rr['date'] = $list['date']; 
                $totalzR = mdb\jizhang_totalz::create($rr); 
                
                 
                if(empty($totalzZ)){
                    $totalzZ['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$bot,'ref'=>$ref,'qunid'=>$chatId,"date"=>0]);//创建总账单  
                }
                
                #清理redis 账单缓存
                Redis::del("{$bot}{$chatId}inc");
                Redis::del("{$bot}{$chatId}dec");   
            }
                
        }else{#老版本按时间切换账单
            $ref =$qunset['ref'];
            $totalzR = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",0)->where("date",$list['date'])->cache("{$bot}{$chatId}totalz{$list['date']}")->find();//取日账单
            $totalzZ = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",0)->where("date",0)->cache("{$bot}{$chatId}totalz0")->find();//取总账单
            if(empty($totalzR) ){
                $totalzR['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$bot,'ref'=>$ref,'qunid'=>$chatId,"date"=>$list['date']]);//创建日账单  
            
             
            if(empty($totalzZ)){
                $totalzZ['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$bot,'ref'=>$ref,'qunid'=>$chatId,"date"=>0]);//创建总账单  
            }
                #判断是否设置日切时间
                if(empty($qunset['json']['qie'])){
                    Redis::del("{$bot}{$chatId}inc");
                    Redis::del("{$bot}{$chatId}dec");   
                }  
            }  
        }
        
        if(empty($list['money'])){
           return $totalzR['id']; 
        } 
 
        
        if($list['fuhao'] == "-"){
            Db::name('jizhang_totalz')->where('id', $totalzZ['id'])->inc("decnum",1)->inc("decvalue",$list['value']*$qunset['json']['huilv'])->inc("decmoney",$list['value'])->update();//总
            Db::name('jizhang_totalz')->where('id', $totalzR['id'])->inc("decnum",1)->inc("decvalue",$list['value']*$qunset['json']['huilv'])->inc("decmoney",$list['value'])->update();//日
            Redis::lpush("{$bot}{$chatId}dec",serialize($list));
            Redis::EXPIRE("{$bot}{$chatId}dec",TimeHelper::secondEndToday()); 
            Redis::ltrim("{$bot}{$chatId}dec", 0, 9);
            
            $totalUser = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",$list['tgid'])->where("ref",$ref)->find();
            if(empty($totalUser)){
                $totalUser['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$bot,'ref'=>$ref,'qunid'=>$chatId,"date"=>$list['date'],"tgid"=>$list['tgid'],"user"=>json_encode(['user'=>$list['user'],'name'=>$list['name']])]);
            }
            Db::name('jizhang_totalz')->where('id', $totalUser['id'])->inc("decnum",1)->inc("decvalue",$list['value']*$qunset['json']['huilv'])->inc("decmoney",$list['value'])->update();//总
            
            
            
        }else{ 
            Db::name('jizhang_totalz')->where('id', $totalzZ['id'])->inc("incnum",1)->inc("incvalue",$list['value'])->inc("shouxufei",$list['shouxufei'])->inc("incmoney",$list['money'])->update();//总
            Db::name('jizhang_totalz')->where('id', $totalzR['id'])->inc("incnum",1)->inc("incvalue",$list['value'])->inc("shouxufei",$list['shouxufei'])->inc("incmoney",$list['money'])->update();//日
            Redis::lpush("{$bot}{$chatId}inc",serialize($list));
            Redis::EXPIRE("{$bot}{$chatId}inc",TimeHelper::secondEndToday()); 
            Redis::ltrim("{$bot}{$chatId}inc", 0, 9);
            
            $totalUser = mdb\jizhang_totalz::where("bot",$bot)->where("qunid",$chatId)->where("del",0)->where("tgid",$list['tgid'])->where("ref",$ref)->find();
            if(empty($totalUser)){
                $totalUser['id'] = Db::name('jizhang_totalz')->insertGetId(['bot'=>$bot,'ref'=>$ref,'qunid'=>$chatId,"date"=>$list['date'],"tgid"=>$list['tgid'],"user"=>json_encode(['user'=>$list['user'],'name'=>$list['name']])]);
            } 
            Db::name('jizhang_totalz')->where('id', $totalUser['id'])->inc("incnum",1)->inc("incvalue",$list['value'])->inc("shouxufei",$list['shouxufei'])->inc("incmoney",$list['money'])->update();//日
            
             
        }
        return $totalzR['id'];
        
    }
    
 
    
    
    public function rlist($bot,$chatId,$totalzID,$set,$USDTprice,$jizhangset){//出入款数据展示 
    
        $totalz = Db::name('jizhang_totalz')->where('id', $totalzID)->find();//读取群统计数据  日 
        
        #入
        $inc_list = Redis::LRANGE("{$bot}{$chatId}inc",0,4);
        $inc_text = "";
        foreach ($inc_list as $value) { 
            $json =unserialize($value);
            if(!empty($json)){
                
                if(!empty($json['shouxufei'])){
                    if($json['huilv']>1){
                        #$shouxufei = round($json['value']*$set['shouxufei']/100,2);
                        $jisuan = floatval(round(($json['value'] - $json['shouxufei']) / $json['huilv'],2));
                        $inc_text = "".date("H:i:s",$json['create_time'])." | (".floatval($json['value'])."-{$json['feilv']}%)÷".floatval($json['huilv'])." = ".$jisuan."\n".$inc_text; 
                    }else{
                        #$shouxufei = round($json['value']*$set['shouxufei']/100,2);
                        $jisuan= floatval($json['value']-$json['shouxufei']);
                        $inc_text = "".date("H:i:s",$json['create_time'])." | (".floatval($json['value'])."-{$json['feilv']}%) = {$jisuan}\n".$inc_text; 
                    }
                }else{
                    if($json['huilv']>1){
                        $jisuan = floatval(round($json['value']/$json['huilv'],2));
                        $inc_text = "".date("H:i:s",$json['create_time'])." | ".floatval($json['value'])."÷".floatval($json['huilv'])." = ".$jisuan."\n".$inc_text; 
                    }else{
                        $jisuan= floatval($json['value']);
                        $inc_text = "".date("H:i:s",$json['create_time'])." | %2B".floatval($json['value'])."\n".$inc_text; 
                    }
                }
                
                
                  
                 
                 
            } 
        } 
        #出 
        $dec_list = Redis::LRANGE("{$bot}{$chatId}dec",0,4);
        $dec_text = "";
        foreach ($dec_list as $value) { 
            $json =unserialize($value);
            if(!empty($json)){  
                if($json['huilv']>1){
                    $dec_text = date("H:i:s",$json['create_time'])." | -{$json['value']}U (￥".round($json['value'] * $json['huilv'],2).")\n".$dec_text; 
                }else{
                    $dec_text = date("H:i:s",$json['create_time'])." | -{$json['value']}\n".$dec_text;  
                }
            } 
        }
        // if(empty($inc_text)){
        //     $dec_text = "-";
        // }
        // if(empty($dec_text)){
        //     $dec_text = "-";
        // }
        
        
        if($set['huilv'] > 1){
            $danwei="U"; 
        }else{
            $danwei="元"; 
        } 
        
        #废弃
        // if(!empty($set['shouxufei'])){
        //   $shouxufei = round($totalz['incvalue']*$set['shouxufei']/100,2);
        // }else{
        //   $shouxufei = 0; 
        // }
        
        
        #老版本 固定价格浮动 
        $yxfm = floatval($totalz['incvalue'] - $totalz['shouxufei']); //应下发 RMB
        $yjxfm= floatval(round($totalz['decvalue'],2)); // 已经下发RMB 
        
        $yxf= floatval($totalz['incmoney']);//应下发U
        $yjxf= floatval($totalz['decmoney']);//已下发U 
        
        $weixfm= floatval(round($totalz['incvalue'] - $totalz['decvalue'] - $totalz['shouxufei'],2));//未下发RMB
        $weixf= floatval(round($totalz['incmoney'] - $totalz['decmoney'],2)); //未下发U
         
         
        // #新版本 自动价格浮动 
        // $yxfm = floatval($totalz['incvalue'] - $totalz['shouxufei']); //应下发 RMB
        // $yjxfm= floatval(round($totalz['decvalue'],1)); // 已经下发RMB
        
 
        
        // $yxf= floatval(round($yxfm / $set['huilv'],2));//应下发U
        // $yjxf= floatval(round($totalz['decvalue']/$set['huilv'],2));//已下发U
         
        
        // $weixfm= floatval(round($yxfm - $yjxfm,1));//未下发RMB
        // $weixf= floatval(round($yxf - $yjxf,2)); //未下发U
        
        // if(empty($qunset['json']['ss'])){
        //     $huilvDanwei = "固定";
        // }else{
        //     $huilvDanwei = "实时";
        // }
         
        if(empty($set['rmb'])){//USDT
        
            if(empty($jizhangset['json']['账单风格'])){
                $text = "<pre><code class='language-今日入款（{$totalz['incnum']}笔）'>{$inc_text}</code></pre><pre><code class='language-今日下发（{$totalz['decnum']}笔）'>{$dec_text}</code></pre>"; 
            }else{
                $text = "今日入款（{$totalz['incnum']}笔）\n<code>{$inc_text}</code> \n今日下发（{$totalz['decnum']}笔）\n<code>{$dec_text}</code>"; 
            }
            $text .= "\n\n总入款￥：<code>{$totalz['incvalue']}</code>"; 
            
            if(!empty($set['ztjs']) && !empty($set['ztvalue']) && !empty($set['ztxianshi'])){ 
                    $text .= " (含昨日:<code>{$set['ztvalue']}</code>)";  
            }
            
            
            if($totalz['shouxufei'] > 0){
                $text .= " <code>(-".floatval($totalz['shouxufei']).")</code>";
            }
            
            if($set['shouxufei']>0){
                $text .= "\n手续费率：<code>{$set['shouxufei']}%</code>";  
            } 
            if(empty($set['ss'])){
                $text .= "\n固定汇率：<code>{$set['huilv']}</code>\n";  
            }else{
                $text .= "\n实时汇率：<code>".$USDTprice + $set['weitiao']."</code>\n";  
            }
             
            if(empty($set['rmbclose'])){
                $text .= "\n应下发：{$yxfm} | {$yxf} {$danwei}";
                $text .= "\n已下发：{$yjxfm} | {$yjxf} {$danwei}";
                $text .= "\n未下发：{$weixfm} | <b><u>{$weixf}</u>{$danwei}</b>";
            }else{
                $text .= "\n应下发：{$yxfm} | {$yxf} {$danwei}";
                $text .= "\n已下发：{$yjxf} {$danwei}";
                $text .= "\n未下发：<b><u>{$weixf}</u>{$danwei}</b>";
            }
            
                // $text .= "\n应下发：{$yxfm} | {$yxf} {$danwei}";
                // $text .= "\n已下发：{$yjxfm} | {$yjxf} {$danwei}";
                // $text .= "\n未下发：{$weixfm} | <b><u>{$weixf}</u>{$danwei}</b>";
 
             
        }else{//RMB模式
            if(empty($jizhangset['json']['账单风格'])){
                $text = "<pre><code class='language-今日入款（{$totalz['incnum']}笔）'>{$inc_text}</code></pre><pre><code class='language-今日下发（{$totalz['decnum']}笔）'>{$dec_text}</code></pre>";
            }else{
                $text = "今日入款（{$totalz['incnum']}笔）\n<code>{$inc_text}</code> \n今日下发（{$totalz['decnum']}笔）\n<code>{$dec_text}</code>";
            }
            $text .= "\n\n入款总额：<code>{$totalz['incvalue']}元</code>";
            
            if(!empty($set['ztjs']) && !empty($set['ztvalue']) && !empty($set['ztxianshi'])){ 
                    $text .= " (含昨日:<code>{$set['ztvalue']}</code>)";  
            }
            
            if($set['shouxufei']>0){
                $text .= "\n手续费率：<code>{$set['shouxufei']}%</code>";  
            }
            
            $text .=  "\n固定汇率：<code>{$set['huilv']}</code>\n";
            
            $text .= "\n应下发：{$yxf} {$danwei}"; 
            $text .= "\n已下发：{$yjxf} {$danwei}"; 
            $text .= "\n未下发：{$weixf} {$danwei}";     
        }
        
        
        
        
        if(!empty($jizhangset['value'])){
            $text .= "\n\n".$jizhangset['value'];
        }
        
        return $text;
    }
    
    
    public function isok($qunset,$user,$tgid){    
        if(strripos("#".$qunset['json']['admin'],"@{$user}")){   
            return true;
        }
        $group =  model\bot_group::where("bot",$qunset['bot'])->where('qunid', $qunset['qunid'])->find(); 
      
        if(isset($group['admin'][$tgid])){
            $admin = "";
            foreach ($group['admin'] as $value) { 
                if(!empty($value['user']['username']) && empty($value['user']['is_bot'])){
                    $admin .= "@{$value['user']['username']} "; 
                }
                 
            } 
            $qunsetjson = $qunset['json']; 
            $qunsetjson['admin'] = $admin;
            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$qunset['bot']}_{$qunset['qunid']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
            return true; 
        } 
         
        
        
        return false;
    }
    
    
    
    public function bijia($qunset,$type = 'bank',$dangwei = 2):array{     
        
        $ico=array( '0'=>'1️⃣','1'=>'2️⃣','2'=>'3️⃣' ,'3'=>'4️⃣','4'=>'5️⃣' ,'5'=>'6️⃣','6'=>'7️⃣','7'=>'8️⃣' ,'8'=>'9️⃣'  ,'9'=>'🔟' );
        $icopay = array("all"=>"","bank"=>"","aliPay"=>"","wxPay"=>""); 
        $icopay[$type]="✅";
        $a1 = array();
        array_push($a1,["text"=>"所有{$icopay['all']}","callback_data"=>"选择_all"]);
        array_push($a1,["text"=>"银行卡{$icopay['bank']}","callback_data"=>"选择_bank"]);
        array_push($a1,["text"=>"支付宝{$icopay['aliPay']}","callback_data"=>"选择_aliPay"]);
        array_push($a1,["text"=>"微信{$icopay['wxPay']}","callback_data"=>"选择_wxPay"]);
        $a21 = array();
        $a22 = array(); 
        $a31 = array(); 
        
        $a32 = array(); 
        array_push($a31,["text"=>'减0.1',"callback_data"=>"减少_0.1"]);
        array_push($a31,["text"=>'加0.1',"callback_data"=>"增加_0.1"]);
        array_push($a32,["text"=>'减0.01',"callback_data"=>"减少_0.01"]);
        array_push($a32,["text"=>'加0.01',"callback_data"=>"增加_0.01"]);
        
         
 
        $sshuilv = 7; 
        
        try {  
            $redistype = Redis::get("bijia_{$type}");
            if(empty($redistype)){ 
                $client = new Guzz_Client(['timeout' => 5,'http_errors' => false,'verify' => false]); 
                $res = json_decode($client->request('GET', "https://www.okx.com/v3/c2c/tradingOrders/books?t=*************&quoteCurrency=cny&baseCurrency=usdt&side=sell&paymentMethod={$type}&userType=all&receivingAds=false&urlId=10")->getBody(),true); 
                if(empty($res['requestId'])){
                    return ["code"=>0,"msg"=>$e->getMessage(),"text"=>"获取币价失败A"];  
                }  
            }else{ 
                $res['data']['sell'] = unserialize($redistype);
            }
            $temparr=array();
            $text = ""; 
            for ($i = 0; $i < 10; $i++) { 
                array_push($temparr,$res['data']['sell'][$i]);
                if($i < 5){
                    if($i == $dangwei){ 
                        $sshuilv = $res['data']['sell'][$i]['price'];  
                        array_push($a21,["text"=>$ico[$i]."✅","callback_data"=>"设定_{$res['data']['sell'][$i]['price']}-{$i}"]); 
                    }else{
                        array_push($a21,["text"=>$ico[$i],"callback_data"=>"设定_{$res['data']['sell'][$i]['price']}-{$i}"]); 
                    }
                    
                     
                }else{
                    if($i == $dangwei){ 
                        $sshuilv = $res['data']['sell'][$i]['price'];  
                        array_push($a22,["text"=>$ico[$i]."✅","callback_data"=>"设定_{$res['data']['sell'][$i]['price']}-{$i}"]); 
                    }else{
                        array_push($a22,["text"=>$ico[$i],"callback_data"=>"设定_{$res['data']['sell'][$i]['price']}-{$i}"]); 
                    }
                }
                 
                $text .= "<code>{$ico[$i]}    {$res['data']['sell'][$i]['price']}   {$res['data']['sell'][$i]['nickName']}</code>\n";  
            } 
            if(empty($redistype)){ 
                Redis::setex("bijia_{$type}",120,serialize($temparr)); 
            } 
            $a4 = array(); 
            array_push($a4,["text"=>'确认',"callback_data"=>"确认设定_".$sshuilv + $qunset['weitiao']."-{$sshuilv}"]);
            
            
            return ["code"=>1,"msg"=>"获取成功","text"=>$text,"type"=>$type,"sshuilv"=>$sshuilv,"a11"=>$a1,"a21"=>$a21,"a22"=>$a22,"a31"=>$a31,"a32"=>$a32,"a4"=>$a4]; 
                 
             
            
            
        } catch (\Throwable $e) {   
            //throw new \Exception("获取币价失败".$e->getMessage());   
            return ["code"=>0,"msg"=>$e->getMessage(),"text"=>"获取币价失败B"];  
        }
    }
    
    
} 
