<?php
namespace bot\webapp;

use app\model;                          #模型  
use bot\mdb;                          #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

use Tntma\Tntjwt\Auth;
use support\Request; 

//webapp 电报小程序 无需鉴权 通用登录接口
class index { 
    public function index($request){  
        return json(["code"=>0,"msg"=>"小程序即将上线,版权：www.97bot.com",]);   
    }
     
    
}