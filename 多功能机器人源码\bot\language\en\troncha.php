<?php
return [
    '启动按钮' => "🔍Usdt query", 
    '查询说明' => "<pre><code class='language-Wallet Query Instructions'>Send the wallet address directly in private chat or group chat, and the robot will tell you the detailed information of the address</code></pre>\n<b>Please send the <u>TRC</u> wallet address you want to query:</b>", 
    '查询地址' => "address", 
    'TRX余额' => "TRX", 
    'usdt余额' => "USDT", 
    '质押冻结' => "freeze", 
    '可用能量' => "energy", 
    '可用带宽' => "freeNet", 
    '交易总数' => "PaysCount", 
    '收支比例' => "expenses", 
    '创建时间' => "Created", 
    '最后活跃' => "lastUsed",  
    '分享查询' => "share", 
    '再查一次' => "Query again", 
    '地址无效' => "Sorry, the address you looked up is invalid.", 
    '地址未激活' => "Sorry your address is not activated",  
    '多签授权已多签' => "Multisig：<b>YES</b>🚫", 
    '多签授权未多签' => "Multisig：<b>No</b>✅",  
    '地址类型合约地址' => "address types：<b>contract</b>💹",
    '控制地址' => "owner", 
    '最近交易' => "Recent Transactions", 
    '笔' => "times", 
    '收' => "In", 
    '付' => "Out", 
    '阈值' => "permission", 
    '的钱包查询' => "Wallet query", 
    '监听该地址' => "monitor it", 
   ];