<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>plugin</title>
  <!--系统CSS 你可以把css放到：/bot/res/xxx/目录下然后在下引用 引用只需要：/xxx/你的.css -->
  <link rel="stylesheet" href="/assets/css/diy.css">
  <link rel="stylesheet" href="/assets/css/list2.css">
  <link rel="stylesheet" href="/assets/font/iconfont.css">
  <!--第三方css-->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> 
  <style type="text/css"> 
    [v-cloak] { 
      display: none; 
    } 
  </style>  
</head>
<body>
<div id="app" v-cloak>
<div class="loca"> 
<div class="ele-body">
<el-row :gutter="10" class="row-bg">
<!--********以上代码不懂不要随意修改,下面开始写element vue代码*******-->

    <el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> he444bot设置</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>
        <el-form ref="setform" :model="setForm" label-width="128px" label-suffix="：" style="padding:10px 0">     
            
             

        <el-col :lg="12" :xs="24"> 
            
                <el-form-item label="设置机器人" size="small" prop="value">
                  <el-select v-model="setForm.bot" allow-create filterable placeholder="请选择机器人" @change="remoteSeleok" style="width:180px">
                    <el-option v-for="item in botlist" :key="item" :label="item" :value="item">
                    </el-option>
                  </el-select>  
                </el-form-item>  
                
                
              <!--<el-form-item label="选择框" size="small">-->
              <!--  <el-radio v-model="setForm.json['选择框']" :label="0">选择框1</el-radio>-->
              <!--  <el-radio v-model="setForm.json['选择框']" :label="1">选择框2</el-radio>-->
              <!--</el-form-item>-->
                
              
              <el-form-item   size="small" >
                  <template slot="label">
                     私信验群真群回复
                    <el-tooltip content="私聊机器人发送编号时，真群回复内容">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="当群存在时回复内容" v-model="setForm.json['zhen']"> </el-input> 
              </el-form-item>
              
               <el-form-item   size="small" >
                  <template slot="label">
                    私信验群假群回复
                    <el-tooltip content="私聊机器人发送编号时，假群回复内容">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="当群不存在时回复内容" v-model="setForm.json['jia']"> </el-input> 
              </el-form-item>
              
              <el-form-item   size="small" >
                  <template slot="label">
                    群内验群真群回复
                    <el-tooltip content="群内发送：验群 时真群回复内容,假群不回复的 支持群名称标签：{qunname}">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="群内发送：验群 时真群回复内容,假群不回复的，支持群名称标签：{qunname}" v-model="setForm.json['qunzhen']"> </el-input> 
              </el-form-item>
              
               
              
              
              <!--<el-form-item>-->
              <!--    <template slot="label">-->
              <!--        <span class="lan shuiying">自定义按钮</span>-->
              <!--    </template>-->
              <!-- {include file="index/btn" /} -->
               
              <!-- </el-form-item>--> 
              
              
              
              <div class="juzhong">
                <!--<span class="qianlan ">说明消息 <span class="jiacu hong ">高亮</span> 说明消息</span>-->
              </div>
              
              
               <el-form-item>
                  <!--<el-button  @click="load()">刷新</el-button> -->
                <el-button type="primary" @click="update()">保存设置</el-button>  
              </el-form-item> 
           
        </el-col> 
        
         <el-col :lg="12" :xs="24"> 
         
         
         <el-form-item   size="small" >
                  <template slot="label">
                    官方人员列表
                    <el-tooltip content="1行1个输入官方人员电报号不带@,只有这些客户号可以设置群编号 初始化等(同时私聊机器人发送这些客服号会提示是真官方人员)">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="4" placeholder="1行1个输入官方人员电报号 不带@" v-model="setForm.json['kefu']"> </el-input> 
              </el-form-item>
              
          
              
              
              <el-form-item   size="small" >
                  <template slot="label">
                    初始化消息2
                    <el-tooltip content="群内初始化时提示的消息2">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="群内初始化时提示的消息2" v-model="setForm.json['chushihua2']"> </el-input> 
              </el-form-item>
              
              <el-form-item   size="small" >
                  <template slot="label">
                    初始化消息3
                    <el-tooltip content="群内初始化时提示的消息3">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="群内初始化时提示的消息3" v-model="setForm.json['chushihua3']"> </el-input> 
              </el-form-item>
              
              <el-form-item   size="small" >
                  <template slot="label">
                    钱包地址消息4
                    <el-tooltip content="钱包地址消息4">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="1" placeholder="钱包地址消息4" v-model="setForm.json['chushihua4']"> </el-input> 
              </el-form-item>
              
              <el-form-item   size="small" >
                  <template slot="label">
                    钱包地址截图5
                    <el-tooltip content="钱包地址截图5">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="1" placeholder="钱包地址截图5" v-model="setForm.json['chushihua5']"> </el-input> 
                  
                  <el-image :src="setForm.json['chushihua5']"></el-image>
              </el-form-item>
              
              
              <el-form-item   size="small" >
                  <template slot="label">
                    初始化消息6
                    <el-tooltip content="群内初始化时提示的消息6">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="5" placeholder="群内初始化时提示的消息6" v-model="setForm.json['chushihua6']"> </el-input> 
              </el-form-item>
              
              
              <el-form-item   size="small" >
                  <template slot="label">
                    初始化消息7
                    <el-tooltip content="群内初始化时提示的消息7">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="群内初始化时提示的消息7" v-model="setForm.json['chushihua7']"> </el-input> 
              </el-form-item>
         
         
         
         </el-col> 
        
        
        
        </el-form> 
        
        
 </el-card>
 <br>
    
<el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> 模块使用帮助</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>
  <div class=" ">
      <span class="lan cu">首先该模块仅适用单独使用不适合多模块使用<br>【机器人》个性化设置》<br>消息模式：隐藏<br>隐藏键盘：隐藏<br>消息内容：自己看汇旺的专群机器人的启动内容，照着填进去即可】</span><br>
                <span class="qianlan "><br>1.首先要设置好官方人员客服飞机号<br>2.把机器人拉进群并设置为管理员<br>3.客服飞机号在群内发送：设置编号D1234<br>4.客服飞机号在群内发送：初始化<br><br>【OK】<br>私聊机器人发送群编号可以验证真假群</span> <br>
                <span class="qianlan ">私聊机器人发送客服用户名可以验证真假客服<br>群内可以发送：验群 验证真假</span>
              </div>
</el-card>              
  
    
        
<!--*****************************代码结束*************************-->
</el-row>
</div>
</div>
</div>
</body> 
<!--系统js 你可以把js放到：/bot/res/xxx/目录下然后在下引用 引用时只需要：/xxx/你的.js-->
<script src="/assets/js/vue.js"></script>  
<script src="/assets/js/axios.js"></script> 
<script src="/assets/js/clipboard.js"></script> 
 

<!--第三方js 如果你要加载其它js请填写到下面-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>  
 

<!--VUE js-->
<!--VUE js-->
<script>
var table = 'he444bot';
{literal}
new Vue({
  el: '#app',
  //页面参数定义
  data() {
    return { 
        loading: true,  //加载状态 
        setForm:{json:{}}, 
        botlist:[],
      //btn a  
      editShow: false,
      editBtnForm: {},
      anniu: [],
      eeedit: { index: 0, indexs: 0 },
      ruled: {
        text: [
          { required: true, message: '按钮名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择按钮类型', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '输入点击按钮打开的地址', trigger: 'blur' }
        ],
        callback_data: [
          { required: true, message: '输入点击按钮的回调消息', trigger: 'blur' }
        ],
      },
      //btn b
      
    }
  },
  //数据监听
  watch: {
      
  },
  
  
  
  //初始化1·渲染前
  created() {
      this.load() 
  },
  
  
  
  //初始化2·渲染后
  mounted() { 
      this.loading = true;
        axios.get("/app/appStore/bot/index ",{}).then((res) => {
            this.loading = false;
            this.botlist = res.data.data
            this.botlist.unshift("全局设置")
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        }); 
      
     
  }, 
  
  
  
  //按钮点击事件方法
  methods: { 
    copy(obj,text){
        const clipboard = new ClipboardJS(obj, {
        text: function (trigger) {
                return text;
            }
        });
        clipboard.on('success', e => { 
            clipboard.destroy()
            this.$message.success('复制成功');
            return ;
        });
        clipboard.on('error', e => {
            clipboard.destroy()
            this.$message.error('复制失败'); 
            return ;
        });
    },   
    //以上按钮事件必备
    load(){ 
        axios.get("/web/"+table+"/setget",{params:{bot:this.setForm.bot}}).then((res) => { 
            if(!res.data.data){
                this.$message.error("该机器人没有单独设定,默认使用全局设置");
                return false
            }
            
            
            if (res.data.data.bot == 0 || res.data.data.bot == '0') {
                res.data.data.bot = "全局设置"  
            }
            this.setForm = res.data.data
            if(this.setForm.json.anniu){
                this.anniu = this.setForm.json.anniu;
            }
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });    
    },
    update(){
        
        if(this.anniu){
            this.setForm.json.anniu = this.anniu 
        }
        
        axios.post("/web/"+table+"/update",{type:"set",data:this.setForm}).then((res) => { 
            
            this.$message.success(res.data.msg);
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });  
        
    },
    remoteSeleok(){
       this.load() 
    },
    
    //按钮事件开始
    btnsele(val) {
      if (val == "url") {
        delete this.editBtnForm.callback_data
      } else if (val == "callback_data") {
        delete this.editBtnForm.url
      } 

    },

    addbtn(index) {
      this.anniu[index].push({ text: '新按钮', url: "https://www.97bot.com" });
    },
    editbtn(index, indexs, items) {
      this.editBtnForm = { ...items }
      this.editShow = true
      //记录按钮偏移  后续修改
      this.eeedit.index = index
      this.eeedit.indexs = indexs

    },
    delbtn(id, index, indexs, comId) {
      this.anniu[index].splice(indexs, 1);//删除指定obj 内 第x个元素   
    },
    newHang() {
      this.anniu.push([])
    },
    delHang(index) {
      this.anniu.splice(this.anniu.length - 1, 1);
    },
    editbtnOK() {
      this.$refs['editBtnForm'].validate((valid, obj) => {
        if (valid) {
          this.anniu[this.eeedit.index][this.eeedit.indexs] = this.editBtnForm
          this.editShow = false
        }
      })

    },
    //按钮事件结束
     
  }, 
  
  
  
  //离开页面前事件
  beforeDestroy () {
  },
})
{/literal}
</script>
</html>