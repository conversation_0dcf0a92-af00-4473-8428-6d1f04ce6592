<?php
namespace bot\_info; 

 
class niucount  {  
    public function index(){    
         
        $info['core']       = 240223;
        
        $info['lv']         = 53;
           
        $info['key']        = "niucount";
         
        $info['name']       = "xin定制牛牛记账消口";
        
        $info['text']       = "此模块为用户定制的打牛牛记账每4口赢2则消2 - 普通用户勿购买"; 
         
        $info['version']    = "1.0";
        
        $info['new']        = "未添加说明";
        
        $info['menuId']     = [316,317,318];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 365; 
        
        $info['autobuy']    = 0;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["tu1.png", "tu2.png", "tu3.png", "tu4.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "jXWY3vrBtrj4YmiSjWO5B2ZvOTBIaiE74N1SzVkpaUjSZB/pmfsaUs7W/gXF2qoaf9hjqw30uF5QP1cNEVCbMPjaP1paI7YjUPYZMdJQbbUcIULFnxW7zMJ1fiJx78IoIRIzST2amOSpglxUIxZozgbKaJm7LOUYuMActQaSdAk=";
 
        $info['tables']     = ["niucount_set", "niucountlist", "niucountbak"];
        
        $info['btn']       = "";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      