<?php
namespace bot\web;

use app\model;                          #模型  
use bot\mdb;                          #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

use support\Request; 

//后端api文件 GET无需鉴权，POST需jwt鉴权(自动的不用管) 
class tgvips { 
   
    public function index(Request $request){
        $name = "tgvip 的 index 控制器方法,返回的文本字符"; 
        return response("你好这是：" . $name);
    } 
    
    #function 对应后台模块 >设置菜单>子菜单 中的（api方法）
    public function list(Request $request){   
        return views( "/tgvips/list", ["name" => "97bot"]); #说明：views返回视图html，其中的list代表： bot/web/tgvip/list.html
    }
    
    #一个默认的获取对应模块的配置设置视图 
    public function set(Request $request){   
        return views( "/tgvips/set", ["name" => "97bot"]); #说明：views返回视图html，其中的set代表： bot/web/tgvip/set.html
    }
     
    
    //获取：bot_account数据表,数据接口的例子 GET 
    public function api_list(Request $request){  
        $data = $request->get();
        $page = ($data["page"]-1)*$data["limit"]; 
        $jsonVal = array();
        $so =[];  
          
        //前置条件id字段大于0
        array_push($so,"id");
        array_push($so,'>');
        array_push($so,0); 
           
          
        //按状态匹配数据 
        if(!empty($data["state"])){
            if($data["state"] == 1){
              array_push($so,"payzt");
              array_push($so,"=");
              array_push($so,1);   
            }else{
              array_push($so,"payzt");
              array_push($so,"=");
              array_push($so,0);  
            } 
     
        } 
        //按字段进行模糊搜索数据
        if(!empty($data["keyword"])){
           array_push($so,$data["t"]?$data["t"]:"bot");
           array_push($so,"like");
           array_push($so,"%".$data["keyword"]."%");
        }
          
        //定义事件日期的搜索
        if(!empty($data["timea"])){
           array_push($so,"create_time");
           array_push($so,">");
           array_push($so,substr($data["timea"],0,10));
           array_push($so,"create_time");
           array_push($so,"<");
           array_push($so,substr($data["timeb"],0,10));
        }
          
        $so = array_chunk($so,3);//拆分  
        
        $count = model\tgvips_list::where([$so])->count(); 
        $list = model\tgvips_list::where([$so])->limit($page,$data["limit"])->order("id desc")->select();  
     
        $jsonVal["count"] = $count;
        $jsonVal["list"] = $list;   
        return json(["code" => 1,"msg"=>"请求成功","data" => $jsonVal ]);  
    }
    
    
    #获取机器人配置 
    public function setget(Request $request){   
        $data = $request->get();  
        if(empty($data['bot'])){
            $tgvip_set =  model\tgvips_set::where('id', 1)->find();  
        }else{
            if($data['bot'] == "全局设置"){
                $data['bot'] = 0;
            }
            $tgvip_set =  model\tgvips_set::where('bot', $data['bot'])->find();  
        } 
        return json(["code" => 1,  "msg" => "获取成功","data"=>$tgvip_set]);  
    }
    
    
    #获取钱包地址
    public function getaddress(Request $request){   
        // $tgvip_set =  model\tgvips_set::where("id",1)->find();
        // if(empty($tgvips['json']['word'])){
        //   $tonkey = trim(getenv("TON_Private_Key"));  
        // }
        // $io = $GLOBALS['Gate']->call("Gate.Tonaddress","111");  
          return json(["code" => 1,  "msg" => "获取成功","data"=>1]);  
    }
    
    
    //通用的post新建数据接口
    public function create(Request $request){  
        $data = $request->post();
        return json(["code" => 1, "msg" => "新建成功" ,"data"=>"返回给前端的数据"]);
    }
    
    
    //通用的post数据更新接口
    public function update(Request $request){   
        $data = $request->post();
        if(empty($data['type'])){
            return json(["code" => 0,  "msg" => "缺少更新方法标识：type"]);  
        }
        $data["data"]['bot'] = 0;
        switch ($data['type']) {
            default:
                return json(["code" => 0,  "msg" => "更新方法：{$data['type']} 未写代码,请在/bot/web/tgvip.php文件的function update 下增加一个case方法"]);  
                break;
                
                
                
            case 'set':
                if(empty($data["data"]['json']['word'])){
                    return json(["code" => 0, "msg" => "助词器不能为空" ,"address"=>""]);
                }
                if(count(explode(" ",$data["data"]['json']['word'])) == 24){
                    $rkey = $GLOBALS['Gate']->call("Gate.Enkeys",$data["data"]['json']['word']);
                    if(empty($rkey['code'])){
                        return json(["code" => 0,  "msg" => "加密私钥失败"]); 
                    }
                    $data["data"]['json']['word'] = $rkey['data'];  
                   #return json(["code" => 0, "msg" => "助词器错误,正确应该为24个单词" ,"address"=>""]); 
                }
                if(empty($data["data"]['json']['address'])){
                    return json(["code" => 0, "msg" => "请先填写助词器并获得钱包地址" ,"address"=>""]); 
                } 
                
                $tgvip_set =  model\tgvips_set::where("id",1)->find();
                
                if(!empty($tgvips['json']['word'])){
                    if(md5($tgvips['json']['word']) != md5($data["data"]['json']['word'])){ 
                        $ton = $GLOBALS['Gate']->call("Gate.Tonserver",$data["data"]['json']['word']); 
                    }
                } 
                
                // $httpVip = httpVip($data["data"]['json']['cookie']); 
                // $response = $httpVip->get("/my/sessions"); 
                // $json =   $response->getBody()->getContents();    
                // $addss = pipei($json,'head\\">([A-Za-z0-9]{15})');
                // if(empty($addss[1])){
                //   # return json(["code" => 0, "msg" => "获取cookie中的地址失败了" ,"address"=>""]);
                // }
                $addss2 = $GLOBALS['Gate']->call("Gate.Tonaddress","");
                if(empty($addss2['address'])){
                    $ton = $GLOBALS['Gate']->call("Gate.Tonserver",$data["data"]['json']['word']);
                    if(empty($ton["code"])){
                        return json(["code" => 0, "msg" => "请重新填写助词器" ,"address"=>""]);
                    }    
                }
                // $a1 = substr($addss[1], 3, 10);
                // $a2 = substr($addss2['address'], 3, 10); 
                // if($a1 != $a2){
                //   # return json(["code" => 0, "msg" => "cookie中登录的钱包地址和助词器钱包地址不符" ,"address"=>""]);
                // } 
                
                $tgvip_set->save($data["data"]);  
                Cache::delete("tgvips_0"); 
                break; 
                
                
            case 'setword': 
                if(empty($data['word'])){
                    return json(["code" => 0, "msg" => "助词器不能为空" ,"address"=>""]);
                }
                if(count(explode(" ",$data['word'])) == 24){
                    $rkey = $GLOBALS['Gate']->call("Gate.Enkeys",$data['word']);
                    if(empty($rkey['code'])){
                        return json(["code" => 0,  "msg" => "加密私钥失败"]); 
                    }
                    $data['word'] = $rkey['data'];  
                    #return json(["code" => 0, "msg" => "助词器错误,正确应该为24个单词" ,"address"=>""]); 
                }
                
                $tgvips = model\tgvips_set::where('bot', 0)->cache("tgvips_0")->find();  
                if(!empty($tgvips['json']['word'])){
                    if(md5($tgvips['json']['word']) == md5($data['word'])){ 
                        $ton = $GLOBALS['Gate']->call("Gate.Tonaddress","");
                        if(isset($ton['address'])){
                            return json(["code" => 1, "msg" => "获取成功cache" ,"address"=>$ton["address"]]);
                        }  
                    }
                }  
                Cache::delete("tgvips_0");
                $ton = $GLOBALS['Gate']->call("Gate.Tonserver",$data['word']);
                if(!empty($ton["code"])){
                    return json(["code" => 1, "msg" => "获取成功" ,"address"=>$ton["address"]]);  
                } 
                
                return json(["code" => 0, "msg" => "获取失败" ,"address"=>$ton["message"]]);
                break; 
        }
        
        return json(["code" => 1, "msg" => "更新成功" ,"data"=>"返回给前端的数据"]);
    }
    
    
    //通用的post删除数据接口
    public function del(Request $request){  
        return json(["code" => 1, "msg" => "删除成功" ,"data"=>"返回给前端的数据"]);
    }
    
 
    
    
}