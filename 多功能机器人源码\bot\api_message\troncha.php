<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列
 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;


use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

class troncha  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     * 
     * $ret支持回调参数：sendText(发送文字讯息) sendPhoto(发送照片) sendVideo(发送视频)  anniu(消息按钮)   [ jianpan(回复键盘) && jianpanText(文字消息) || jianpanPhoto(照片消息)]
     * 
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret['key']=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret['level']=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面 
        if(!isset($message['bot']['appStore'][$ret['key']])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return $ret; 
                }
            }
        }
        #-----------------以上核心代码勿动 level 视情况调整改动--------------------------
        
        
        #下面开始写代码
        
    

        if($message["isHuiFu"]){  
            return $ret;  //回复内容过滤
        }
        if($message['text'] == trans('启动按钮', [], 'troncha')){
            $ret['sendText'] = trans('查询说明', [], 'troncha');
        }
        
        if(preg_match('/^T\w{33}$/u', $message['text'])){ 
            
            $text =  Redis::GET("{$message['text']}_addr"); 
            if(empty($text)){ //没有缓存全新请求查询
                $formtext = "\n\n<b><a href='tg://user?id={$message['formId']}'> @{$message['formName']}</a> ".trans('的钱包查询', [], $ret["key"])."</b>\n\n";
                $arrtext = [
                    trans('查询地址', [], $ret["key"])=>'<a href="https://tronscan.org/#/address/'.$message['text'].'">'.substr ($message['text'], 0,4).'...'.substr ($message['text'], 24).'</a>',
                    trans('TRX余额', [], $ret["key"])=>0,  
                    trans('usdt余额', [], $ret["key"])=>0,   
                    trans('质押冻结', [], $ret["key"])=>0,   
                    trans('可用能量', [], $ret["key"])=>"0 / 0",   
                    trans('可用带宽', [], $ret["key"])=>"0 / 0",   
                    trans('交易总数', [], $ret["key"])=>"0 / 0",   
                    trans('收支比例', [], $ret["key"])=>"0 / 0",   
                    trans('创建时间', [], $ret["key"])=>'未知',   
                    trans('最后活跃', [], $ret["key"])=>'未知',    
                ];  
                
                $tronkey = getenv("tronGrid_APIkey");
                if(empty($tronkey) || strlen($tronkey) < 20){
                    $client = new Guzz_Client(['timeout' => 8,'http_errors' => false,'verify' => false]);  
                }else{
                    $client = new Guzz_Client(['timeout' => 8,'http_errors' => false,'verify' => false,'headers' => ['TRON-PRO-API-KEY' => $tronkey]]);  
                }
                
                $scankey = getenv("TRONSCAN_APIKEY");
                if(empty($scankey) || strlen($scankey) < 20){
                    $TRONSCANclient = new Guzz_Client(['timeout' => 8,'http_errors' => false,'verify' => false]);
                }else{
                    $TRONSCANclient = new Guzz_Client(['timeout' => 8,'http_errors' => false,'verify' => false,'headers' => ['TRON-PRO-API-KEY' => $scankey]]);
                } 
                
                $oklinkClient = new Guzz_Client(['timeout' => 5,'http_errors' => false,'verify' => false,'headers' => ['X-Apikey' => okApikey("-b31e-4547-9299-b6d07b7631aba2c903cc") ]]);
                
                $promises = [
                    'trongrid' => $client->getAsync("https://api.trongrid.io/v1/accounts/{$message['text']}"),
                    'tronscan'   => $TRONSCANclient->getAsync("https://apilist.tronscan.org/api/account?address={$message['text']}"),
                    'transactions'   => $TRONSCANclient->getAsync("https://apilist.tronscanapi.com/api/token_trc20/transfers?limit=6&start=0&sort=-timestamp&count=true&filterTokenValue=0&relatedAddress={$message['text']}"),
                //   'oklink'   => $oklinkClient->getAsync("https://www.oklink.com/api/tracker/c/v1/r1/address/detail?address={$message['text']}&chain=TRX&tokenContractAddress=TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"),
                    
                ];
                $results = Guzz_Promise\unwrap($promises);//并发异步请求
                
                if(isset($results['trongrid'])){  
                    $res = json_decode($results['trongrid']->getBody()->getContents(),true);  
                }
                if(isset($results['tronscan'])){  
                    $tronscan = json_decode($results['tronscan']->getBody()->getContents(),true); 
                }
                if(isset($results['transactions'])){  
                    $transactions = json_decode($results['transactions']->getBody()->getContents(),true);  
                }
                if(isset($results['oklink'])){  
                    $oklink = json_decode($results['oklink']->getBody()->getContents(),true);   
                }  
                 
                if(empty($res['success'])){  
                   $_text= str_replace("=", "：",http_build_query($arrtext, '', "\n")); 
                   $ret['sendText'] = "{$formtext}<b>".trans('地址无效', [], $ret["key"])."\n\n</b>{$_text}"; 
                   return $ret;
                }
                if(count($res['data']) < 1){  
                    $_text= str_replace("=", "：",http_build_query($arrtext, '', "\n")); 
                    $ret['sendText'] = "{$formtext}<b>".trans('地址未激活', [], $ret["key"])."\n\n</b>{$_text}"; 
                    return $ret;
                } 
                
                if(empty($res['data'][0]['balance'])){
                    $res['data'][0]['balance'] = 0;
                } 
                 
                $arrtext[trans('TRX余额', [], $ret["key"])] = "<b>".($res['data'][0]['balance'] / 1000000)."</b>";
                foreach ($res['data'][0]['trc20'] as $key=>$value) { 
                    if(!empty($value['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'])){
                        $arrtext[trans('usdt余额', [], $ret["key"])] = "<b>".($value['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'] / 1000000)."</b>";   
                        break;
                    }   
                }
                
                if(!empty($res['data'][0]['account_resource']['frozen_balance_for_energy']['frozen_balance'])){
                    $arrtext[trans('质押冻结', [], $ret["key"])] = "<b>".($res['data'][0]['account_resource']['frozen_balance_for_energy']['frozen_balance'] / 1000000)."</b>";  
                }
                
                if(empty($tronscan['bandwidth']['energyRemaining'])){
                    $tronscan['bandwidth']['energyRemaining'] = 0;
                }
                if(empty($tronscan['bandwidth']['energyLimit'])){
                    $tronscan['bandwidth']['energyLimit'] = 0;
                } 
                if(empty($tronscan['bandwidth']['netRemaining'])){
                    $tronscan['bandwidth']['netRemaining'] = 0;
                }
                if(empty($tronscan['bandwidth']['freeNetRemaining'])){
                    $tronscan['bandwidth']['freeNetRemaining'] = 0;
                }
                if(empty($tronscan['bandwidth']['netLimit'])){
                    $tronscan['bandwidth']['netLimit'] = 0;
                }
                if(empty($tronscan['bandwidth']['freeNetLimit'])){
                    $tronscan['bandwidth']['freeNetLimit'] =0;
                }
                if(empty($tronscan['transactions'])){
                   $tronscan['transactions'] = 0; 
                }
                if(empty($tronscan['transactions_in'])){
                   $tronscan['transactions_in']=0; 
                }
                if(empty($tronscan['transactions_out'])){
                   $tronscan['transactions_out'] =0; 
                }
                 
                 
                $arrtext[trans('可用能量', [], $ret["key"])]   = $tronscan['bandwidth']['energyRemaining']." / ".$tronscan['bandwidth']['energyLimit'];
                $arrtext[trans('可用带宽', [], $ret["key"])]   = $tronscan['bandwidth']['netRemaining']+$tronscan['bandwidth']['freeNetRemaining']." / ".$tronscan['bandwidth']['netLimit']+$tronscan['bandwidth']['freeNetLimit'];
                $arrtext[trans('交易总数', [], $ret["key"])]   = "<b>{$tronscan['transactions']}</b> ".trans('笔', [], $ret["key"]); 
                $arrtext[trans('收支比例', [], $ret["key"])]   = trans('收', [], $ret["key"])."<b>{$tronscan['transactions_in']}</b> / ".trans('付', [], $ret["key"])."<b>{$tronscan['transactions_out']}</b>";  
                
                 
                
                if(!empty($res['data'][0]['create_time'])){ 
                    $arrtext[trans('创建时间', [], $ret["key"])] = date("Y-m-d H:i:s",substr ($res['data'][0]['create_time'],0,10));
                }
                if(!empty($res['data'][0]['latest_opration_time'])){ 
                    $arrtext[trans('最后活跃', [], $ret["key"])] = date("Y-m-d H:i:s",substr ($res['data'][0]['latest_opration_time'],0,10));
                } 
                
                $_text= str_replace("=", "：",http_build_query($arrtext, '', "\n")); 
                
                
                if(isset($oklink['data']['totalRecieved'])){  
                    $_text   .= "\n\n来往地址：<b>".$oklink['data']['inputAddressCount'] + $oklink['data']['outputAddressCount']."</b> 个"; 
                    $_text   .= "\n收入总数：<b>".round($oklink['data']['totalRecieved'])."</b> USDT"; 
                     
                }
                
                if(isset($tronscan['ownerPermission']['threshold'])){
                    if($tronscan['ownerPermission']['threshold'] == 1){
                        if($tronscan['ownerPermission']['keys'][0]['address'] != $message['text']){
                            $_text .= "\n\n".trans('多签授权已多签', [], $ret["key"]);
                            $_text .= "\n".trans('控制地址', [], $ret["key"])."：<code>{$tronscan['ownerPermission']['keys'][0]['address']}</code>";
                        }else{
                            $_text .= "\n\n".trans('多签授权未多签', [], $ret["key"]);
                        }
                            
                    }else{
                        $_text .= "\n\n".trans('多签授权已多签', [], $ret["key"])."<b>[{$tronscan['ownerPermission']['threshold']}]</b>";
                        foreach ($tronscan['ownerPermission']['keys'] as $owner) {
                            //$_text .= "\n阈值{$owner['weight']}：".'<a href="https://tronscan.org/#/address/'.$owner['address'].'">'.substr ($owner['address'], 0,4).'...'.substr ($owner['address'], 24).'</a>';
                            $_text .= urlencode("\n".trans('阈值', [], $ret["key"])."{$owner['weight']}：<a href=\"https://tronscan.org/#/address/{$owner['address']}\">".substr ($owner['address'], 0,4)."...".substr ($owner['address'], 24)."</a>");
                        } 
    
                    } 
                }else{
                    $_text .= "\n\n".trans('地址类型合约地址', [], $ret["key"]);
                }
                
                if(!empty($transactions['token_transfers']) ){
                    $_text .= "\n \n".trans('最近交易', [], $ret["key"])."：";
                    foreach ($transactions['token_transfers'] as $trans) {
                        if($trans['event_type'] != 'Transfer'){
                            continue;
                        }
                        
                        $dath = substr($trans['block_ts'],0,10);
                        if($trans['from_address'] == $message['text']){
                            $fuhao = '－';
                        }else{
                            $fuhao = '＋';
                        } 
                        if(isset($trans['tokenInfo']['tokenAbbr'])){
                        $_text .= urlencode("\n<code>".date("m-d H:i:s",$dath)."</code> [{$trans['tokenInfo']['tokenAbbr']}]<a href=\"https://tronscan.org/#/transaction/{$trans['transaction_id']}\">{$fuhao}".number_format($trans['quant']/pow(10, $trans['tokenInfo']['tokenDecimal']),2)."</a>");
                        }
                    }
                    
                }
                
                 
                 
                
                
                  
                
                
                 
                
                
                
                Redis::SETEX("{$message['text']}_addr",30,serialize("{$formtext}{$_text}")); //记录缓存30s 查询1次
                $ret['sendText'] = "{$formtext}{$_text}"; 
            }else{
                //返回缓存的数据
                $ret['sendText'] = unserialize($text);
            }
                $ret['anniu'] = [
                                // [
                                //     [
                                //         "text"=>"💡 交易概况",
                                //         "callback_data"=> "查询TRC钱包|交易对手|".$message['text']
                                //     ],
                                //     [
                                //         "text"=>"📯 天眼数据",
                                //         "callback_data"=>"查询TRC钱包|详细查询|".$message['text']
                                //     ] 
                                    
                                // ],
                                [
                                    [
                                        "text"=>trans('分享查询', [], $ret["key"]),
                                        "switch_inline_query"=>$message['text']
                                    ],
                                    [
                                        "text"=>trans('监听该地址', [], $ret["key"]),
                                        "switch_inline_query_current_chat"=> $message['text']
                                    ]
                                    
                                ],
                            ]; 
            
                // $ret['anniu'] = [
                //                 [
                //                     [
                //                         "text"=>trans('分享查询', [], $ret["key"]),
                //                         "switch_inline_query"=>$message['text']
                //                     ],
                //                     [
                //                         "text"=>trans('再查一次', [], $ret["key"]),
                //                         "switch_inline_query_current_chat"=>$message['text']
                //                     ]
                                    
                //                 ],
                //                 [
                //                     [
                //                         "text"=>trans('切换语言'),
                //                         "callback_data" => "切换多语言"
                //                     ],
                //                     [
                //                         "text"=>trans('监听该地址', [], $ret["key"]),
                //                         "callback_data" => "监听该地址"
                //                     ]
                                    
                //                 ],
                //             ];     
                return $ret;
        } 
        
        
        
       return $ret;  
    }
} 
