<?php
namespace bot\api_callback;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

use Box\Spout\Writer\Common\Creator\WriterEntityFactory;
use Box\Spout\Writer\Common\Creator\Style\StyleBuilder;
use Box\Spout\Common\Entity\Style\Color;
use Dcat\EasyExcel\Excel;
 
 
class jizhang  {  
    /**
     * 【参数解答】
     * $message["message"]      =   电报原始消息
     * $message["bot"]          =   机器人配置信息
     * $message["callId"]       =   点击唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $message["text"]         =   消息文字内容
     * $message["time"]         =   消息到达-服务器时间戳
     * $message["msgTime"]      =   消息发布时间戳  电报官方时间
     * $message["editTime"]     =   消息最后编辑时间戳 0未编辑过
     * 
     * $message["btnData"]      =   消息按钮对应 消息事件
     * 
     * $message["gamaId"]       =   游戏标识ID   
     * $message["gameName"]     =   游戏唯一标识 游戏才有
     
     * $message["photo"]        =   点击按钮时消息内容中-如果有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   点击按钮时消息内容中-有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   点击按钮时消息内容中-有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   点击按钮时消息内容中-有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $ret返回参数：
     * alert (最高优先级 - 下面参数无效)
     * sendText(发送文字讯息) editText(编辑消息内容)  必须2选1
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     * anniu(消息按钮)
     * jianpan(键盘按钮格式和anniu一样) + jianpanText(文本消息) 或 jianpanPhoto(发送照片) （第2优先级 - 下面参数无效）
     * huifu=1(强制用户回复) huifuTips(回复提示文字)  
     *
     * next 储存到action用于消息交互,比如要求用户输入地址,金额, 下一条消息内容将转储到actionText中
     * huifu=1(强制用户回复) huifuTips(回复提示文字) 如非必要 不建议使用       
     * delMessage=1 代表删除被回复消息 
     * nodel=1 代表即将发送的消息禁止被自动删除 
     * nextdel 代表删除action交互动作
     * back=0 禁止增加返回|删除按钮(back=-1 代表保持返回按钮但跳过按钮消息记录) 
     *  
     */
     
    
    #默认执行函数 
    public function index($message){ 
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        #------------------------以上代码默认请勿修改 level 视情况调整改动--------------------- 
        if($message['chatType'] != "supergroup"){
            return $ret;
        }
        
        
        
        
        if($message['btnData']  == '记账帮助'){
            $ret['delMessage'] =1;//删除当前消息
            $ret['sendText'] = "记账机器人说明";
            $ret['sendPhoto'] = run_path() ."/bot/res/jizhang/jizhang.png";
            $ret['nodel']=1;//禁止删除稍后发送的消息
            return $ret;
            
        }else if($message['btnData']  == '禁止日切'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            $qunsetjson = $qunset['json'];
            if(empty($qunset['json']['qie'])){ 
                $qunsetjson['qie'] = 1;  
            }else{ 
                $qunsetjson['qie'] = 0; 
            } 
            if(empty($qunsetjson['qie'])){
                $qie = "✅ ";
            }else{
                $qie = "🔲";
            }
            
            if(empty($qunset['json']['ztjs'])){
                $ztjs = "🔲";
            }else{
                $ztjs = "✅";
            }
            
             
            
            
            
            $ret['editText'] = "下一账单日期：<b>{$qunset['date']}</b>\n自动切换时间：<code>".substr($qunset['json']['time'],0,2).":".substr($qunset['json']['time'],-2)."</code>";
            $ret['anniu'] = [ 
                        [
                            [
                                "text" => "设置日切时间",
                                "callback_data" => "设置日切时间"
                            ]
                        ],
                        [
                            [
                                "text" => "允许每日自动切换账单{$qie}",
                                "callback_data" => "禁止日切"
                            ]
                        ],
                        [
                            [
                                "text" => "昨日未下发 → 转到今日{$ztjs}",
                                "callback_data" => "昨日未结款转移"
                            ]
                        ],
                    ];
                    
            if(!empty($qunset['json']['ztjs'])){
                if(empty($qunset['json']['ztxianshi'])){
                    $ztxianshi = "🔲";
                }else{
                    $ztxianshi = "✅";
                }
               $ret['anniu'][] =  [
                            [
                                "text" => "昨日转移款 → 账单显示{$ztxianshi}",
                                "callback_data" => "昨日转移款显示"
                            ]
                        ]; 
            }          
            $ret['back'] = 0; 
            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
            return $ret;
        
            
        }else if($message['btnData']  == '昨日未结款转移'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            $qunsetjson = $qunset['json']; 
            
            if(empty($qunset['json']['ztjs'])){ 
                $qunsetjson['ztjs'] = 1;  
            }else{ 
                $qunsetjson['ztjs'] = 0; 
            } 
            
            if(!empty($qunset['json']['qie'])){
                $qie = "🔲";
            }else{
                $qie = "✅";
            }
            
            if(empty($qunsetjson['ztjs'])){
                $ztjs = "🔲";
            }else{
                $ztjs = "✅";
            }
             
            
            
            $ret['editText'] = "下一账单日期：<b>{$qunset['date']}</b>\n自动切换时间：<code>".substr($qunset['json']['time'],0,2).":".substr($qunset['json']['time'],-2)."</code>";
            $ret['anniu'] = [ 
                        [
                            [
                                "text" => "设置日切时间",
                                "callback_data" => "设置日切时间"
                            ]
                        ],
                        [
                            [
                                "text" => "允许每日自动切换账单{$qie}",
                                "callback_data" => "禁止日切"
                            ]
                        ],
                        [
                            [
                                "text" => "昨日未下发 → 转到今日{$ztjs}",
                                "callback_data" => "昨日未结款转移"
                            ]
                        ],
                    ];
            
            if(!empty($qunsetjson['ztjs'])){
                if(empty($qunset['json']['ztxianshi'])){
                    $ztxianshi = "🔲";
                }else{
                    $ztxianshi = "✅";
                }
               $ret['anniu'][] =  [
                            [
                                "text" => "昨日转移款 → 账单显示{$ztxianshi}",
                                "callback_data" => "昨日转移款显示"
                            ]
                        ]; 
            }     
                    
            $ret['back'] = 0; 
            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
            return $ret;
            
        }else if($message['btnData']  == '昨日转移款显示'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            $qunsetjson = $qunset['json']; 
            
            if(empty($qunset['json']['ztxianshi'])){ 
                $qunsetjson['ztxianshi'] = 1;  
            }else{ 
                $qunsetjson['ztxianshi'] = 0; 
            } 
            
            if(!empty($qunset['json']['qie'])){
                $qie = "🔲";
            }else{
                $qie = "✅";
            }
            
            if(empty($qunset['json']['ztjs'])){
                $ztjs = "🔲";
            }else{
                $ztjs = "✅";
            }
             
            
            
            $ret['editText'] = "下一账单日期：<b>{$qunset['date']}</b>\n自动切换时间：<code>".substr($qunset['json']['time'],0,2).":".substr($qunset['json']['time'],-2)."</code>";
            $ret['anniu'] = [ 
                        [
                            [
                                "text" => "设置日切时间",
                                "callback_data" => "设置日切时间"
                            ]
                        ],
                        [
                            [
                                "text" => "允许每日自动切换账单{$qie}",
                                "callback_data" => "禁止日切"
                            ]
                        ],
                        [
                            [
                                "text" => "昨日未下发 → 转到今日{$ztjs}",
                                "callback_data" => "昨日未结款转移"
                            ]
                        ],
                    ];
            
            if(!empty($qunset['json']['ztjs'])){
                if(empty($qunsetjson['ztxianshi'])){
                    $ztxianshi = "🔲";
                }else{
                    $ztxianshi = "✅";
                }
               $ret['anniu'][] =  [
                            [
                                "text" => "昨日转移款 → 账单显示{$ztxianshi}",
                                "callback_data" => "昨日转移款显示"
                            ]
                        ]; 
            }     
                    
            $ret['back'] = 0; 
            Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['json'=>json_encode($qunsetjson)]);
            return $ret;
            
            
        }else if($message['btnData']  == '设置日切时间'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            $ret['editText'] = "【举例】\n晚上00:00分切换则输入：<code>0000</code>\n半夜03:00分切换则输入：<code>0300</code>\n\n<b>请输入日切时间(时分)：</b>";
            $ret["next"] = "设置日切时间";
            return $ret;
        
        }else if($message['btnData']  == '下载账单'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "请联系管理员下载";
                return $ret;
            }
            
            $user = Db::name("bot_account")->where("del",0)->where("bot",$message['bot']['API_BOT'])->where("tgid",$message["formId"])->find();
            if(empty($user)){
               $ret["editText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
               $ret["anniu"] = [ 
                        [
                            [
                                "text" => "🔴 点击启用机器人",
                                "url" => "https://t.me/{$message['bot']['API_BOT']}"
                            ]
                        ],
                    ];
               return $ret;  
            }
            
            $jsonVal = array();
            $so =[];
            array_push($so,"del");
            array_push($so,'=');
            array_push($so,0); 
            
 
            
            array_push($so,"bot");
            array_push($so,'=');
            array_push($so,$message['bot']['API_BOT']); 
            
            array_push($so,"ref");
            array_push($so,'=');
            array_push($so,$qunset['ref']); 
            
            array_push($so,"qunid");
            array_push($so,'=');
            array_push($so,$message['chatId']); 
            
            $so = array_chunk($so,3);//拆分 
            #$count = mdb\jizhang_list::where([$so])->field("count(*)as count,count(fuhao='-' or null) as jian,count(fuhao='+' or null) as jia,SUM(shouxufei) as shouxufei,SUM(CASE WHEN fuhao = '+' THEN value ELSE 0 END) AS jiaM,SUM(CASE WHEN fuhao = '-' THEN money ELSE 0 END) AS jianM,SUM(CASE WHEN fuhao = '+' THEN money ELSE 0 END) AS jiaU,SUM(CASE WHEN fuhao = '-' THEN value ELSE 0 END) AS jianU")->find(); 
            
            $count = Db::name("jizhang_totalz")->where([$so,["tgid","=",0]])->order('id desc')->find(); 
            
            if(empty($count['incnum'])){ 
                $ret['editText'] = "当前似乎没有账单哦"; 
                return $ret;
            } 
            
            $DataList = mdb\jizhang_list::where([$so])->order('id asc')->select();  
             
            
            $oklist=array(); 
            foreach ($DataList as $value) {  
                
                $json['name'] = $value['name'];
                $json['user'] = $value['user'];
                
                $json['fuhao'] = $value['fuhao'];
                $json['huilv'] = $value['huilv'];
                $json['shouxufei'] = $value['shouxufei']; 
                $json['create_time'] = $value['create_time']; 
                $json['yu'] = $value['yu']; 
                
                if($value['fuhao'] == '-'){
                    $json['fuhao'] = "下发";
                    $json['value'] = -$value['money'];
                    $json['money'] = -$value['value'];
                    
                }else{
                    $json['fuhao'] = "入款";
                    $json['value'] = $value['value'];
                    $json['money'] = $value['money'];
                }  
                
                if(isset($value['reply']['toName'])){
                    $json['reply'] = "@{$value['reply']['toUser']} {$value['reply']['toName']}";
                }else{
                    $json['reply'] = "";
                } 
                
                $oklist[] = $json; 
            } 
            
             
            array_unshift($oklist,[]); //加入空行 
            
            $array_push = ['reply'=>"统计：",'name'=>"",'user'=>"",'fuhao'=>"入{$count['incnum']}/出{$count['decnum']}",'huilv'=>$count['incnum']+$count['decnum']."笔",'shouxufei'=>"≈{$count['shouxufei']}元",'value'=>"≈{$count['incvalue']}元",'money'=>"≈{$count['incmoney']}U",'yu'=>""];//定义数组
            array_unshift($oklist,$array_push); 
            
            array_unshift($oklist,[]); //加入空行
            
             
        
            
            
            $headings = ['reply'=>'回复Ta','name'=>'操作人','user'=>'用户名','huilv'=>'币价','fuhao'=>'记账类型','value'=>'人民币','shouxufei'=>'扣手续费','money'=>'≈USDT','create_time'=>'记账时间','yu'=>'未下发'];
            
            $fileurl = run_path()."/bot/res/down/{$message['chatId']}/";//路径
            $filename = "账单_{$qunset['date']}_{$qunset['ref']}.xlsx"; 
            if(!is_dir($fileurl)){
              mkdir($fileurl,0755,true);  
            } 
            Excel::xlsx($oklist)->headings($headings)->store($fileurl.$filename); 
        
        
            //Excel::csv($DataList)->download($fileurl.$filename);
            $sendText = "文件名称：<u>账单_{$qunset['date']}_{$qunset['ref']}.xlsx</u>\n来自群组：<code>{$message['chatName']}</code>\n群组编号：<code>{$message['chatId']}</code>\n下载时间：<code>".date("Y-m-d H:i:s")."</code>"; 
            $sendFile = $fileurl.$filename; 
            
            $ret['editText'] = $sendText."\n\n<b>完整账单已私信发送给您,请查收！</b>✅"; 
            
            $base = new Base();
            $base->sendUrl("/sendDocument?chat_id={$message['formId']}&document=file://{$sendFile}&caption=".$sendText,0,1); 
                 
            
            return $ret;
            
        }else if($message['btnData']  == '入款详情'){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            $jsonVal = array();
            $so =[];
            array_push($so,"del");
            array_push($so,'=');
            array_push($so,0);  
            
            array_push($so,"bot");
            array_push($so,'=');
            array_push($so,$message['bot']['API_BOT']); 
            
            array_push($so,"ref");
            array_push($so,'=');
            array_push($so,$qunset['ref']); 
            
            array_push($so,"qunid");
            array_push($so,'=');
            array_push($so,$message['chatId']); 
            
            array_push($so,"tgid");
            array_push($so,'>');
            array_push($so,0); 
            
            $so = array_chunk($so,3);//拆分   
            $count = Db::name("jizhang_totalz")->where([$so])->order('id desc')->find(); 
            
            if(empty($count['incnum'])){ 
                $ret['editText'] = "似乎没有详细入账单哦"; 
                return $ret;
            } 
            $DataList = mdb\jizhang_totalz::where([$so])->order('id desc')->select(); 
            $text = "<b>[本群今日记账详情]</b>\n";
            foreach ($DataList as $res) {
                $text .= "\n<a href='https://t.me/{$res['user']['user']}'>{$res['user']['name']}</a> 入款：<u><b>{$res['incvalue']}元</b></u> | 下发：<b><u>{$res['decmoney']} U</u></b>"; 
            } 
            // $ret['back'] =0;
            $ret['editText'] = $text; 
            return $ret;
            
            
        }else if($res = pipei($message['btnData'],"(.+)_([a-z0-9.]+)-?(.+)?")){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            $USDTprice = Redis::GET("USDTprice");
            
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            
            switch ($res[1]) {
                default: 
                    break;
                    
                case '确认设定':
                    Redis::SETEX("USDTprice",600,$res[3]); 
                    $qunsetjson = $qunset['json'];    
                    
                    $qunsetjson['rmbclose'] = 1;//浮动汇率 不同汇率,关闭人民币显示
                    $qunsetjson['ss'] = 1; 
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]); 
                    $ret['editText']= "设置置成功✅\n\n本群微调价格：{$qunsetjson['weitiao']}\n本群实时汇率：<b>".$res[2]."</b>";
                    $ret['back'] = 0;
                    break; 
                    
                case '设定': 
                    $qunsetjson = $qunset['json'];  
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    }
                    if(empty($qunsetjson['lei'])){
                        $qunsetjson['lei'] = 'bank';
                    }
                    $qunsetjson['dangwei'] = $res[3];
                    $models = new \bot\api_message\jizhang;
                    $text = $models->bijia($qunsetjson,$qunsetjson['lei'],$qunsetjson['dangwei']);   
                    $ret['editText']= "{$text['text']}\n\n档位价格：{$text['sshuilv']}\n微调价格：{$qunsetjson['weitiao']}\n实时汇率：<b>".$text['sshuilv']+$qunsetjson['weitiao']."</b>";
                    $ret['anniu'] = null; 
                    $ret['anniu'][] = $text['a21'];
                    $ret['anniu'][] = $text['a22'];
                    $ret['anniu'][] = $text['a11'];
                    $ret['anniu'][] = $text['a31'];
                    $ret['anniu'][] = $text['a32'];
                    $ret['anniu'][] = $text['a4'];    
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    } 
                    $ret['back'] = 0;
                     
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]);
                    break; 
                    
                    
                case '选择': 
                    $qunsetjson = $qunset['json']; 
                    if(empty($qunsetjson['dangwei'])){
                        $qunsetjson['dangwei'] = 0;
                    }
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    } 
                    
                    $qunsetjson['lei'] = $res[2];
                     
                    $models = new \bot\api_message\jizhang;
                    $text = $models->bijia($qunsetjson,$res[2],$qunsetjson['dangwei']);   
                    $ret['editText']= "{$text['text']}\n\n档位价格：{$text['sshuilv']}\n微调价格：{$qunsetjson['weitiao']}\n实时汇率：<b>".$text['sshuilv']+$qunsetjson['weitiao']."</b>";
                    $ret['anniu'] = null; 
                    $ret['anniu'][] = $text['a21'];
                    $ret['anniu'][] = $text['a22'];
                    $ret['anniu'][] = $text['a11'];
                    $ret['anniu'][] = $text['a31'];
                    $ret['anniu'][] = $text['a32'];
                    $ret['anniu'][] = $text['a4'];   
                     
                     
                    $ret['back'] = 0;
                    
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]);
                    break; 
                    
                    
                case '减少': 
                    $qunsetjson = $qunset['json']; 
                    if(empty($qunsetjson['dangwei'])){
                        $qunsetjson['dangwei'] = 0;
                    }
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    }
                    if(empty($qunsetjson['lei'])){
                        $qunsetjson['lei'] = 'bank';
                    } 
                    $qunsetjson['weitiao'] = round($qunsetjson['weitiao'] - $res[2],2);
                    $models = new \bot\api_message\jizhang;
                    $text = $models->bijia($qunsetjson,$res[2],$qunsetjson['dangwei']);    
                    $ret['editText']= "{$text['text']}\n\n档位价格：{$text['sshuilv']}\n微调价格：{$qunsetjson['weitiao']}\n实时汇率：<b>".$text['sshuilv']+$qunsetjson['weitiao']."</b>";
                    $ret['back'] = 0;
                    $ret['anniu'] = null; 
                    $ret['anniu'][] = $text['a21'];
                    $ret['anniu'][] = $text['a22'];
                    $ret['anniu'][] = $text['a11'];
                    $ret['anniu'][] = $text['a31'];
                    $ret['anniu'][] = $text['a32'];
                    $ret['anniu'][] = $text['a4']; 
                     
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]);
                    break;  
                    
                
                case '增加': 
                    $qunsetjson = $qunset['json']; 
                    if(empty($qunsetjson['dangwei'])){
                        $qunsetjson['dangwei'] = 0;
                    }
                    if(empty($qunsetjson['weitiao'])){
                        $qunsetjson['weitiao'] = 0;
                    }
                    if(empty($qunsetjson['lei'])){
                        $qunsetjson['lei'] = 'bank';
                    }
                    $qunsetjson['weitiao'] =  round($qunsetjson['weitiao'] + $res[2],2);
                    
                    $models = new \bot\api_message\jizhang;
                    $text = $models->bijia($qunsetjson,$res[2],$qunsetjson['dangwei']);    
                    $ret['editText']= "{$text['text']}\n\n档位价格：{$text['sshuilv']}\n微调价格：{$qunsetjson['weitiao']}\n实时汇率：<b>".$text['sshuilv']+$qunsetjson['weitiao']."</b>";
                    $ret['back'] = 0;
                    $ret['anniu'] = null; 
                    $ret['anniu'][] = $text['a21'];
                    $ret['anniu'][] = $text['a22'];
                    $ret['anniu'][] = $text['a11'];
                    $ret['anniu'][] = $text['a31'];
                    $ret['anniu'][] = $text['a32'];
                    $ret['anniu'][] = $text['a4']; 
                    
                    Db::name("jizhang_qunset")->where("id",$qunset['id'])->cache("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset")->update(['type'=>1,'json'=>json_encode($qunsetjson)]);
                    break;     
            }
            
            
        }else if($res = pipei($message['btnData'],"删除总账单(-?\w+)")){
            $Model = new mdb\jizhang_qunset;
            $qunset = $Model->setJSON($message['bot']['API_BOT'],$message['chatId']); 
            if(!$this->isok($qunset,$message['formUser'],$message['formId'])){
                $ret['alert'] = "无权操作";
                return $ret;
            }
            Db::name("jizhang_totalz")->where("bot",$message['bot']['API_BOT'])->where("qunid",$res[1])->where("date",0)->cache("{$message['bot']['API_BOT']}{$res[1]}totalz0")->update(["del"=>1]); 
            Cache::delete("{$message['bot']['API_BOT']}_{$message['chatId']}_jizhang_qunset");
            $ret['editText'] = "日账单已删除⛔️\n"; 
            $ret['editText'] .= "总账单已删除⛔️"; 
            $ret['editText'] .= "\n\n本群当前为初始状态,没有任何记账数据。"; 
            $ret['back']=0;
            $ret['nodel']=1;
        } 
 
        return $ret;
    }
    
    
    
    public function isok($qunset,$user,$tgid){   
        if(strripos("#".$qunset['json']['admin'],"@{$user}")){   
            return true;
        }
        $group =  model\bot_group::where("bot",$qunset['bot'])->where('qunid', $qunset['qunid'])->find(); 
        if(isset($group['admin'][$tgid])){
            return true; 
        } 
        return false;
    }
}
