-- 表结构：tb_niucountbak
CREATE TABLE `tb_niucountbak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `qunid` bigint(11) NOT NULL,
  `num` int(10) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `val` varchar(2) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `json` json NOT NULL,
  `total` int(11) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3754 DEFAULT CHARSET=utf8mb4 COMMENT='牛牛记账表';
