

CREATE TABLE `sys_crontab` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL COMMENT '任务标题',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务类型 (1 command, 2 class, 3 url, 4 eval)',
  `rule` varchar(100) NOT NULL COMMENT '任务执行表达式',
  `target` varchar(150) NOT NULL DEFAULT '' COMMENT '调用任务字符串',
  `parameter` varchar(500) NOT NULL COMMENT '任务调用参数',
  `running_times` int(11) NOT NULL DEFAULT '0' COMMENT '已运行次数',
  `last_running_time` int(11) NOT NULL DEFAULT '0' COMMENT '上次运行时间',
  `remark` varchar(255) NOT NULL COMMENT '备注',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，越大越前',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务状态状态[0:禁用;1启用]',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  `singleton` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否单次执行 (0 是 1 不是)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `title` (`title`) USING BTREE,
  KEY `create_time` (`create_time`) USING BTREE,
  KEY `status` (`status`) USING BTREE,
  KEY `type` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定时器任务表';

INSERT INTO sys_crontab VALUES ('1','usdtss','2','0 */3 * * * *','app\\task\\usdtprice','','5465','1734083280','usdtss','0','1','1732086163','1732086163','1');
INSERT INTO sys_crontab VALUES ('2','energyss','2','0 */4 * * * *','app\\task\\energyprice','','4098','1734083280','energyss','0','1','1732086163','1732086163','1');
INSERT INTO sys_crontab VALUES ('3','block1','2','*/3 * * * * *','app\\task\\block_number','','328016','1734083383','block1','0','1','1732086163','1732086163','1');
INSERT INTO sys_crontab VALUES ('4','block2','2','*/50 * * * * *','app\\task\\block_query','','32803','1734083341','block2','0','1','1732086163','1732086163','1');
INSERT INTO sys_crontab VALUES ('5','get_url','2','*/30 * * * * *','app\\task\\get_url','','32811','1734083372','get_url','0','1','1732086164','1732086164','1');


CREATE TABLE `sys_crontab_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `crontab_id` bigint(20) unsigned NOT NULL COMMENT '任务id',
  `target` varchar(255) NOT NULL COMMENT '任务调用目标字符串',
  `parameter` varchar(500) NOT NULL COMMENT '任务调用参数',
  `exception` text NOT NULL COMMENT '任务执行或者异常信息输出',
  `return_code` tinyint(1) NOT NULL DEFAULT '0' COMMENT '执行返回状态[0成功; 1失败]',
  `running_time` varchar(10) NOT NULL COMMENT '执行所用时间',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `create_time` (`create_time`) USING BTREE,
  KEY `crontab_id` (`crontab_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='定时器任务执行日志表';



CREATE TABLE `sys_menu` (
  `menuId` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单id',
  `parentId` int(11) NOT NULL DEFAULT '0' COMMENT '上级id, 0是顶级',
  `auth` int(1) NOT NULL,
  `title` varchar(200) NOT NULL COMMENT '菜单名称',
  `path` varchar(200) DEFAULT NULL COMMENT '菜单路由地址',
  `component` varchar(200) DEFAULT NULL COMMENT '菜单组件地址, 目录可为空',
  `menuType` int(11) DEFAULT '0' COMMENT '类型, 0菜单, 1按钮',
  `sortNumber` int(11) NOT NULL DEFAULT '1' COMMENT '排序号',
  `authority` varchar(200) DEFAULT NULL COMMENT '权限标识',
  `target` varchar(200) DEFAULT '_self' COMMENT '打开位置',
  `icon` varchar(200) DEFAULT NULL COMMENT '菜单图标',
  `color` varchar(200) DEFAULT NULL COMMENT '图标颜色',
  `hide` int(11) NOT NULL DEFAULT '0' COMMENT '是否隐藏',
  `active` varchar(200) DEFAULT NULL COMMENT '菜单侧栏选中的path',
  `meta` varchar(800) DEFAULT NULL COMMENT '其它路由元信息',
  `del` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除, 0否, 1是',
  `tenantId` int(11) NOT NULL DEFAULT '1' COMMENT '租户id',
  `createTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`menuId`) USING BTREE,
  KEY `tenant_id` (`tenantId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=443 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='菜单';

INSERT INTO sys_menu VALUES ('1','0','0','Dashboard ','/dashboard','','0','0','','_self','el-icon-s-home','','0','','','0','1','2022-06-15 11:49:57','2022-06-15 11:49:57');
INSERT INTO sys_menu VALUES ('2','1','0','分析页','/dashboard/monitor','/dashboard/monitor','0','11','','_self','el-icon-odometer','','0','','','0','1','2022-06-15 12:02:41','2022-06-15 12:02:41');
INSERT INTO sys_menu VALUES ('3','0','0','商户信息','/user/profile','/user/profile','0','2','','_self','el-icon-user-solid','','0','','','0','1','2022-06-15 12:06:23','2022-06-15 12:06:23');
INSERT INTO sys_menu VALUES ('4','3','0','基本信息','/user/profile','/user/profile','0','3','','_self','el-icon-setting','','0','','','1','1','2022-06-15 12:07:45','2022-06-15 12:07:45');
INSERT INTO sys_menu VALUES ('5','1','0','消息','/user/message','/user/message','0','22','','_self','el-icon-chat-dot-square','','0','','','0','1','2022-06-15 12:08:38','2022-06-15 12:08:38');
INSERT INTO sys_menu VALUES ('9','0','0','系统管理','/admin','','0','1','','_self','el-icon-_setting-solid','','0','','','0','1','2022-06-15 12:11:58','2022-06-15 12:11:58');
INSERT INTO sys_menu VALUES ('10','9','0','菜单管理','/admin/menu','/admin/menu','0','10','','_self','el-icon-s-operation','','0','','','0','1','2022-06-15 12:12:25','2022-06-15 12:12:25');
INSERT INTO sys_menu VALUES ('11','9','0','角色管理','/admin/role','/admin/role','0','91','','_self','el-icon-user','','0','','','0','1','2022-06-15 12:12:50','2022-06-15 12:12:50');
INSERT INTO sys_menu VALUES ('15','9','0','消息管理','/admin/allmsg','/admin/allmsg','0','20','','_self','el-icon-chat-line-round','','0','','','0','1','2022-06-15 12:53:20','2022-06-15 12:53:20');
INSERT INTO sys_menu VALUES ('19','5','0','获取消息','','','1','0','/api/user/my_message_notice','_self','','','0','','','0','1','2022-06-15 13:48:39','2022-06-15 13:48:39');
INSERT INTO sys_menu VALUES ('20','3','0','获取账户信息','','','1','31','/api/user/user_basic','_self','','','0','','','0','1','2022-06-15 13:49:55','2022-06-15 13:49:55');
INSERT INTO sys_menu VALUES ('21','3','0','绑解谷歌验证','','','1','31','/api/user/my_open_Google','_self','','','0','','','0','1','2022-06-15 13:56:48','2022-06-15 13:56:48');
INSERT INTO sys_menu VALUES ('22','3','0','绑定telgram','','','1','31','/api/user/my_open_Telegram','_self','','','0','','','0','1','2022-06-15 13:58:39','2022-06-15 13:58:39');
INSERT INTO sys_menu VALUES ('23','5','0','删除消息','','','1','20','/api/user/my_message_del','_self','','','0','','','0','1','2022-06-15 14:00:54','2022-06-15 14:00:54');
INSERT INTO sys_menu VALUES ('50','3','0','修改密码','','','1','31','/api/user/UpdatePassword','_self','','','0','','','0','1','2022-07-27 17:44:15','2022-07-27 17:44:15');
INSERT INTO sys_menu VALUES ('51','9','0','用户管理','/admin/user','/admin/user','0','0','','_self','el-icon-coordinate','','0','','','0','1','2022-07-29 17:43:50','2022-07-29 17:43:50');
INSERT INTO sys_menu VALUES ('65','3','0','设置登录IP白名单','','','1','31','/api/user/LoginIp','_self','','','0','','','0','1','2022-08-20 02:54:32','2022-08-20 02:54:32');
INSERT INTO sys_menu VALUES ('86','5','0','清空消息','','','1','10','/api/user/message_read','_self','','','0','','','0','3','2022-10-07 16:41:23','2022-10-07 16:41:23');
INSERT INTO sys_menu VALUES ('91','3','0','查看在线终端','','','1','0','/api/user/zxzd','_self','','','0','','','0','1','2022-11-19 15:48:18','2022-11-19 15:48:18');
INSERT INTO sys_menu VALUES ('111','0','0','账户中心','/user','','0','2','','_self','el-icon-setting','','0','','','0','1','2022-12-03 16:06:54','2022-12-03 16:06:54');
INSERT INTO sys_menu VALUES ('123','111','0','账户信息','/user/info','/user/my.vue','0','0','','_self','el-icon-document','','0','','','0','1','2022-12-04 15:54:19','2022-12-04 15:54:19');
INSERT INTO sys_menu VALUES ('125','0','0','Dashboard','/tg','','0','1','','_self','el-icon-s-home','','0','','','0','2','2023-03-14 02:22:36','2023-03-14 02:22:36');
INSERT INTO sys_menu VALUES ('126','125','0','数据概况','/tg/index','/tg/index','0','10','','_self','el-icon-data-line','','0','','','0','2','2023-03-14 02:43:09','2023-03-14 02:43:09');
INSERT INTO sys_menu VALUES ('127','149','0','钱包配置','/tg/setup','/tg/setup','0','111','','_self','el-icon-_setting-solid','','0','','','0','2','2023-03-14 03:53:34','2023-03-14 03:53:34');
INSERT INTO sys_menu VALUES ('128','0','0','自定义菜单','/tg/command','/tg/command','0','80','','_self','el-icon-_nav','','0','','','0','2','2023-03-14 03:54:01','2023-03-14 03:54:01');
INSERT INTO sys_menu VALUES ('129','0','0','自定义按钮','/tg/msgbtn','/tg/msgbtn','0','90','','_self','el-icon-_pad','','0','','','0','2','2023-03-14 03:55:01','2023-03-14 03:55:01');
INSERT INTO sys_menu VALUES ('130','0','0','自定义键盘','/tg/reply','/tg/reply','0','100','','_self','el-icon-_keyboard','','0','','','0','2','2023-03-14 03:55:34','2023-03-14 03:55:34');
INSERT INTO sys_menu VALUES ('131','149','0','机器人配置','/tg/info','/tg/info','0','100','','_self','el-icon-_camera','','0','','','0','2','2023-03-14 04:03:52','2023-03-14 04:03:52');
INSERT INTO sys_menu VALUES ('132','0','0','群组管理','/tg/group','/tg/group','0','50','','_self','el-icon-_user-group','','0','','','0','2','2023-03-14 04:29:12','2023-03-14 04:29:12');
INSERT INTO sys_menu VALUES ('133','0','0','用户管理','/tg/user','/tg/user','0','40','','_self','el-icon-user','','0','','','0','2','2023-03-14 04:30:03','2023-03-14 04:30:03');
INSERT INTO sys_menu VALUES ('134','151','0','地址管理','/tg/trc20','/tg/trc20','0','298','','_self','el-icon-location-information','','0','','','0','2','2023-03-14 04:33:36','2023-03-14 04:33:36');
INSERT INTO sys_menu VALUES ('135','151','0','兑换记录','/tg/dh_log','/tg/dh_log','0','300','','_self','el-icon-tickets','','0','','','0','2','2023-03-14 04:37:32','2023-03-14 04:37:32');
INSERT INTO sys_menu VALUES ('136','129','0','发布更新按钮','','','1','10','/api/tgbot/command_markup','_self','','','0','','','0','2','2023-03-19 01:25:48','2023-03-19 01:25:48');
INSERT INTO sys_menu VALUES ('137','129','0','删除按钮','','','1','20','/api/tgbot/markup_del','_self','','','0','','','0','2','2023-03-19 05:26:16','2023-03-19 05:26:16');
INSERT INTO sys_menu VALUES ('138','129','0','添加事件','','','1','19','/api/tgbot/command_add','_self','','','0','','','0','2','2023-03-19 09:35:16','2023-03-19 09:35:16');
INSERT INTO sys_menu VALUES ('139','128','0','添加菜单命令','','','1','10','/api/tgbot/commands_add','_self','','','0','','','0','2','2023-03-21 15:04:08','2023-03-21 15:04:08');
INSERT INTO sys_menu VALUES ('140','128','0','删除菜单命令','','','1','20','/api/tgbot/command_del','_self','','','0','','','0','2','2023-03-21 16:30:20','2023-03-21 16:30:20');
INSERT INTO sys_menu VALUES ('141','133','0','发送消息','','','1','10','/api/tgbot/send_Msg','_self','','','0','','','0','2','2023-03-25 12:57:22','2023-03-25 12:57:22');
INSERT INTO sys_menu VALUES ('142','132','0','发送消息','','','1','10','/api/tgbot/send_Msg','_self','','','0','','','0','2','2023-03-25 12:57:40','2023-03-25 12:57:40');
INSERT INTO sys_menu VALUES ('143','132','0','加白名单','','','1','8','/api/tgbot/group_update_zt','_self','','','0','','','0','2','2023-03-28 10:37:48','2023-03-28 10:37:48');
INSERT INTO sys_menu VALUES ('144','132','0','机器人退群','','','1','6','/api/tgbot/bot_tuiqun','_self','','','0','','','0','2','2023-03-28 10:39:20','2023-03-28 10:39:20');
INSERT INTO sys_menu VALUES ('145','131','0','修改机器人信息','','','1','10','/api/tgbot/bot_setup','_self','','','0','','','0','2','2023-04-27 09:51:59','2023-04-27 09:51:59');
INSERT INTO sys_menu VALUES ('146','127','0','修改兑换配置信息','','','1','10','/api/tgbot/bot_trx_setup','_self','','','0','','','0','2','2023-04-27 15:21:07','2023-04-27 15:21:07');
INSERT INTO sys_menu VALUES ('147','151','0','购买TRX','/tg/trx','/tg/trx','0','290','','_self','el-icon-_flash-solid','','0','','{\"badge\": \"闪兑\"}','0','2','2023-07-07 11:33:32','2023-07-07 11:33:32');
INSERT INTO sys_menu VALUES ('148','151','0','预支TRX','/tg/jie','/tg/jie','0','291','','_self','el-icon-_red-packet-solid','','0','','{\"badge\": \"借\"}','0','2','2023-07-07 11:34:40','2023-07-07 11:34:40');
INSERT INTO sys_menu VALUES ('149','0','0','基础配置','/tg/x','','0','25','','_self','el-icon-setting','','0','','','0','2','2023-07-07 11:36:07','2023-07-07 11:36:07');
INSERT INTO sys_menu VALUES ('150','149','0','功能开关','/tg/kaiguan','/tg/kaiguan','0','123','','_self','el-icon-open','','0','','','0','2','2023-07-07 11:46:33','2023-07-07 11:46:33');
INSERT INTO sys_menu VALUES ('151','0','0','兑换管理','/tg/d','','0','28','','_self','el-icon-_vercode','','0','','','0','2','2023-07-07 12:19:11','2023-07-07 12:19:11');
INSERT INTO sys_menu VALUES ('152','132','0','设置群欢迎语','','','1','0','/api/tgbot/group_welcome','_self','','','0','','','0','2','2023-07-07 16:53:18','2023-07-07 16:53:18');
INSERT INTO sys_menu VALUES ('153','132','0','设置定时广告','','','1','0','/api/tgbot/group_adtext','_self','','','0','','','0','2','2023-07-07 16:53:36','2023-07-07 16:53:36');
INSERT INTO sys_menu VALUES ('154','0','0','修改密码','','','1','999','/api/user/UpdatePassword','_self','','','0','','','0','2','2023-07-11 16:26:49','2023-07-11 16:26:49');
INSERT INTO sys_menu VALUES ('155','0','0','上传文件','','','1','998','/api/upload/index','_self','','','0','','','0','2','2023-07-14 15:11:31','2023-07-14 15:11:31');
INSERT INTO sys_menu VALUES ('156','132','0','删除群广告图','','','1','90','/api/tgbot/group_delimg','_self','','','0','','','0','2','2023-07-14 17:49:26','2023-07-14 17:49:26');
INSERT INTO sys_menu VALUES ('157','148','0','手动预支','','','1','11','/api/tgbot/trx_addtrx','_self','','','0','','','0','2','2023-07-17 03:41:28','2023-07-17 03:41:28');
INSERT INTO sys_menu VALUES ('158','134','0','修改接收通知状态','','','1','1','/api/tgbot/trc20_update_zt','_self','','','0','','','0','2','2023-07-17 14:01:01','2023-07-17 14:01:01');
INSERT INTO sys_menu VALUES ('159','134','0',' 拉黑地址','','','1','11','/api/tgbot/trc20_lahei','_self','','','0','','','0','2','2023-07-17 14:20:49','2023-07-17 14:20:49');
INSERT INTO sys_menu VALUES ('160','134','0','绑定TGid','','','1','3','/api/tgbot/trc20_bangding','_self','','','0','','','0','2','2023-07-17 15:34:58','2023-07-17 15:34:58');
INSERT INTO sys_menu VALUES ('161','147','0','闪兑购买TRX','','','1','1','/api/tgbot/trx_sunswapTrx','_self','','','0','','','0','2','2023-07-19 14:02:38','2023-07-19 14:02:38');
INSERT INTO sys_menu VALUES ('162','148','0','自动预支设置','','','1','12','/api/tgbot/trx_AutoSet','_self','','','0','','','0','2','2023-07-20 13:03:52','2023-07-20 13:03:52');
INSERT INTO sys_menu VALUES ('163','135','0','补发兑换订单','','','1','2','/api/tgbot/trx_bufa','_self','','','0','','','0','2','2023-07-21 12:29:12','2023-07-21 12:29:12');
INSERT INTO sys_menu VALUES ('164','0','0','频道管理','/tg/pindao','/tg/pindao','0','52','','_self','el-icon-star-off','','0','','','0','2','2023-07-23 13:27:33','2023-07-23 13:27:33');
INSERT INTO sys_menu VALUES ('165','150','0','修改功能开关','','','1','0','/api/tgbot/bot_setkg','_self','','','0','','','0','2','2023-07-23 14:05:17','2023-07-23 14:05:17');
INSERT INTO sys_menu VALUES ('166','0','0','Dashboard','/bot/1','','0','10','','_self','el-icon-house','','0','','','0','3','2023-08-27 03:58:09','2023-08-27 03:58:09');
INSERT INTO sys_menu VALUES ('167','166','0','统计数据','/bot/index','/bot/index','0','11','','_self','el-icon-_trending-up','','0','','','0','3','2023-08-27 03:59:18','2023-08-27 03:59:18');
INSERT INTO sys_menu VALUES ('168','0','0','应用中心','/bot/2','','0','20','','_self','el-icon-_shop','','0','','{\"badge\": \"new\"}','0','3','2023-08-27 04:03:06','2023-08-27 04:03:06');
INSERT INTO sys_menu VALUES ('169','168','0','功能模块','/bot/plugin','/bot/plugin','0','21','','_self','el-icon-open','','0','','','0','3','2023-08-27 04:05:14','2023-08-27 04:05:14');
INSERT INTO sys_menu VALUES ('170','168','0','小程序模块','/bot/webapp','/bot/webapp','0','31','','_self','el-icon-mobile','','0','','','0','3','2023-08-27 06:24:28','2023-08-27 06:24:28');
INSERT INTO sys_menu VALUES ('171','169','0','获取模块列表','','','1','1','/app/newapp/index/index','_self','','','0','','','0','3','2023-08-27 15:54:48','2023-08-27 15:54:48');
INSERT INTO sys_menu VALUES ('172','230','0','机器人列表','/bot/botlist','/bot/botlist','0','1','','_self','el-icon-s-promotion','','0','','','0','3','2023-08-27 16:02:49','2023-08-27 16:02:49');
INSERT INTO sys_menu VALUES ('173','172','0','获取机器人列表','','','1','10','/app/newapp/index/botlist','_self','','','0','','','0','3','2023-08-27 16:04:10','2023-08-27 16:04:10');
INSERT INTO sys_menu VALUES ('174','0','1','TRX兑换','/bot/gdtrx','','0','40','','_self','el-icon-_flash','','0','','','0','3','2023-08-27 16:08:26','2023-08-27 16:08:26');
INSERT INTO sys_menu VALUES ('175','0','1','TG开会员','/bot/tgvip','','0','50','','_self','el-icon-_setting','','0','','','0','3','2023-08-27 16:08:59','2023-08-27 16:08:59');
INSERT INTO sys_menu VALUES ('176','174','1','兑换设置','/bot/trxset','/bot/trxset','0','10','','_self','el-icon-setting','','0','','','0','3','2023-08-27 16:10:06','2023-08-27 16:10:06');
INSERT INTO sys_menu VALUES ('177','174','1','预支记录','/gdtrx/yzlog','/web/gdtrx/list','0','20','','_self','el-icon-document','','0','','','0','3','2023-08-27 16:10:56','2023-08-27 16:10:56');
INSERT INTO sys_menu VALUES ('178','169','0','安装应用模块','','','1','1691','/app/appStore/index/appinstall','_self','','','0','','','0','3','2023-08-31 03:18:31','2023-08-31 03:18:31');
INSERT INTO sys_menu VALUES ('179','169','0','卸载应用模块','','','1','0','/app/appStore/index/appuninstall','_self','','','0','','','0','3','2023-08-31 03:19:04','2023-08-31 03:19:04');
INSERT INTO sys_menu VALUES ('180','0','1','地址监控','/bot/usdtjt','','0','60','','_self','el-icon-timer','','0','','','0','3','2023-08-31 04:01:09','2023-08-31 04:01:09');
INSERT INTO sys_menu VALUES ('181','169','0','更新应用模块','','','1','0','/app/appStore/index/appupdate','_self','','','0','','','0','3','2023-08-31 04:02:04','2023-08-31 04:02:04');
INSERT INTO sys_menu VALUES ('182','169','0','购买应用模块','','','1','0','/app/appStore/index/appbuypost','_self','','','0','','','0','3','2023-08-31 04:02:22','2023-08-31 04:02:22');
INSERT INTO sys_menu VALUES ('183','169','0','打包发布模块','','','1','0','/app/appStore/index/appbuild','_self','','','0','','','0','3','2023-08-31 04:03:15','2023-08-31 04:03:15');
INSERT INTO sys_menu VALUES ('184','169','0','登录模块中心','','','1','0','/app/appStore/index/applogin','_self','','','0','','','0','3','2023-08-31 04:03:49','2023-08-31 04:03:49');
INSERT INTO sys_menu VALUES ('185','180','1','TRC地址列表','/usdtjt/list','/bot/usdtjt_list','0','10','','_self','el-icon-_lamp','','0','','','0','3','2023-08-31 04:12:07','2023-08-31 04:12:07');
INSERT INTO sys_menu VALUES ('186','172','0','添加修改机器人','','','1','0','/app/appStore/bot/addbot','_self','','','0','','','0','3','2023-08-31 11:07:08','2023-08-31 11:07:08');
INSERT INTO sys_menu VALUES ('187','172','0','启用停用机器人','','','1','0','/app/appStore/bot/botzt','_self','','','0','','','0','3','2023-08-31 11:27:08','2023-08-31 11:27:08');
INSERT INTO sys_menu VALUES ('188','185','0','新增监听地址','','','1','0','/app/appStore/bot/address_jt_add','_self','','','0','','','0','3','2023-09-02 13:55:16','2023-09-02 13:55:16');
INSERT INTO sys_menu VALUES ('189','221','0','修改自身密码','','','1','2211','/api/user/UpdatePassword','_self','','','0','','','0','3','2023-09-03 16:46:22','2023-09-03 16:46:22');
INSERT INTO sys_menu VALUES ('190','172','0','删除机器人','','','1','0','/app/appStore/bot/del','_self','','','0','','','0','3','2023-09-21 03:26:24','2023-09-21 03:26:24');
INSERT INTO sys_menu VALUES ('191','169','0','新增功能模块','','','1','0','/app/appStore/plugin/create','_self','','','0','','','0','3','2023-09-25 03:40:19','2023-09-25 03:40:19');
INSERT INTO sys_menu VALUES ('192','0','1','群管助手','/bot/qunguan','','0','70','','_self','el-icon-_connecting-line','','0','','','0','3','2023-09-26 14:49:46','2023-09-26 14:49:46');
INSERT INTO sys_menu VALUES ('193','192','1','消息内容黑名单','/bot/adkill','/bot/adkill','0','14','','_self','el-icon-_eye-close','','0','','','0','3','2023-09-26 14:52:12','2023-09-26 14:52:12');
INSERT INTO sys_menu VALUES ('194','192','1','群组定时发消息','/bot/quntask','/bot/quntask','0','50','','_self','el-icon-alarm-clock','','0','','','0','3','2023-09-26 15:01:23','2023-09-26 15:01:23');
INSERT INTO sys_menu VALUES ('195','194','0','新增定时广告','','','1','0','/web/quntask/create','_self','','','0','','','0','3','2023-09-26 15:07:47','2023-09-26 15:07:47');
INSERT INTO sys_menu VALUES ('196','193','0','新增关键词','','','1','0','/app/appStore/adkill/create','_self','','','0','','','0','3','2023-09-26 15:08:12','2023-09-26 15:08:12');
INSERT INTO sys_menu VALUES ('197','193','0','删除关键词','','','1','0','/app/appStore/adkill/del','_self','','','0','','','0','3','2023-09-27 11:26:10','2023-09-27 11:26:10');
INSERT INTO sys_menu VALUES ('198','193','0','修改关键词','','','1','0','/app/appStore/adkill/update','_self','','','0','','','0','3','2023-09-27 11:26:32','2023-09-27 11:26:32');
INSERT INTO sys_menu VALUES ('199','185','0','删除地址','','','1','0','/web/usdtjt/del','_self','','','0','','','0','3','2023-09-27 13:57:12','2023-09-27 13:57:12');
INSERT INTO sys_menu VALUES ('200','192','1','用户昵称黑名单','/bot/ukill','/bot/ukill','0','6','','_self','el-icon-user','','0','','','0','3','2023-10-01 15:09:11','2023-10-01 15:09:11');
INSERT INTO sys_menu VALUES ('201','200','0','新增黑名单','','','1','0','/app/appStore/ukill/create','_self','','','0','','','0','3','2023-10-01 15:09:50','2023-10-01 15:09:50');
INSERT INTO sys_menu VALUES ('202','200','0','删除黑名单','','','1','0','/app/appStore/ukill/del','_self','','','0','','','0','3','2023-10-01 15:10:02','2023-10-01 15:10:02');
INSERT INTO sys_menu VALUES ('203','200','0','修改黑名单','','','1','0','/app/appStore/ukill/update','_self','','','0','','','0','3','2023-10-01 15:10:13','2023-10-01 15:10:13');
INSERT INTO sys_menu VALUES ('204','192','1','进群自动欢迎','/bot/welcome','/bot/welcome','0','4','','_self','el-icon-chat-line-round','','0','','','0','3','2023-10-22 10:35:19','2023-10-22 10:35:19');
INSERT INTO sys_menu VALUES ('205','204','0','新增欢迎内容','','','1','0','/app/appStore/welcome/create','_self','','','0','','','0','3','2023-10-22 10:36:12','2023-10-22 10:36:12');
INSERT INTO sys_menu VALUES ('206','204','0','修改欢迎内容','','','1','0','/app/appStore/welcome/update','_self','','','0','','','0','3','2023-10-22 10:36:28','2023-10-22 10:36:28');
INSERT INTO sys_menu VALUES ('207','204','0','删除欢迎内容','','','1','3','/app/appStore/welcome/del','_self','','','0','','','0','3','2023-10-22 10:36:42','2023-10-22 10:36:42');
INSERT INTO sys_menu VALUES ('208','192','1','入群按钮验证','/bot/qverify','/bot/qverify','0','3','','_self','el-icon-_vercode','','0','','','0','3','2023-10-24 15:57:58','2023-10-24 15:57:58');
INSERT INTO sys_menu VALUES ('209','208','0','新增','','','1','0','/app/appStore/qverify/create','_self','','','0','','','0','3','2023-10-24 15:59:30','2023-10-24 15:59:30');
INSERT INTO sys_menu VALUES ('210','208','0','修改','','','1','0','/app/appStore/qverify/update','_self','','','0','','','0','3','2023-10-24 15:59:40','2023-10-24 15:59:40');
INSERT INTO sys_menu VALUES ('211','208','0','删除','','','1','0','/app/appStore/qverify/del','_self','','','0','','','0','3','2023-10-24 15:59:47','2023-10-24 15:59:47');
INSERT INTO sys_menu VALUES ('212','194','0','修改定时广告','','','1','0','/web/quntask/update','_self','','','0','','','0','3','2023-10-24 16:01:21','2023-10-24 16:01:21');
INSERT INTO sys_menu VALUES ('213','194','0','删除定时广告','','','1','0','/web/quntask/del','_self','','','0','','','0','3','2023-10-24 16:01:33','2023-10-24 16:01:33');
INSERT INTO sys_menu VALUES ('214','208','0','修改设置','','','1','0','/app/appStore/qverify/update_set','_self','','','0','','','0','3','2023-10-27 15:46:21','2023-10-27 15:46:21');
INSERT INTO sys_menu VALUES ('215','0','0','用户列表','/bot/userlist','/bot/userlist.vue','0','28','','_self','el-icon-user','','0','','','0','3','2023-10-30 14:46:08','2023-10-30 14:46:08');
INSERT INTO sys_menu VALUES ('216','0','0','群组管理','/bot/qunlist','/bot/qunlist.vue','0','30','','_self','el-icon-_user-group','','0','','','0','3','2023-10-30 14:46:45','2023-10-30 14:46:45');
INSERT INTO sys_menu VALUES ('217','0','0','频道管理','/bot/pindaolist','/bot/pindaolist','0','32','','_self','el-icon-_appointment-ok','','0','','','0','3','2023-10-30 14:47:22','2023-10-30 14:47:22');
INSERT INTO sys_menu VALUES ('218','0','1','充值管理','/bot/pay','/bot/pay','0','888','','_self','el-icon-_rmb','','0','','','0','3','2023-10-30 14:51:38','2023-10-30 14:51:38');
INSERT INTO sys_menu VALUES ('219','218','1','充值订单','/bot/paylist','/bot/paylist','0','2','','_self','el-icon-document','','0','','','0','3','2023-10-30 14:52:26','2023-10-30 14:52:26');
INSERT INTO sys_menu VALUES ('220','218','1','流水记录','/bot/buylist','/bot/buylist','0','8','','_self','el-icon-shopping-cart-full','','0','','','0','3','2023-10-30 14:53:18','2023-10-30 14:53:18');
INSERT INTO sys_menu VALUES ('221','0','0','通用API功能','/api','/api','0','999','','_self','el-icon-folder-opened','','1','','','0','3','2023-11-01 13:49:54','2023-11-01 13:49:54');
INSERT INTO sys_menu VALUES ('222','221','0','发送电报消息','','','1','0','/app/appStore/bot/sendmessage','_self','','','0','','','0','3','2023-11-01 13:51:08','2023-11-01 13:51:08');
INSERT INTO sys_menu VALUES ('223','216','0','机器人退群','','','1','0','/app/appStore/bot/exitqun','_self','','','0','','','0','3','2023-11-01 15:01:09','2023-11-01 15:01:09');
INSERT INTO sys_menu VALUES ('224','217','0','机器人退频道','','','1','0','/app/appStore/bot/exitpd','_self','','','0','','','0','3','2023-11-01 15:01:34','2023-11-01 15:01:34');
INSERT INTO sys_menu VALUES ('225','192','1','群聊天积分','/bot/jyxzdz','/bot/jyxzdz','0','2','','_self','el-icon-_prerogative','','0','','','0','3','2023-11-03 11:09:59','2023-11-03 11:09:59');
INSERT INTO sys_menu VALUES ('226','225','0','新增','','','1','0','/app/appStore/jyxzdz/create','_self','','','0','','','0','3','2023-11-03 11:12:09','2023-11-03 11:12:09');
INSERT INTO sys_menu VALUES ('227','225','0','修改','','','1','0','/app/appStore/jyxzdz/update','_self','','','0','','','0','3','2023-11-03 11:12:23','2023-11-03 11:12:23');
INSERT INTO sys_menu VALUES ('228','225','0','删除','','','1','0','/app/appStore/jyxzdz/del','_self','','','0','','','0','3','2023-11-03 11:12:33','2023-11-03 11:12:33');
INSERT INTO sys_menu VALUES ('229','225','0','修改积分设置','','','1','0','/app/appStore/jyxzdz/update_set','_self','','','0','','','0','3','2023-11-03 11:36:16','2023-11-03 11:36:16');
INSERT INTO sys_menu VALUES ('230','0','0','机器人','/bot/list','/bot/list','0','24','','_self','el-icon-s-promotion','','0','','','0','3','2023-11-11 05:03:37','2023-11-11 05:03:37');
INSERT INTO sys_menu VALUES ('231','230','1','托管设置','/bot/autobot','/bot/autobot','0','6','','_self','el-icon-_setting','','0','','','0','3','2023-11-11 06:06:49','2023-11-11 06:06:49');
INSERT INTO sys_menu VALUES ('232','231','0','删除','','','1','0','/app/appStore/autobot/del','_self','','','0','','','0','3','2023-11-11 06:11:10','2023-11-11 06:11:10');
INSERT INTO sys_menu VALUES ('233','231','0','新增','','','1','0','/app/appStore/autobot/create','_self','','','0','','','0','3','2023-11-11 06:11:32','2023-11-11 06:11:32');
INSERT INTO sys_menu VALUES ('234','231','0','更新','','','1','0','/app/appStore/autobot/update','_self','','','0','','','0','3','2023-11-11 06:11:46','2023-11-11 06:11:46');
INSERT INTO sys_menu VALUES ('235','231','0','托管设置修改','','','1','0','/app/appStore/autobot/update_set','_self','','','0','','','0','3','2023-11-11 07:35:58','2023-11-11 07:35:58');
INSERT INTO sys_menu VALUES ('236','0','0','用户管理','/bot/uuu','/bot/uuu','0','27','','_self','el-icon-user','','0','','','1','3','2023-11-11 10:40:57','2023-11-11 10:40:57');
INSERT INTO sys_menu VALUES ('237','236','1','充值记录','/bot/pay','/bot/pay','0','2','','_self','','','0','','','1','3','2023-11-11 10:42:03','2023-11-11 10:42:03');
INSERT INTO sys_menu VALUES ('238','218','1','提现订单','/bot/tixian','/bot/tixian','0','3','','_self','el-icon-_salary','','0','','','0','3','2023-11-11 10:48:54','2023-11-11 10:48:54');
INSERT INTO sys_menu VALUES ('239','0','1','红包管理','/bot/hongbao','/bot/hongbao','0','80','','_self','el-icon-_red-packet','','0','','','0','3','2023-11-11 10:49:58','2023-11-11 10:49:58');
INSERT INTO sys_menu VALUES ('240','239','0','新增','','','1','1','/web/hongbao/create','_self','','','0','','','0','3','2023-11-11 10:50:35','2023-11-11 10:50:35');
INSERT INTO sys_menu VALUES ('241','239','0','修改','','','1','2','/web/hongbao/update','_self','','','0','','','0','3','2023-11-11 10:50:51','2023-11-11 10:50:51');
INSERT INTO sys_menu VALUES ('242','239','0','删除','','','1','3','/web/hongbao/del','_self','','','0','','','0','3','2023-11-11 10:51:00','2023-11-11 10:51:00');
INSERT INTO sys_menu VALUES ('243','218','1','充值配置','/bot/payset','/bot/payset','0','0','','_self','el-icon-_setting','','0','','','0','3','2023-11-11 11:13:54','2023-11-11 11:13:54');
INSERT INTO sys_menu VALUES ('244','243','0','修改配置','','','1','0','/app/appStore/money/update_set','_self','','','0','','','0','3','2023-11-12 13:15:36','2023-11-12 13:15:36');
INSERT INTO sys_menu VALUES ('245','238','0','修改','','','1','0','/app/appStore/tixian/update','_self','','','0','','','0','3','2023-11-12 13:16:14','2023-11-12 13:16:14');
INSERT INTO sys_menu VALUES ('246','219','0','修改','','','1','0','/app/appStore/pay/update','_self','','','0','','','0','3','2023-11-12 13:16:33','2023-11-12 13:16:33');
INSERT INTO sys_menu VALUES ('247','219','0','删除','','','1','2191','/app/appStore/pay/del','_self','','','0','','','0','3','2023-11-12 13:16:46','2023-11-12 13:16:46');
INSERT INTO sys_menu VALUES ('248','238','0','删除','','','1','0','/app/appStore/tixian/del','_self','','','0','','','0','3','2023-11-12 13:17:00','2023-11-12 13:17:00');
INSERT INTO sys_menu VALUES ('249','239','0','修改红包设置','','','1','0','/web/hongbao/update_set','_self','','','0','','','0','3','2023-11-15 16:03:02','2023-11-15 16:03:02');
INSERT INTO sys_menu VALUES ('250','221','0','用户余额操作','','','1','2','/app/appStore/bot/money','_self','','','0','','','0','3','2023-11-16 14:34:51','2023-11-16 14:34:51');
INSERT INTO sys_menu VALUES ('251','185','0','新增','','','1','0','/web/usdtjt/create','_self','','','0','','','0','3','2023-11-16 15:17:44','2023-11-16 15:17:44');
INSERT INTO sys_menu VALUES ('252','185','0','修改','','','1','0','/web/usdtjt/update','_self','','','0','','','0','3','2023-11-16 15:17:58','2023-11-16 15:17:58');
INSERT INTO sys_menu VALUES ('253','174','1','兑换记录','/bot/trxlog','/bot/trxlog','0','15','','_self','el-icon-_table','','0','','','0','3','2023-11-19 13:49:15','2023-11-19 13:49:15');
INSERT INTO sys_menu VALUES ('254','176','0','更新','','','1','0','/app/appStore/gdtrx/update','_self','','','0','','','0','3','2023-11-19 13:51:08','2023-11-19 13:51:08');
INSERT INTO sys_menu VALUES ('255','176','0','新增','','','1','0','/app/appStore/gdtrx/create','_self','','','0','','','0','3','2023-11-19 13:51:33','2023-11-19 13:51:33');
INSERT INTO sys_menu VALUES ('256','221','0','拉黑地址','','','1','0','/app/appStore/trx/lahei','_self','','','0','','','0','3','2023-11-19 13:52:23','2023-11-19 13:52:23');
INSERT INTO sys_menu VALUES ('257','253','0','补发','','','1','0','/app/appStore/gdtrx/bufa','_self','','','0','','','0','3','2023-11-19 13:52:49','2023-11-19 13:52:49');
INSERT INTO sys_menu VALUES ('258','169','0','模块菜单设定','','','1','0','/app/appStore/plugin/update','_self','','','0','','','0','3','2023-12-05 23:08:29','2023-12-05 23:08:29');
INSERT INTO sys_menu VALUES ('259','180','1','监控设置','/usdtjt/usdtjt_set','/web/usdtjt/set','0','0','','_self','el-icon-_setting','','0','','','0','3','2023-12-06 21:26:44','2023-12-06 21:26:44');
INSERT INTO sys_menu VALUES ('260','180','1','付费用户','/usdtjt/buylist','/web/usdtjt/buylist','0','5','','_self','el-icon-document','','0','','','0','3','2023-12-07 23:23:42','2023-12-07 23:23:42');
INSERT INTO sys_menu VALUES ('261','0','1','能量租赁','/bot/nengliang','','0','42','','_self','el-icon-s-order','','0','','','0','3','2023-12-08 12:19:41','2023-12-08 12:19:41');
INSERT INTO sys_menu VALUES ('262','261','1','设置','/nengliang/set','/bot/nengliangSet','0','0','','_self','','','0','','','0','3','2023-12-08 12:20:47','2023-12-08 12:20:47');
INSERT INTO sys_menu VALUES ('263','261','1','租赁订单','/nengliang/list','/bot/nengliangList','0','2','','_self','','','0','','','0','3','2023-12-08 12:21:36','2023-12-08 12:21:36');
INSERT INTO sys_menu VALUES ('264','261','1','预支订单','/nengliang/yzlog','/bot/nengliangYzlog','0','5','','_self','','','0','','','0','3','2023-12-08 12:22:21','2023-12-08 12:22:21');
INSERT INTO sys_menu VALUES ('265','261','1','自动能量地址','/nengliang/auto','/bot/nengliangAuto','0','10','','_self','','','0','','','0','3','2023-12-08 12:23:45','2023-12-08 12:23:45');
INSERT INTO sys_menu VALUES ('266','261','0','新增','','','1','100','/app/appStore/nengliang/create','_self','','','0','','','0','3','2023-12-08 12:31:55','2023-12-08 12:31:55');
INSERT INTO sys_menu VALUES ('267','261','0','修改','','','1','100','/app/appStore/nengliang/update','_self','','','0','','','0','3','2023-12-08 12:32:13','2023-12-08 12:32:13');
INSERT INTO sys_menu VALUES ('268','261','0','删除','','','1','100','/app/appStore/nengliang/del','_self','','','0','','','0','3','2023-12-08 12:32:32','2023-12-08 12:32:32');
INSERT INTO sys_menu VALUES ('294','192','1','自动同意进群','/jinqun/jinqunset','/web/jinqun/jinqunset','0','1','','_self','el-icon-finished','','0','','','0','3','2023-12-13 19:49:36','2023-12-22 12:48:48');
INSERT INTO sys_menu VALUES ('295','294','0','新增','','','1','2941','/web/jinqun/create','_self','','','0','','','0','3','2023-12-13 20:20:31','2023-12-13 20:20:31');
INSERT INTO sys_menu VALUES ('296','294','0','修改','','','1','2941','/web/jinqun/update','_self','','','0','','','0','3','2023-12-13 20:20:31','2023-12-13 20:20:31');
INSERT INTO sys_menu VALUES ('297','294','0','删除','','','1','2941','/web/jinqun/del','_self','','','0','','','0','3','2023-12-13 20:20:31','2023-12-13 20:20:31');
INSERT INTO sys_menu VALUES ('298','192','1','上课下课','/shangke/set','/web/shangke/set','0','1','','_self','el-icon-moon-night','','0','','','0','3','2023-12-22 12:47:01','2023-12-22 12:48:48');
INSERT INTO sys_menu VALUES ('299','298','0','新增','','','1','2981','/web/shangke/create','_self','','','0','','','0','3','2023-12-22 12:47:01','2023-12-22 12:47:01');
INSERT INTO sys_menu VALUES ('300','298','0','修改','','','1','2981','/web/shangke/update','_self','','','0','','','0','3','2023-12-22 12:47:01','2023-12-22 12:47:01');
INSERT INTO sys_menu VALUES ('301','298','0','删除','','','1','2981','/web/shangke/del','_self','','','0','','','0','3','2023-12-22 12:47:01','2023-12-22 12:47:01');
INSERT INTO sys_menu VALUES ('302','298','0','新增','','','1','2981','/web/shangke/create','_self','','','0','','','1','3','2023-12-22 12:48:48','2023-12-22 12:48:48');
INSERT INTO sys_menu VALUES ('304','298','0','删除','','','1','2981','/web/shangke/del','_self','','','0','','','1','3','2023-12-22 12:48:48','2023-12-22 12:48:48');
INSERT INTO sys_menu VALUES ('305','298','0','新增','','','1','2981','/web/shangke/create','_self','','','0','','','1','3','2023-12-22 14:43:41','2023-12-22 14:43:41');
INSERT INTO sys_menu VALUES ('307','298','0','删除','','','1','2981','/web/shangke/del','_self','','','0','','','1','3','2023-12-22 14:43:41','2023-12-22 14:43:41');
INSERT INTO sys_menu VALUES ('308','221','0','群发消息','','','1','0','/app/appStore/bot/qunfa','_self','','','0','','','0','3','2023-12-25 17:20:26','2023-12-25 17:20:26');
INSERT INTO sys_menu VALUES ('309','294','0','新增','','','1','2941','/web/jinqun/create','_self','','','0','','','1','3','2023-12-28 12:47:22','2023-12-28 12:47:22');
INSERT INTO sys_menu VALUES ('311','294','0','删除','','','1','2941','/web/jinqun/del','_self','','','0','','','1','3','2023-12-28 12:47:22','2023-12-28 12:47:22');
INSERT INTO sys_menu VALUES ('313','221','0','群发消息','','','1','1','/app/appStore/bot/qunfa2','_self','','','0','','','1','3','2024-01-04 13:18:18','2024-01-04 13:18:18');
INSERT INTO sys_menu VALUES ('314','221','0','群发消息','','','1','1','/app/appStore/bot/qunfa3','_self','','','0','','','1','3','2024-01-04 13:27:51','2024-01-04 13:27:51');
INSERT INTO sys_menu VALUES ('315','221','0','群发消息','','','1','1','/app/appStore/bot/qunfa4','_self','','','0','','','1','3','2024-01-04 13:28:19','2024-01-04 13:28:19');
INSERT INTO sys_menu VALUES ('316','0','1','牛牛记账消除','/bot/niucount','','0','200','','_self','el-icon-_transfer','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:17:17');
INSERT INTO sys_menu VALUES ('317','316','1','授权使用群','/niucount/set','/web/niucount/set','0','1','','_self','el-icon-finished','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:18:29');
INSERT INTO sys_menu VALUES ('318','316','1','数据列表','/niucount/list','/web/niucount/list','0','2','','_self','el-icon-document','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:18:29');
INSERT INTO sys_menu VALUES ('319','316','0','新增','','','1','888','/web/niucount/create','_self','','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:17:17');
INSERT INTO sys_menu VALUES ('320','316','0','修改','','','1','889','/web/niucount/update','_self','','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:17:17');
INSERT INTO sys_menu VALUES ('321','316','0','删除','','','1','890','/web/niucount/del','_self','','','0','','','0','3','2024-01-14 12:17:17','2024-01-14 12:17:17');
INSERT INTO sys_menu VALUES ('322','0','1','汇旺验群设置','/bot/he444bot','','0','200','','_self','el-icon-_user-group','','0','','','0','3','2024-01-15 23:31:17','2024-01-15 23:31:17');
INSERT INTO sys_menu VALUES ('323','322','1','验群设定','/he444bot/set','/web/he444bot/set','0','1','','_self','el-icon-chat-line-round','','0','','','0','3','2024-01-15 23:31:17','2024-01-16 02:04:56');
INSERT INTO sys_menu VALUES ('324','322','0','新增','','','1','888','/web/he444bot/create','_self','','','0','','','0','3','2024-01-15 23:31:17','2024-01-15 23:31:17');
INSERT INTO sys_menu VALUES ('325','322','0','修改','','','1','889','/web/he444bot/update','_self','','','0','','','0','3','2024-01-15 23:31:17','2024-01-15 23:31:17');
INSERT INTO sys_menu VALUES ('326','322','0','删除','','','1','890','/web/he444bot/del','_self','','','0','','','0','3','2024-01-15 23:31:17','2024-01-15 23:31:17');
INSERT INTO sys_menu VALUES ('327','322','1','群编号列表','/he444bot/list','/web/he444bot/list','0','2','','_self','el-icon-_table','','0','','','0','3','2024-01-16 02:04:56','2024-01-16 02:05:10');
INSERT INTO sys_menu VALUES ('328','216','0','删除','','','1','5','/app/appStore/bot_group/del','_self','','','0','','','0','3','2024-01-16 11:57:39','2024-01-16 11:57:39');
INSERT INTO sys_menu VALUES ('329','216','0','更新','','','1','4','/app/appStore/bot_group/update','_self','','','0','','','0','3','2024-01-16 11:57:53','2024-01-16 11:57:53');
INSERT INTO sys_menu VALUES ('330','192','0','新增','','','1','888','/web/shangke/create','_self','','','0','','','0','3','2024-01-16 12:29:58','2024-01-16 12:29:58');
INSERT INTO sys_menu VALUES ('331','192','0','修改','','','1','889','/web/shangke/update','_self','','','0','','','0','3','2024-01-16 12:29:58','2024-01-16 12:29:58');
INSERT INTO sys_menu VALUES ('332','192','0','删除','','','1','890','/web/shangke/del','_self','','','0','','','0','3','2024-01-16 12:29:58','2024-01-16 12:29:58');
INSERT INTO sys_menu VALUES ('430','0','0','安装小程序','','','1','1','/app/appStore/webapp/install','_self','','','0','','','0','3','2024-08-22 21:57:22','2024-08-22 21:57:22');
INSERT INTO sys_menu VALUES ('431','0','0','卸载小程序','','','1','1','/app/appStore/webapp/uninstall','_self','','','0','','','0','3','2024-08-22 21:57:22','2024-08-22 21:57:22');
INSERT INTO sys_menu VALUES ('432','0','0','购买小程序','','','1','1','/app/appStore/webapp/buy','_self','','','0','','','0','3','2024-08-22 21:57:22','2024-08-22 21:57:22');
INSERT INTO sys_menu VALUES ('433','0','1','双向联系设置','/bot/shuang','','0','200','','_self','el-icon-headset','','0','','','0','3','2024-11-20 15:06:22','2024-11-20 15:06:22');
INSERT INTO sys_menu VALUES ('434','433','1','客服群设置','/shuang/set','/web/shuang/set','0','1','','_self','el-icon-setting','','0','','','0','3','2024-11-20 15:06:22','2024-11-20 15:06:22');
INSERT INTO sys_menu VALUES ('435','433','0','新增','','','1','888','/web/shuang/create','_self','','','0','','','0','3','2024-11-20 15:06:22','2024-11-20 15:06:22');
INSERT INTO sys_menu VALUES ('436','433','0','修改','','','1','889','/web/shuang/update','_self','','','0','','','0','3','2024-11-20 15:06:22','2024-11-20 15:06:22');
INSERT INTO sys_menu VALUES ('437','433','0','删除','','','1','890','/web/shuang/del','_self','','','0','','','0','3','2024-11-20 15:06:22','2024-11-20 15:06:22');
INSERT INTO sys_menu VALUES ('438','0','1','电报账号中心','/bot/tgclient','','0','200','','_self','el-icon-_user-add','','0','','','0','3','2024-12-03 15:57:03','2024-12-03 15:57:03');
INSERT INTO sys_menu VALUES ('439','438','1','账号列表','/tgclient/list','/web/tgclient/list','0','1','','_self','el-icon-tickets','','0','','','0','3','2024-12-03 15:57:03','2024-12-03 15:57:03');
INSERT INTO sys_menu VALUES ('440','438','0','新增','','','1','888','/web/tgclient/create','_self','','','0','','','0','3','2024-12-03 15:57:03','2024-12-03 15:57:03');
INSERT INTO sys_menu VALUES ('441','438','0','修改','','','1','889','/web/tgclient/update','_self','','','0','','','0','3','2024-12-03 15:57:03','2024-12-03 15:57:03');
INSERT INTO sys_menu VALUES ('442','438','0','删除','','','1','890','/web/tgclient/del','_self','','','0','','','0','3','2024-12-03 15:57:03','2024-12-03 15:57:03');


CREATE TABLE `sys_role` (
  `roleId` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色id',
  `roleName` varchar(200) NOT NULL COMMENT '角色名称',
  `roleCode` varchar(200) NOT NULL COMMENT '角色标识',
  `comments` varchar(400) DEFAULT NULL COMMENT '备注',
  `del` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除, 0否, 1是',
  `tenantId` int(11) NOT NULL DEFAULT '1' COMMENT '租户id',
  `theme` varchar(255) NOT NULL,
  `createTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`roleId`) USING BTREE,
  KEY `tenant_id` (`tenantId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色';

INSERT INTO sys_role VALUES ('1','系统管理员','admin','系统管理员','0','1','','2020-02-26 15:18:37','2020-03-21 15:15:54');
INSERT INTO sys_role VALUES ('9','超级模块管理员','botadmin','综合机器人超级管理员','0','3','','2023-08-27 11:52:47','2023-08-27 11:52:47');


CREATE TABLE `sys_role_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `roleId` int(11) NOT NULL,
  `userId` int(11) NOT NULL DEFAULT '0',
  `tenantId` int(11) NOT NULL,
  `menuText` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

INSERT INTO sys_role_menu VALUES ('1','1','0','1','[51,10,11,9]');
INSERT INTO sys_role_menu VALUES ('2','9','0','3','[440, 441, 442, 435, 436, 437, 86, 330, 331, 332, 166, 167, 168, 169, 258, 179, 181, 182, 183, 184, 191, 171, 178, 170, 230, 172, 186, 187, 190, 173, 231, 232, 233, 234, 235, 215, 216, 223, 329, 328, 217, 224, 254, 255, 257, 266, 267, 268, 188, 199, 251, 252, 295, 296, 297, 299, 300, 301, 226, 227, 228, 229, 209, 210, 211, 214, 205, 206, 207, 201, 202, 203, 196, 197, 198, 195, 212, 213, 249, 240, 241, 242, 319, 320, 321, 324, 325, 326, 244, 246, 247, 245, 248, 256, 308, 222, 250, 189, 430, 431, 432]');


CREATE TABLE `sys_tenantId` (
  `tenantId` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `name` varchar(16) NOT NULL,
  `text` varchar(100) NOT NULL,
  PRIMARY KEY (`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='租户';

INSERT INTO sys_tenantId VALUES ('1','0','总系统','all');
INSERT INTO sys_tenantId VALUES ('3','0','模块化机器人','电报机器人综合系统');


CREATE TABLE `sys_theme` (
  `userId` int(11) NOT NULL,
  `theme` text NOT NULL,
  PRIMARY KEY (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `sys_user_googel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `myid` int(11) NOT NULL,
  `menuText` text NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

INSERT INTO sys_user_googel VALUES ('1','100012','[57,39,50,3,4]');
INSERT INTO sys_user_googel VALUES ('2','100011','[57,39,50,3,4]');
INSERT INTO sys_user_googel VALUES ('3','100013','[57,39,50,3,4,65,66]');
INSERT INTO sys_user_googel VALUES ('4','100020','[57,39,50,3,4,65,66]');


CREATE TABLE `tb_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商户',
  `del` int(11) NOT NULL DEFAULT '0',
  `roleId` int(11) NOT NULL DEFAULT '0',
  `tenantId` int(11) NOT NULL DEFAULT '0',
  `upid` int(11) NOT NULL DEFAULT '0' COMMENT '上家id',
  `key` char(32) DEFAULT NULL COMMENT '通讯密匙',
  `username` char(15) DEFAULT NULL,
  `password` char(64) DEFAULT NULL,
  `spassword` varchar(11) DEFAULT NULL,
  `SecretKey` varchar(32) NOT NULL COMMENT '谷歌key',
  `tgid` bigint(20) NOT NULL,
  `plugin` varchar(32) NOT NULL,
  `remark` varchar(32) NOT NULL,
  `Telegram` json NOT NULL,
  `money` decimal(10,2) DEFAULT '5.00' COMMENT '余额',
  `tmoney` decimal(13,2) NOT NULL DEFAULT '0.00' COMMENT '待提现',
  `smoney` decimal(13,2) DEFAULT '0.00' COMMENT '流水',
  `ddnumber` int(11) DEFAULT '0' COMMENT '总订单',
  `rate` decimal(5,2) DEFAULT '1.38',
  `tel` decimal(11,0) DEFAULT '***********',
  `regtime` int(11) DEFAULT '**********',
  `api` int(11) DEFAULT '1' COMMENT '?可用',
  `google` int(11) NOT NULL DEFAULT '0' COMMENT '?谷歌',
  `post` int(11) NOT NULL DEFAULT '1' COMMENT '回调',
  `moshi` int(11) NOT NULL,
  `numMoney` decimal(10,2) NOT NULL,
  `sxf` decimal(10,2) NOT NULL,
  `etc` decimal(10,2) NOT NULL COMMENT '下发中',
  `sauto` int(11) NOT NULL DEFAULT '1' COMMENT '分笔',
  `webhook` int(11) NOT NULL,
  `webhookurl` varchar(64) NOT NULL,
  `dfset` int(11) NOT NULL,
  `DecSet` int(11) NOT NULL,
  `one` int(11) NOT NULL COMMENT '自己',
  `mt` int(11) NOT NULL COMMENT '扣自身余额',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `username` (`username`),
  KEY `tgid` (`tgid`),
  KEY `plugin` (`plugin`),
  KEY `remark` (`remark`)
) ENGINE=MyISAM AUTO_INCREMENT=100002 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

INSERT INTO tb_account VALUES ('100000','0','1','1','0','-','admin','******','123456','YZRAR3RWULHBITV3','0','','','null','100000.00','0.00','0.00','0','0.00','***********','**********','1','0','1','0','0.00','0.00','0.00','0','0','','0','0','0','0');
INSERT INTO tb_account VALUES ('100001','0','9','1','0','6FCADB80DA261E37889CF24333BC8049','97bot','$2y$10$ZYqEX0uEyP2mPrs5OjE6cOHMZmMY/TEQgm56ic85v4f86oVM0JFoC','888666','XUFZCIEDOPZIKUAS','0','','','null','0.00','0.00','0.00','0','0.00','***********','**********','1','0','1','0','0.00','0.00','0.00','1','0','','0','0','0','0');


CREATE TABLE `tb_account_tg` (
  `id` int(6) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL COMMENT '停',
  `roleId` int(2) NOT NULL DEFAULT '1' COMMENT '角色id',
  `up` bigint(20) NOT NULL COMMENT '邀请',
  `bot` varchar(32) NOT NULL COMMENT '所属机器人',
  `tgid` bigint(20) NOT NULL COMMENT '电报ID',
  `username` varchar(16) NOT NULL COMMENT '电报用户名',
  `name` varchar(32) NOT NULL COMMENT '称呼',
  `regtime` int(11) NOT NULL COMMENT '注册时间',
  `tgnum` int(5) NOT NULL COMMENT '邀请数量',
  `tgtrx` bigint(20) NOT NULL,
  `tgyue` bigint(20) NOT NULL,
  `dhnum` int(11) NOT NULL,
  `dhusdt` bigint(20) NOT NULL,
  `dhtrx` bigint(20) NOT NULL,
  `send` int(1) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`),
  KEY `up` (`up`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_adkill_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL DEFAULT '0' COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL,
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_autobot_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_autobot_set VALUES ('1','0','','1','','{\"1个月\": \"5\", \"6个月\": 25, \"12个月\": 50, \"下级返点\": \"80\", \"免费时长\": \"2\", \"免费模块\": [\"autobot\", \"money\", \"tgfl\", \"adkill\", \"chaid\", \"count\", \"hongbao\", \"kaiqun\", \"ntips\", \"quntask\", \"qverify\", \"troncha\", \"ukill\", \"usdtjt\", \"welcome\", \"z0usdt\"]}','0','0');


CREATE TABLE `tb_banklist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(11) NOT NULL DEFAULT '0',
  `name` varchar(16) NOT NULL,
  `png` varchar(16) NOT NULL DEFAULT 'no.png',
  `lv` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8;

INSERT INTO tb_banklist VALUES ('1','1','网商银行','no.png','0');
INSERT INTO tb_banklist VALUES ('2','0','农业银行','no.png','0');
INSERT INTO tb_banklist VALUES ('3','1','邮储银行','no.png','0');
INSERT INTO tb_banklist VALUES ('4','1','建设银行','no.png','0');
INSERT INTO tb_banklist VALUES ('5','1','工商银行','no.png','0');
INSERT INTO tb_banklist VALUES ('6','1','交通银行','no.png','0');
INSERT INTO tb_banklist VALUES ('7','1','招商银行','no.png','0');
INSERT INTO tb_banklist VALUES ('8','1','光大银行','no.png','0');
INSERT INTO tb_banklist VALUES ('9','1','中信银行','no.png','0');
INSERT INTO tb_banklist VALUES ('10','1','浦发银行','no.png','0');
INSERT INTO tb_banklist VALUES ('11','1','平安银行','no.png','0');
INSERT INTO tb_banklist VALUES ('12','1','兴业银行','no.png','0');
INSERT INTO tb_banklist VALUES ('13','1','民生银行','no.png','0');
INSERT INTO tb_banklist VALUES ('14','1','中国银行','no.png','0');
INSERT INTO tb_banklist VALUES ('15','0','四川农信','no.png','0');
INSERT INTO tb_banklist VALUES ('16','1','柳州银行','no.png','0');
INSERT INTO tb_banklist VALUES ('17','1','邢台银行','no.png','0');
INSERT INTO tb_banklist VALUES ('18','0','河北省农村信用社','no.png','0');
INSERT INTO tb_banklist VALUES ('19','0','黑龙江农村信用社','no.png','0');
INSERT INTO tb_banklist VALUES ('20','0','吉林农村信用社','no.png','0');
INSERT INTO tb_banklist VALUES ('21','1','黑龙江农村商业银行','no.png','0');


CREATE TABLE `tb_bot_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(12) NOT NULL,
  `trx` decimal(12,2) NOT NULL,
  `usdt` decimal(12,2) NOT NULL,
  `up` varchar(888) NOT NULL,
  `down` varchar(888) NOT NULL,
  `rate` decimal(4,2) NOT NULL DEFAULT '5.00',
  `user` varchar(32) NOT NULL,
  `name` varchar(64) NOT NULL,
  `userinfo` json DEFAULT NULL,
  `json` json DEFAULT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_bot_account VALUES ('1','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','tgymw22','TG源码网','{\"id\": **********, \"type\": \"private\", \"username\": \"tgymw22\", \"last_name\": \"源码网\", \"first_name\": \"TG\"}','{\"privateID\": 221}','**********','**********');
INSERT INTO tb_bot_account VALUES ('2','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','未设置用户名','啊你','{\"id\": **********, \"type\": \"private\", \"last_name\": \"你\", \"first_name\": \"啊\"}','{\"privateID\": 13}','**********','**********');
INSERT INTO tb_bot_account VALUES ('3','0','tgymwjt_bot','**********','0.00','0.00','','','99.00','tgymwnet','TG源码网','{\"id\": **********, \"type\": \"private\", \"username\": \"tgymwnet\", \"last_name\": \"源码网\", \"first_name\": \"TG\"}','{\"privateID\": 11}','**********','**********');
INSERT INTO tb_bot_account VALUES ('4','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','kira38877','魏','{\"id\": **********, \"type\": \"private\", \"username\": \"kira38877\", \"first_name\": \"魏\"}','{\"privateID\": 114}','**********','**********');
INSERT INTO tb_bot_account VALUES ('5','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','GG_adviser','EvanDr','{\"id\": **********, \"type\": \"private\", \"username\": \"GG_adviser\", \"last_name\": \"Dr\", \"first_name\": \"Evan\"}','{\"privateID\": 261}','**********','**********');
INSERT INTO tb_bot_account VALUES ('6','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','GiantWhaleVentures','啊波🔥巨鲸出海(招代理)🔥','{\"id\": **********, \"type\": \"private\", \"username\": \"GiantWhaleVentures\", \"last_name\": \"🔥巨鲸出海(招代理)🔥\", \"first_name\": \"啊波\"}','{\"privateID\": 213}','**********','**********');
INSERT INTO tb_bot_account VALUES ('7','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','cxx888998','三哥','{\"id\": **********, \"type\": \"private\", \"username\": \"cxx888998\", \"last_name\": \"哥\", \"first_name\": \"三\"}','{\"privateID\": 248}','**********','**********');
INSERT INTO tb_bot_account VALUES ('8','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','ylpifa','TRX兑换 | 能量租赁 | 船长','{\"id\": **********, \"type\": \"private\", \"username\": \"ylpifa\", \"first_name\": \"TRX兑换 | 能量租赁 | 船长\"}','{\"privateID\": 253}','**********','**********');
INSERT INTO tb_bot_account VALUES ('9','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','dhjsdjf','XD','{\"id\": **********, \"type\": \"private\", \"username\": \"dhjsdjf\", \"last_name\": \"D\", \"first_name\": \"X\"}','{\"privateID\": 259}','**********','**********');
INSERT INTO tb_bot_account VALUES ('10','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','zhang1717','𝒜𝒟𝒩𝒜𝒩','{\"id\": **********, \"type\": \"private\", \"username\": \"zhang1717\", \"first_name\": \"𝒜𝒟𝒩𝒜𝒩\"}','{\"privateID\": 268}','**********','**********');
INSERT INTO tb_bot_account VALUES ('11','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','未设置用户名','天机团队-M','{\"id\": **********, \"type\": \"private\", \"first_name\": \"天机团队-M\"}','{\"privateID\": 281}','**********','**********');
INSERT INTO tb_bot_account VALUES ('12','0','tgymwkf_bot','**********','0.00','0.00','','','99.00','aa223399bbb','Katrina Caydon','{\"id\": **********, \"type\": \"private\", \"username\": \"aa223399bbb\", \"first_name\": \"Katrina Caydon\"}','{\"privateID\": 289}','**********','**********');


CREATE TABLE `tb_bot_address_jt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plugin` varchar(32) NOT NULL COMMENT '应用',
  `bot` varchar(32) NOT NULL COMMENT '机器人',
  `type` int(11) NOT NULL DEFAULT '1',
  `url` varchar(64) NOT NULL COMMENT '回调通知地址',
  `username` varchar(32) NOT NULL COMMENT '用户',
  `tgid` bigint(20) NOT NULL COMMENT '用户id',
  `coin` varchar(8) NOT NULL COMMENT '币',
  `address` char(48) NOT NULL COMMENT '地址',
  `addressHex` varchar(42) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `coin` (`coin`),
  KEY `address` (`address`),
  KEY `tgid` (`tgid`),
  KEY `addressHex` (`addressHex`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_bot_address_jt VALUES ('1','','tgymwjt_bot','1','','tgymwnet','**********','TRC','THr6U59ijyC56vp5YDBLsb3pnnAnbk4ibg','415668a3d843acc38c393e12495c671a758f134b65','1732761202','1732761202');
INSERT INTO tb_bot_address_jt VALUES ('2','','tgymwkf_bot','3','','tgymwnet','**********','TRC','THr6U59ijyC56vp5YDBLsb3pnnAnbk4ibg','415668a3d843acc38c393e12495c671a758f134b65','1733113927','1733301724');
INSERT INTO tb_bot_address_jt VALUES ('3','','tgymwkf_bot','3','','tgymwnet','**********','TRC','你的钱包地址用于充值收款','0160d7c468','1733119617','0');


CREATE TABLE `tb_bot_channel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `plugin` varchar(16) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `pid` bigint(20) NOT NULL,
  `title` varchar(64) NOT NULL,
  `info` json NOT NULL,
  `admin` json DEFAULT NULL,
  `update_time` int(10) NOT NULL,
  `create_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `time` (`update_time`),
  KEY `pid` (`pid`),
  KEY `title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_delurl` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mark` varchar(32) NOT NULL,
  `time` int(11) NOT NULL,
  `url` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `time` (`time`),
  KEY `mark` (`mark`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `plugin` varchar(32) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `qunid` bigint(20) NOT NULL,
  `quninfo` json DEFAULT NULL,
  `admin` json DEFAULT NULL COMMENT '?管理员',
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `plugin` (`plugin`),
  KEY `qunid` (`qunid`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='机器人加群表';

INSERT INTO tb_bot_group VALUES ('1','0','tgbot','tgymwkf_bot','-4515203698','{\"id\": -4515203698, \"type\": \"group\", \"title\": \"客服群\", \"all_members_are_administrators\": true}','','1732087034','1732087034');


CREATE TABLE `tb_bot_group_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `userid` bigint(20) NOT NULL COMMENT '用户ID',
  `userinfo` json NOT NULL COMMENT '用户信息',
  `qunid` bigint(20) NOT NULL COMMENT '群ID',
  `quninfo` json NOT NULL COMMENT '群信息',
  `ufrom` json NOT NULL COMMENT '邀请人',
  `cretae_time` int(10) NOT NULL COMMENT '进入时间',
  `update_time` int(10) NOT NULL,
  `exit_time` int(10) NOT NULL COMMENT '退群时间',
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='群用户';

INSERT INTO tb_bot_group_user VALUES ('1','0','**********','{\"id\": **********, \"is_bot\": false, \"username\": \"tgymw22\", \"last_name\": \"源码网\", \"first_name\": \"TG\", \"language_code\": \"zh-hans\"}','-4515203698','{\"id\": -4515203698, \"type\": \"group\", \"title\": \"客服群\", \"all_members_are_administrators\": true}','null','1732087034','0','0');
INSERT INTO tb_bot_group_user VALUES ('2','0','7720491582','{\"id\": 7720491582, \"is_bot\": true, \"username\": \"TGymkf_bot\", \"first_name\": \"TG源码网开发测试\"}','-4515203698','{\"id\": -4515203698, \"type\": \"group\", \"title\": \"客服群\", \"all_members_are_administrators\": true}','{\"id\": **********, \"is_bot\": false, \"username\": \"tgymw22\", \"last_name\": \"源码网\", \"first_name\": \"TG\", \"language_code\": \"zh-hans\"}','0','1732198649','0');


CREATE TABLE `tb_bot_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(11) NOT NULL,
  `zt` int(1) NOT NULL DEFAULT '1',
  `upbot` varchar(32) NOT NULL,
  `plugin` varchar(32) NOT NULL,
  `appStore` json DEFAULT NULL,
  `API_BOT` varchar(32) NOT NULL COMMENT '机器用户名',
  `WEB_URL` varchar(64) NOT NULL COMMENT '部署域名',
  `WEB_IP` varchar(15) NOT NULL COMMENT '部署IP',
  `API_URL` varchar(64) NOT NULL DEFAULT 'https://api.telegram.org/bot' COMMENT '电报API',
  `API_TOKEN` varchar(64) NOT NULL COMMENT '机器人TOKEN',
  `Admin` bigint(11) NOT NULL DEFAULT '1418208536' COMMENT '管理员ID',
  `AdminName` varchar(32) NOT NULL,
  `json` json DEFAULT NULL,
  `manage` json DEFAULT NULL COMMENT '管理员列表',
  `start` text NOT NULL COMMENT '启动文字',
  `autodel` int(4) NOT NULL DEFAULT '300',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `outime` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zt` (`zt`),
  KEY `API_BOT` (`API_BOT`),
  KEY `plugin` (`plugin`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_bot_list VALUES ('1','0','1','','tgbot','[\"shuang\", \"chaid\", \"money\", \"z0usdt\"]','tgymwkf_bot','http://127.0.0.1:8686/app/tgbot','*************','http://127.0.0.1:3021/bot','8087068888:AAE6mj6ClKqIg-b2L_dwVMw7p7bqEFlCtD8','**********','tgymwnet','{\"menu\": 0, \"command\": \"start,启动机器人\\nid,查看ID\", \"允许键盘\": [\"chaid\", \"money\"], \"响应方式\": \"消息键盘\", \"消息模式\": 1, \"消息类型\": 0, \"隐藏键盘\": 0}','','欢迎使用TG源码网客服机器人','300','2024-11-20 15:15:26','1890662400');
INSERT INTO tb_bot_list VALUES ('2','0','1','','tgbot','[\"troncha\", \"usdtjt\", \"money\"]','tgymwjt_bot','http://127.0.0.1:8686/app/tgbot','*************','http://127.0.0.1:3021/bot','7237473320:AAF0J2-opl5um1XyOb2mralMfljfrmsbZ1I','**********','tgymwnet','{\"menu\": 0, \"command\": \"start,启动机器人\\nid,查看ID\", \"允许键盘\": [\"usdtjt\", \"money\", \"troncha\"], \"响应方式\": \"消息键盘\", \"消息模式\": 1, \"消息类型\": 0, \"隐藏键盘\": 0}','','欢迎使用TRC20地址监听机器人，本机器人来自TG源码网（www.tgymw.net）','300','2024-11-28 10:31:50','2048428800');


CREATE TABLE `tb_bot_list_tuoguan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `upbot` varchar(32) NOT NULL,
  `upadmin` bigint(12) NOT NULL,
  `mybot` varchar(32) NOT NULL,
  `myadmin` bigint(12) NOT NULL,
  `up` varchar(888) NOT NULL,
  `down` varchar(888) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `upbot` (`upbot`),
  KEY `mybot` (`mybot`),
  KEY `myadmin` (`myadmin`),
  KEY `upadmin` (`upadmin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_log_jie` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `plugin` varchar(16) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `auto` int(1) NOT NULL COMMENT '模式0自动1手动',
  `type` int(1) NOT NULL COMMENT '1借2还',
  `money` decimal(12,2) NOT NULL COMMENT '数量',
  `address` varchar(48) NOT NULL,
  `zt` int(1) NOT NULL COMMENT '1成功2失败',
  `hash` varchar(64) NOT NULL,
  `time` int(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预支记录';



CREATE TABLE `tb_bot_total_d` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `dated` int(11) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `user` int(11) NOT NULL,
  `unuser` int(10) NOT NULL,
  `tguser` int(11) NOT NULL,
  `numu` int(11) NOT NULL,
  `usdt` bigint(20) NOT NULL,
  `numt` int(11) NOT NULL,
  `trx` bigint(20) NOT NULL,
  `jie` decimal(12,2) NOT NULL,
  `huan` decimal(12,2) NOT NULL,
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `del` (`del`),
  KEY `dated` (`dated`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_bot_total_d VALUES ('1','0','20241120','tgymwkf_bot','2','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('2','0','20241128','tgymwjt_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('3','0','20241130','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('4','0','20241202','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('5','0','20241204','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','1733244064');
INSERT INTO tb_bot_total_d VALUES ('6','0','20241205','tgymwkf_bot','2','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('7','0','20241207','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('8','0','20241209','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('9','0','20241210','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');
INSERT INTO tb_bot_total_d VALUES ('10','0','20241211','tgymwkf_bot','1','0','0','0','0','0','0','0.00','0.00','**********');


CREATE TABLE `tb_bot_total_h` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `dateh` int(11) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `botid` int(11) NOT NULL,
  `numu` int(11) NOT NULL,
  `usdt` bigint(20) NOT NULL,
  `numt` int(11) NOT NULL,
  `trx` bigint(20) NOT NULL,
  `jie` decimal(12,2) NOT NULL,
  `huan` decimal(12,2) NOT NULL,
  `time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `del` (`del`),
  KEY `dateh` (`dateh`),
  KEY `bot` (`bot`),
  KEY `botid` (`botid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_total_tg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `date` int(8) NOT NULL,
  `time` int(11) NOT NULL,
  `tgnum` int(11) NOT NULL,
  `account` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `date` (`date`),
  KEY `tgid` (`tgid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日推广统计表';



CREATE TABLE `tb_bot_usdt_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `txid` varchar(64) NOT NULL,
  `ufrom` varchar(34) NOT NULL,
  `uto` varchar(34) NOT NULL,
  `value` bigint(20) NOT NULL,
  `time` int(10) NOT NULL,
  `huilv` decimal(10,2) NOT NULL,
  `oktxid` varchar(64) NOT NULL,
  `huan` decimal(12,2) NOT NULL COMMENT '还款数量',
  `oktrx` decimal(10,2) NOT NULL,
  `okzt` int(1) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `oktime` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `txid_2` (`txid`),
  KEY `bot` (`bot`),
  KEY `txid` (`txid`),
  KEY `zt` (`okzt`),
  KEY `oktxid` (`oktxid`),
  KEY `ufrom` (`ufrom`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='usdt收款列表';



CREATE TABLE `tb_bot_vip_paylog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plugin` varchar(32) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(64) NOT NULL,
  `num` int(11) NOT NULL,
  `amout` bigint(20) NOT NULL,
  `time` int(11) NOT NULL,
  `oktime` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_vip_setup` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plugin` varchar(16) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `hash` varchar(32) NOT NULL,
  `cookie` json DEFAULT NULL,
  `3` decimal(10,2) NOT NULL DEFAULT '15.00',
  `6` decimal(10,2) NOT NULL DEFAULT '30.00',
  `12` decimal(10,2) NOT NULL DEFAULT '55.00',
  `24` decimal(10,2) NOT NULL DEFAULT '100.00',
  PRIMARY KEY (`id`),
  UNIQUE KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_vip_userlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(32) NOT NULL,
  `name` varchar(64) NOT NULL,
  `rec` varchar(64) NOT NULL,
  `photo` text NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_bot_xufei_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plugin` varchar(16) NOT NULL COMMENT '插件',
  `bot` varchar(32) NOT NULL COMMENT '续费机器人',
  `tgid` bigint(20) NOT NULL COMMENT '发起人TGID',
  `msgid` int(11) NOT NULL,
  `addr` varchar(34) NOT NULL COMMENT '收款地址',
  `payaddr` varchar(34) NOT NULL COMMENT '付款人地址',
  `txid` varchar(64) NOT NULL,
  `zt` int(1) NOT NULL COMMENT '状态',
  `tday` int(11) NOT NULL COMMENT '续费时长',
  `money` decimal(12,2) NOT NULL COMMENT '订单金额',
  `time` int(10) NOT NULL COMMENT '创建时间',
  `oktime` int(10) NOT NULL COMMENT '成功时间',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`),
  KEY `zt` (`zt`),
  KEY `plugin` (`plugin`),
  KEY `txid` (`txid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_faka_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_gdtrx_address` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `disable` int(1) NOT NULL COMMENT '黑名单',
  `address` varchar(34) NOT NULL,
  `send` int(1) NOT NULL,
  `numu` int(10) NOT NULL,
  `usdt` decimal(12,2) NOT NULL,
  `numt` int(10) NOT NULL,
  `trx` decimal(12,2) NOT NULL,
  `jiet` decimal(12,2) NOT NULL COMMENT '贷款trx',
  `create_time` int(11) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tgid` (`tgid`),
  KEY `address` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_gdtrx_dhlog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `zt` int(1) NOT NULL COMMENT '0提交 1成功 2失败',
  `qudao` varchar(16) NOT NULL COMMENT '渠道',
  `from` varchar(34) NOT NULL COMMENT '地址',
  `to` varchar(34) NOT NULL,
  `usdt` decimal(12,2) NOT NULL,
  `hash` varchar(64) NOT NULL,
  `trx` decimal(12,2) NOT NULL COMMENT '回T数量',
  `hash2` varchar(64) NOT NULL COMMENT 'hash回',
  `huan` decimal(12,2) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `hash` (`hash`),
  KEY `hash_2` (`hash`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预支记录';



CREATE TABLE `tb_gdtrx_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(32) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_gdtrx_set VALUES ('1','0','','1','','{\"下级抽成\": \"8\", \"付款钱包\": \"\", \"允许预支\": 1, \"兑换说明\": \"<code>本机器人24小时自动兑换TRX,给地址转USDT就自动回TRX给您！</code>\\n\\n【<b>当前实时汇率</b>】\\n{huilv}\\n\\n<b>钱包地址</b> <u>禁止交易所地址转入</u>\\n{address}\", \"开启预支\": 1, \"收款钱包\": \"\", \"最低兑换\": \"1\", \"汇率抽成\": \"10\", \"通知模板\": \"<b>新的订单·兑换成功</b>✅\\n付款地址：<code>{from}</code>\\n兑换数量：<b>{usdt} USDT</b>\\n兑换获得：<b>{trx} TRX</b>\\n\\n<b>该地址兑换数据：</b>\\n{total}\", \"兑换说明图\": \"https://telegra.ph/file/caa1f5ee9a712397b3ad9.jpg\", \"兑换通知群\": [], \"自定义按钮\": [], \"预支TRX数量\": \"20\", \"允许余额闪兑\": 0, \"预支兑换记录\": \"10\", \"预支单日上限\": \"30\", \"预支电报会员\": 0}','0','2023');


CREATE TABLE `tb_gdtrx_yzlog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(32) NOT NULL,
  `zt` int(1) NOT NULL,
  `address` varchar(34) NOT NULL,
  `trx` decimal(12,2) NOT NULL,
  `hash` varchar(64) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `address` (`address`),
  KEY `bot` (`bot`),
  KEY `address_2` (`address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_guanjianci_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `num` int(11) NOT NULL COMMENT '计次',
  `text` varchar(32) NOT NULL COMMENT '关键词',
  `tgid` tinytext NOT NULL COMMENT '通知人id',
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `text` (`text`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_guanjianci_list2` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `text` varchar(64) NOT NULL,
  `tgid` bigint(16) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `text` (`text`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_guanjianci_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_guanjianci_set VALUES ('1','0','test','1','test','{\"test\": 1}','1703831195','1703831195');


CREATE TABLE `tb_he444bot_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bianhao` varchar(8) NOT NULL,
  `qunid` bigint(20) NOT NULL,
  `qunuser` varchar(64) NOT NULL,
  `qunname` varchar(64) NOT NULL,
  `json` json NOT NULL,
  `quninfo` json NOT NULL,
  `update_time` int(10) NOT NULL,
  `create_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `qunuser` (`qunuser`),
  KEY `qunname` (`qunname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='专群列表';



CREATE TABLE `tb_he444bot_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_he444bot_set VALUES ('1','bbot','','99','123','{\"jia\": \"该群是假群，请联系汇旺客服 @hwdb 核实。\", \"kefu\": \"he444\\nhe999\", \"zhen\": \"恭喜,该编号有真群,请注意核对群内成员,可点击下方官方人员用户名查看共同群。\\n1、官方人员，汇旺担保机器人，用户名： @he444bot \\n2、官方人员，汇旺建群号㉜，用户名： @hwbb92 \\n3、官方人员，汇旺五组交易员②，用户名： @hwjy43 \\n4、官方人员，汇旺五组审计④，用户名： @hwff43 \\n5、官方人员，汇旺五组财务，用户名： @hwgf60\"}','1970','1705376260');
INSERT INTO tb_he444bot_set VALUES ('2','','','0','123','{\"jia\": \"该群是假群，请联系汇旺客服 @hwdb 核实。\", \"kefu\": \"he444\\nhe999\", \"zhen\": \"恭喜,该编号有真群,请注意核对群内成员,可点击下方官方人员用户名查看共同群。\\n1、官方人员，汇旺担保机器人，用户名： @he444bot \\n2、官方人员，汇旺建群号㉜，用户名： @hwbb92 \\n3、官方人员，汇旺五组交易员②，用户名： @hwjy43 \\n4、官方人员，汇旺五组审计④，用户名： @hwff43 \\n5、官方人员，汇旺五组财务，用户名： @hwgf60\"}','1970','2024');


CREATE TABLE `tb_hongbao_detai` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hbid` int(11) NOT NULL,
  `tgid` bigint(12) NOT NULL,
  `user` varchar(16) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `hbid` (`hbid`),
  KEY `tgid` (`tgid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_hongbao_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zt` int(11) NOT NULL DEFAULT '1',
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(12) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(15) NOT NULL,
  `coin` varchar(6) NOT NULL,
  `money` decimal(12,2) NOT NULL,
  `num` int(10) NOT NULL,
  `moneys` decimal(12,2) NOT NULL,
  `nums` int(10) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tgid` (`tgid`),
  KEY `user` (`user`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_hongbao_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_hongbao_set VALUES ('1','0','','1','','{\"最低个数\": 1, \"最大金额\": 999, \"最小金额\": \"1\", \"最高个数\": 99, \"允许用户撤回\": 1}','0','0');


CREATE TABLE `tb_ipsafe` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `myid` int(11) NOT NULL COMMENT 'myid',
  `type` int(11) NOT NULL COMMENT '1登录2请求3代付',
  `ip` text NOT NULL COMMENT 'ip内容',
  `url` text NOT NULL COMMENT '白名单url',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `myid` (`myid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;



CREATE TABLE `tb_jinqun_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_jinqun_set VALUES ('1','0','','1','CDN|高防|主机|云|服务器|海外|屏蔽|sd|交易所|im','','0','1703831195');


CREATE TABLE `tb_keep_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `qunid` bigint(20) NOT NULL,
  `did` int(11) NOT NULL COMMENT '日记录ID',
  `huilv` decimal(10,2) NOT NULL,
  `feilv` decimal(10,2) NOT NULL,
  `def` decimal(12,2) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `usdt` decimal(10,2) NOT NULL,
  `from` varchar(32) NOT NULL COMMENT '操作人',
  `reply` json NOT NULL,
  `time` int(11) NOT NULL,
  `date` int(8) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `time` (`time`),
  KEY `del` (`del`),
  KEY `did` (`did`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_keep_logc` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `qunid` bigint(20) NOT NULL,
  `did` int(11) NOT NULL COMMENT '日记录ID',
  `type` int(1) NOT NULL COMMENT '出款类型0u 1人民币',
  `huilv` decimal(10,2) NOT NULL COMMENT '汇率',
  `usdt` decimal(12,2) NOT NULL COMMENT 'usdt数量',
  `money` decimal(12,2) NOT NULL COMMENT '人民币数量',
  `from` varchar(16) NOT NULL,
  `reply` json NOT NULL,
  `time` int(11) NOT NULL,
  `date` int(8) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `time` (`time`),
  KEY `del` (`del`),
  KEY `did` (`did`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_keep_setup` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `qunid` bigint(20) NOT NULL,
  `info` json DEFAULT NULL,
  `huilv` decimal(10,2) NOT NULL,
  `sshuilv` int(1) NOT NULL COMMENT '实时汇率',
  `dangwei` int(1) NOT NULL DEFAULT '1',
  `weitiao` decimal(4,2) NOT NULL COMMENT '微调',
  `feilv` decimal(10,2) NOT NULL,
  `rmb` int(1) NOT NULL DEFAULT '1' COMMENT '显示人民币',
  `decmoshi` int(1) NOT NULL COMMENT '出款模式0u 1人民币',
  `admin` varchar(255) NOT NULL COMMENT '管理员',
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `qunid` (`qunid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_keep_total` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `save` int(1) NOT NULL DEFAULT '0' COMMENT '储存',
  `qunid` bigint(20) NOT NULL COMMENT '群ID',
  `info` json DEFAULT NULL,
  `date` int(8) NOT NULL COMMENT '时间表达',
  `incnum` int(11) NOT NULL COMMENT '入款笔数',
  `defmoney` decimal(12,2) NOT NULL,
  `incmoney` decimal(12,2) NOT NULL COMMENT '入款money',
  `incusdt` decimal(12,2) NOT NULL COMMENT '入款usdt',
  `decnum` int(11) NOT NULL,
  `decmoney` decimal(12,2) NOT NULL,
  `decusdt` decimal(12,2) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `date` (`date`),
  KEY `del` (`del`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_keep_totalz` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `qunid` bigint(20) NOT NULL COMMENT '群ID',
  `info` json DEFAULT NULL,
  `def` decimal(12,2) NOT NULL COMMENT '总金额(原始)',
  `num` int(11) NOT NULL COMMENT '总入款笔数',
  `money` decimal(12,2) NOT NULL COMMENT '应下发金额',
  `usdt` decimal(12,2) NOT NULL COMMENT '应下发usdt',
  `decnum` int(11) NOT NULL COMMENT '出款笔数',
  `decmoney` decimal(12,2) NOT NULL COMMENT '已下发money',
  `decusdt` decimal(12,2) NOT NULL COMMENT '已下发usdt',
  `date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '日期',
  `deltime` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qunid` (`qunid`),
  KEY `del` (`del`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_keep_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `type` int(1) NOT NULL COMMENT '1正常2回复',
  `qunid` bigint(20) NOT NULL,
  `userid` bigint(20) NOT NULL,
  `username` varchar(32) NOT NULL,
  `num1` int(11) NOT NULL,
  `money1` decimal(14,2) NOT NULL,
  `num2` int(11) NOT NULL,
  `money2` decimal(14,2) NOT NULL,
  `date` int(8) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(11) NOT NULL DEFAULT '0' COMMENT '显示',
  `myid` int(11) NOT NULL COMMENT '商户id',
  `type` varchar(8) NOT NULL COMMENT '消息类型',
  `title` varchar(32) NOT NULL COMMENT '标题',
  `class` varchar(16) NOT NULL COMMENT '颜色calss',
  `icon` varchar(32) NOT NULL COMMENT 'icon',
  `content` text COMMENT '私信内容',
  `avatar` varchar(100) DEFAULT NULL COMMENT '私信头像',
  `status` int(11) NOT NULL DEFAULT '2' COMMENT '待办状态',
  `description` varchar(32) DEFAULT NULL COMMENT '待办日期',
  `time` int(11) NOT NULL COMMENT '时间',
  `read` int(11) NOT NULL DEFAULT '0' COMMENT '已阅',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`),
  KEY `myid` (`myid`),
  KEY `type` (`type`),
  KEY `read` (`read`),
  KEY `show` (`del`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

INSERT INTO tb_message VALUES ('1','0','100001','notice','请修改后台默认密码','hong','','你的后台密码是默认的123456请务必修改','','2','','1732086162','0');
INSERT INTO tb_message VALUES ('2','0','100001','notice','你未设置后台白名单IP','','','建议你开启后台白名单IP<br>机器人根目录下：.env文件中最下面检查是否有以下参数(没有自己加进去)<br>okip=白名单IP','','2','','1732086162','0');
INSERT INTO tb_message VALUES ('3','0','100001','notice','你未设置后台白名单IP','','','建议你开启后台白名单IP<br>机器人根目录下：.env文件中最下面检查是否有以下参数(没有自己加进去)<br>okip=白名单IP','','2','','1732759949','0');
INSERT INTO tb_message VALUES ('4','0','100001','notice','你未设置后台白名单IP','','','建议你开启后台白名单IP<br>机器人根目录下：.env文件中最下面检查是否有以下参数(没有自己加进去)<br>okip=白名单IP','','2','','1733873975','0');
INSERT INTO tb_message VALUES ('5','0','100001','notice','你未设置后台白名单IP','','','建议你开启后台白名单IP<br>机器人根目录下：.env文件中最下面检查是否有以下参数(没有自己加进去)<br>okip=白名单IP','','2','','1733962584','0');
INSERT INTO tb_message VALUES ('6','0','100001','notice','你未设置后台白名单IP','','','建议你开启后台白名单IP<br>机器人根目录下：.env文件中最下面检查是否有以下参数(没有自己加进去)<br>okip=白名单IP','','2','','1734081564','0');


CREATE TABLE `tb_money_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_money_set VALUES ('1','0','','1','tgymwkf_bot','{\"允许提现\": 0, \"允许转账\": 1, \"充值地址\": \"THr6U59ijyC56vp5YDBLsb3pnnAnbk4ibg\", \"最低充值TRX\": \"1\", \"最低提现TRX\": \"2\", \"最低转账TRX\": \"3\", \"最低充值USDT\": \"1\", \"最低提现USDT\": \"2\", \"最低转账USDT\": \"5\"}','1708267280','1708267280');


CREATE TABLE `tb_moneylog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '1充值 2提现 3转账 4消费',
  `bot` varchar(32) NOT NULL,
  `zt` int(1) NOT NULL,
  `uid` int(10) NOT NULL,
  `tgid` bigint(12) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(16) NOT NULL,
  `coin` varchar(8) NOT NULL,
  `lmoney` decimal(12,2) NOT NULL,
  `money` decimal(12,2) NOT NULL,
  `nmoney` decimal(12,2) NOT NULL,
  `value` varchar(88) NOT NULL,
  `addr1` varchar(34) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `addr2` varchar(34) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `zt` (`zt`),
  KEY `tgid` (`tgid`),
  KEY `update_time` (`update_time`),
  KEY `create_time` (`create_time`),
  KEY `bot` (`bot`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_mybot` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `byuser` varchar(16) NOT NULL,
  `name` varchar(32) NOT NULL,
  `version` varchar(16) NOT NULL,
  `usdt` decimal(16,6) NOT NULL,
  `text` text NOT NULL,
  `path` varchar(150) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `create_time` (`create_time`),
  KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='功能模块';

INSERT INTO tb_mybot VALUES ('1','gd国际','TRX兑换','1.0.0','15.000000','USDT自动换TRX','/myapp/trx.zip','1693104746','1693104746');


CREATE TABLE `tb_nengliang_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_niucount_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_niucount_set VALUES ('1','0','','1','-1001810783822\n-1001810783822','{\"qunlist\": [123456, 456789]}','0','1705301249');


CREATE TABLE `tb_niucountbak` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `qunid` bigint(11) NOT NULL,
  `num` int(10) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `val` varchar(2) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `json` json NOT NULL,
  `total` int(11) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='牛牛记账表';



CREATE TABLE `tb_niucountlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `del` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `qunid` bigint(11) NOT NULL,
  `num` int(10) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `val` varchar(2) NOT NULL,
  `money` decimal(10,2) NOT NULL,
  `json` json NOT NULL,
  `total` int(11) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='牛牛记账表';



CREATE TABLE `tb_pay_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msgid` int(11) NOT NULL,
  `zt` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(15) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(15) NOT NULL,
  `coin` varchar(6) NOT NULL,
  `value` decimal(12,2) NOT NULL,
  `hash` varchar(64) NOT NULL,
  `addr1` varchar(34) NOT NULL,
  `addr2` varchar(34) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`),
  KEY `coin` (`coin`),
  KEY `msgid` (`msgid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_pay_list VALUES ('1','155','0','tgymwkf_bot','**********','GG_adviser','EvanDr','TRX','100.00','','','','','1733110209','0');
INSERT INTO tb_pay_list VALUES ('2','198','0','tgymwkf_bot','**********','GG_adviser','EvanDr','TRX','10.00','','','','','1733119634','0');
INSERT INTO tb_pay_list VALUES ('3','241','0','tgymwkf_bot','**********','cxx888998','三哥','USDT','10.00','','','','','1733387604','0');
INSERT INTO tb_pay_list VALUES ('4','265','0','tgymwkf_bot','**********','GG_adviser','EvanDr','TRX','10.00','','','','','1733647589','0');


CREATE TABLE `tb_qiandao_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_qunfa_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_quntask_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_qverify` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL DEFAULT '0',
  `zh_CN` varchar(8) NOT NULL,
  `en` varchar(32) NOT NULL,
  `tu` varchar(12) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='群验证图形';

INSERT INTO tb_qverify VALUES ('2','0','蛇','snake','🐍','1698417839','1698417839');
INSERT INTO tb_qverify VALUES ('3','0','？气很大','octopus','🔥','1698417839','1698422423');
INSERT INTO tb_qverify VALUES ('4','0','狐狸','fox','🦊','1698417839','1698417839');
INSERT INTO tb_qverify VALUES ('5','0','狗','dog','🐶','1698417839','1698418100');
INSERT INTO tb_qverify VALUES ('9','0','戒指','ring','💍','1698421693','1698421693');


CREATE TABLE `tb_qverify_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_qverify_set VALUES ('1','全局设置','','0','','{\"失败上限\": 1, \"验证功能\": 1, \"验证时限\": \"10\", \"验证次数\": \"3\", \"验证超时\": 1, \"默认语言\": \"zh_CN\", \"强制关注频道\": \"1234567\"}','0','0');


CREATE TABLE `tb_set_usdtjt` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人',
  `value` json DEFAULT NULL COMMENT '配置信息',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_set_usdtjt VALUES ('1','1','{\"buy\": 1, \"max\": 1}','0','0');


CREATE TABLE `tb_shangke_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_shangke_set VALUES ('2','0','','1','12','{\"上课\": \"<b>开始上班有业务请联系</b>\", \"下课\": \"<b>本群今日已下课</b>\\n\"}','1970','1705379364');


CREATE TABLE `tb_shuang_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_shuang_set VALUES ('1','0','test','1','test','{\"kefu\": \"1418208536\", \"qunid\": \"-4515203698\"}','1970','1732087000');
INSERT INTO tb_shuang_set VALUES ('2','tgymwkf_bot','test','1','test','{\"kefu\": \"1418208536\", \"qunid\": \"-4515203698\"}','1970','2024');


CREATE TABLE `tb_tgclient_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_tgclient_set VALUES ('1','0','test','1','test','{\"test\": 1}','1703831195','1703831195');


CREATE TABLE `tb_tgfl_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_tgvip_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_total` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `date` int(8) NOT NULL,
  `buy` decimal(12,2) NOT NULL,
  `tixian` decimal(12,2) NOT NULL,
  `zhuan` decimal(12,2) NOT NULL,
  `cztrx` decimal(12,2) NOT NULL,
  `czusdt` decimal(12,2) NOT NULL,
  `sendhb` decimal(12,2) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_troncha_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `value` json DEFAULT NULL COMMENT '配置信息',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_trx_setup` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plugin` varchar(32) NOT NULL,
  `bot` varchar(16) NOT NULL,
  `PrivateKey` varchar(64) NOT NULL COMMENT '钱包秘钥',
  `addr` varchar(34) NOT NULL,
  `TRON_API_KEY` varchar(48) NOT NULL COMMENT '波场APIKEY',
  `Ttime` int(5) NOT NULL DEFAULT '600' COMMENT '监听时间阈值',
  `maxusdt` decimal(5,2) NOT NULL DEFAULT '100.00' COMMENT '最大兑换U',
  `type` int(1) NOT NULL DEFAULT '1' COMMENT '兑换模式',
  `Rate` int(2) NOT NULL COMMENT '抽成比例',
  `Price` decimal(5,2) NOT NULL COMMENT '固定价格',
  `Minusdt` decimal(5,2) NOT NULL COMMENT '最小兑换',
  `fanli` int(2) NOT NULL,
  `yuzhi` json DEFAULT NULL,
  `shandui` bigint(20) NOT NULL,
  `okshandui` bigint(20) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `plugin` (`plugin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_ukill_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL DEFAULT '0' COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL,
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_usdtjt_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(32) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_usdtjt_set VALUES ('1','0','','0','','{\"style\": \"2\", \"通知按钮\": [], \"通知模板\": \"收款地址：{to}\\n付款地址：{from}\\n交易数量：{money}\\n交易哈希：{hash}\\n区块高度：{block}\\n订单时间：{time}\", \"单个地址收费\": \"15\", \"无限地址收费\": \"15\", \"免费监听地址数量\": \"2\"}','0','1970');


CREATE TABLE `tb_usdtjt_user_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(20) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(12) NOT NULL,
  `num` int(11) NOT NULL,
  `create_time` int(11) NOT NULL,
  `update_time` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



CREATE TABLE `tb_webapp` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(32) NOT NULL,
  `name` varchar(64) NOT NULL,
  `path` varchar(64) NOT NULL,
  `json` json NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序模版';



CREATE TABLE `tb_welcome_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL,
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;

INSERT INTO tb_welcome_set VALUES ('1','0','','0','欢迎用户：{user} \n用户昵称：{name} \n\n欢迎加入本群：{qunuser}   {qunname}\n','{\"图片地址\": \"AgACAgUAAxkBAAEBlshlONWIo9MRQ6jlqhi37hR__LBeeQACy7cxG_9LyFUsZapb2yt9AAEBAAMCAAN4AAMwBA\", \"开启欢迎\": 1, \"欢迎类型\": 1, \"视频地址\": \"BAACAgUAAxkBAAEBlsJlONTYl1kQZ7Izi8tRMK4NZcsPagACtQQAAtiAEVcTqY-FSG6CsjAE\"}','1698020273','1698492682');
