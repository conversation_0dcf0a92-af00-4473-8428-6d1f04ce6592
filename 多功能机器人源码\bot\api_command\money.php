<?php
namespace bot\api_command;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 

class money  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $ret支持回调参数：sendText(发送文字讯息) sendPhoto(发送照片) sendVideo(发送视频) sendFile(发送文件) huifu=1(强制用户回复) huifuTips(回复提示文字) anniu(消息按钮)   [ jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)] back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     * 
     * @param $id
     * @return array
     */
      
    
    #默认执行函数 
    public function index($message){  
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if(preg_match("/\/(\w+)\s*(.*)/i", $message["text"], $com)){
            if(count($com) != 3){ 
                return $ret;
            } 
        }else{
            return $ret;
        }
        $type   = $com[1]; //命令
        $value  = $com[2]; //参数
        
        #多机器人群内同命令时 只处理被@的机器人命令
        if(!empty($value)){
            if($value[0] == "@"){
                if(substr($value, 1) !=  $message["bot"]["API_BOT"]){
                    return $ret;
                } 
            }
        }
        #判断命令是否机器人admin才能用
        if(is_admin($type)){
            if($message["formId"] != $message["bot"]["Admin"]){
                return $ret;
            }
            
        }
        #-----------上面代码固定不要修改 level 视情况调整改动----------------  
        #下面开始写代码 
        
        switch ($type) {
            default: 
                break;
                
                
            case "pay":  
                $ret['back'] = 0;
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->where("del",0)->find(); 
                if(empty($user)){  
                   $ret["editText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
                   $ret["anniu"] = [ 
                        [
                            [
                                "text" => "🔴 点击启用机器人",
                                "url" => "https://t.me/{$message['bot']['API_BOT']}"
                            ]
                        ],
                    ];
                   return $ret;
                }
                if($message["chatType"] != "private"){
                   $ret['delMessage'] = 1;
                   $ret['sendText'] = "请私聊机器人充值余额";
                   return $ret; 
                }
                
                $ret['sendText'] = "<code>💰你正在进行充值...</code>\n\n®️用户名：@{$message["formUser"]}\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择充值余额类型：</b>";
                $ret['anniu'] = [
                            [
                                [
                                    "text" => "充值TRX",
                                    "callback_data" => "充值余额TRX"
                                ],
                                [
                                    "text" => "充值USDT",
                                    "callback_data" => "充值余额USDT"
                                ]
                            ],
                        ];  
                break; 
                
            case "tixian":   
                $ret['back'] = 0;
                if($message["chatType"] != "private"){
                    $ret['delMessage'] = 1;
                   $ret['sendText'] = "请私聊机器人使用提现功能";
                   return $ret; 
                }
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                $ret['sendText'] = "<code>💰你正在进行提现...</code>\n\n®️用户名：@{$message["formUser"]}\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择提现货币：</b>";
                $ret['anniu'] = [
                            [
                                [
                                    "text" => "提现TRX",
                                    "callback_data" => "TRX余额提现"
                                ],
                                [
                                    "text" => "提现USDT",
                                    "callback_data" => "USDT余额提现"
                                ]
                            ],
                        ]; 
                
                return $ret;
             
        }
        
         
        
        
        #代码结束  
        return $ret;  
    }
    
     
        
    
 
}
