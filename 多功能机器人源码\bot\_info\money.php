<?php
namespace bot\_info; 

 
class money  {  
    public function index(){    
         
        $info['core']       = 240310;
        
        $info['lv']         = 887;
           
        $info['key']        = "money";
         
        $info['name']       = "用户钱包余额";
        
        $info['text']       = "用户可以<span class='lan'>充值丶提现丶查询</span>余额用于兑换<span class='hong'>TRX,开会员,租能量,续费</span>等等用途"; 
         
        $info['version']    = "2.7";
        
        $info['new']        = "修复一个重大转账BUG问题务必更新";
        
        $info['menuId']     = [218, 243, 219, 238, 220];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["money01.png", "money02.png", "money03.png", "money04.png", "money1.png", "money2.png", "money3.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "pay,余额充值,0|tixian,余额提现,0";
 
        $info['com2']       = "";
        
        $info['auth']       = "d+Ae0Djf5oI6oC8HBgV/HqC6rKvkylYLobxkyPIp5b4yGgkGihloVm/FWhjpdHunIlJLYCJHveVih6S1FciXwrTo5ZPc6Srve8jXKRYiET0Hpx91jR8e7UmWdGrsfKOHGcJYK3B5waR9NaHh17oqVMwq26rcDkfORdV9G4m9GqI=";
 
        $info['tables']     = ["money_set", "moneylog", "total", "pay_list"];
        
        $info['btn']       = "🔰我的余额";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      