<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
use GuzzleHttp\Cookie\CookieJar;


class tgvips  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * next (不强制回复·但下一条文本消息将作为本次交互所需要的回复内容)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        
        #下面开始写代码
        
        //你定义了启动按钮：💎电报会员 请在下面写消息触发事件
        if($message["text"] == trans('启动按钮', [], 'tgvips')){
            Cache::delete("tgvips_0");
            $tgvip = new model\tgvips_set;
            $vipset = $tgvip->getJson($message['bot']['API_BOT']);
            if(empty($vipset['json']['3个月'])){
                $ret["sendText"] = "管理员未设置套餐价格";
                return $ret;
                
            } 
            $ret["sendText"] = trans('开通电报会员', [], 'tgvips');
            $ret["anniu"] = [
                        [
                            [
                                "text" => trans('开通电报会员', [], 'tgvips')."（".trans('3个月', [], 'tgvips')."） {$vipset['json']['3个月']}U",
                                "callback_data" => "开通电报会员|3"
                            ]
                        ],
                        [
                            [
                                "text" => trans('开通电报会员', [], 'tgvips')."（".trans('6个月', [], 'tgvips')."） {$vipset['json']['6个月']}U",
                                "callback_data" => "开通电报会员|6"
                            ],
                        ],
                        [
                            [
                                "text" => trans('开通电报会员', [], 'tgvips')."（  ".trans('12个月', [], 'tgvips')."   ）{$vipset['json']['12个月']}U",
                                "callback_data" => "开通电报会员|12"
                            ],
                        ],
                    ]; 
            return $ret;  
        }
        
         
        
        
        if(isset($message['action']) && $message['action'] == '给他人开通tgvip会员'){
            $message["actionText"] = str_replace("@", "", $message["actionText"]); 
            $message["actionText"] = str_replace("https://t.me/", "", $message["actionText"]);   
            if($ss = pipei($message["actionText"],"^(\w{4,})$")){ 
                $touser = $ss[1];
                $tgvip = new model\tgvips_set;
                $vipset = $tgvip->getJson($message['bot']['API_BOT']);
                $numt = pipei($message["oldText"],trans('开通时长', [], 'tgvips')."：([0-9]+)");  
                if(empty($numt[1])){
                    $ret["sendText"] ="<b>出错了..</b>\n<code>".trans('开通时长', [], 'tgvips')."获取失败</code>";
                    return $ret;
                }
                $numt = $numt[1]; 
                
                $httpVip = httpVip($vipset['json']['cookie']);
                $response = $httpVip->post("/api?hash={$vipset['json']['hash']}", [
                        'form_params' => [
                            'query' => $touser,
                            "months"=> $numt,
                            "method"=> "searchPremiumGiftRecipient"
                            ]
                    ]); 
                $json = json_decode($response->getBody()->getContents(), true);   
                 
                 
                
                if(isset($json['found']['recipient'])){
                    $tgvip_user = model\tgvips_user::where("rec",$json['found']['recipient'])->find();
                    if(empty($tgvip_user)){
                       $sql['user'] =  $touser;
                       $sql['name'] =  $json['found']['name'];
                       $sql['rec'] =  $json['found']['recipient'];
                       $tgvip_user = model\tgvips_user::create($sql);
                    }else{ 
                       $tgvip_user['user'] =  $touser;
                       $tgvip_user['name'] =  $json['found']['name']; 
                       $tgvip_user->save();
                    } 
                    
                    // $photo = pipei($json['found']['photo'],'src="(.+)"');
                    
                     
                    // if(!is_dir("/97bot/user")){
                    //     mkdir("/97bot/user", 0777);
                    // } 
                    
                    // if(isset($photo[1])){
                    //     file_put_contents("/97bot/user/{$touser}.jpg", file_get_contents($photo[1]));
                    // } 
                    // if(is_file("/97bot/user/{$touser}.jpg")){
                    //     $ret['sendPhoto'] = "/97bot/user/{$touser}.jpg";
                    // } 
                    
                    $numst = pipei($message["oldText"],trans('开通时长', [], 'tgvips')."：([0-9]+)");  
                    $numsu = pipei($message["oldText"],trans('订单金额', [], 'tgvips')."：([0-9]+)");  
                    
                    
                    $ret['sendText'] = "<code>".trans('你正在赠送电报会员', [], 'tgvips')."</code>\n\n".trans('开通时长', [], 'tgvips')."：<b>{$numst[1]}".trans('个月', [], 'tgvips')."</b>\n".trans('订单金额', [], 'tgvips')."：<b>{$numsu[1]}USDT</b>\n\n<b>【".trans('赠送目标', [], 'tgvips')."】</b>\n".trans('用户名', [], 'tgvips')."：<b>@{$touser}</b>";
                    $ret['sendText'] .= "\n".trans('昵称名', [], 'tgvips')."：<code>{$json['found']['name']}</code>"; 
                    $ret['nextdel'] = 1;
                    
                    
                    $ret["anniu"] = [
                        [
                             
                            [
                                "text" => "👌 ".trans('确认支付', [], 'tgvips'),
                                "callback_data" => "确认开通电报会员|{$touser}|{$numt}"
                            ]
                        ]
                    ]; 
                }else if(isset($json['error'])){
                    $ret["sendText"] ="<b>出错了..</b>\n<code>{$json['error']}</code>";
                    return $ret;
                }
 
            }else{ 
                $ret["sendText"] = trans('正在赠送错误', [], 'tgvips');
                return $ret;
            }
            
        }
         

        
        
        
        #代码结束
        return $ret;     
    }
} 
