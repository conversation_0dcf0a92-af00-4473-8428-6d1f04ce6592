<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>plugin</title>
  <!--系统CSS 你可以把css放到：/bot/res/xxx/目录下然后在下引用 引用只需要：/xxx/你的.css -->
  <link rel="stylesheet" href="/assets/css/diy.css">
  <link rel="stylesheet" href="/assets/css/list2.css">
  <link rel="stylesheet" href="/assets/font/iconfont.css">
  <!--第三方css-->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> 
  <style type="text/css"> 
    [v-cloak] { 
      display: none; 
    } 
  </style>  
</head>
<body>
<div id="app" v-cloak>
<div class="loca"> 
<div class="ele-body">
<el-row :gutter="10" class="row-bg">
<!--********以上代码不懂不要随意修改,下面开始写element vue代码*******-->

        <el-col :lg="12" :xs="24">
          <el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> 电报会员价格设置</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>

            <el-form ref="setform" :model="setForm" :rules="rules" label-width="128px" label-suffix="：" style="padding:10px 0"> 
            <el-row :gutter="10" class="row-bg">
            <!--<el-col :lg="24" :xs="24">-->
            <!--    <el-form-item label="设置机器人" size="small" prop="value">-->
            <!--      <el-select v-model="setForm.bot" allow-create filterable placeholder="请选择机器人" @change="remoteSeleok" style="width:180px">-->
            <!--        <el-option v-for="item in botlist" :key="item" :label="item" :value="item">-->
            <!--        </el-option>-->
            <!--      </el-select> -->
            <!--       &nbsp;-->
            <!--      <el-tooltip content="当其它机器人没有单独设置数据时,则默认使用该机器人的配置信息">-->
            <!--        <el-checkbox v-model="setForm.type" :true-label="99"  :false-label="0"  >设为总管机器人</el-checkbox>-->
            <!--      </el-tooltip>-->
            <!--    </el-form-item> -->
            <!--    </el-col>-->
            
            <el-col :lg="24" :xs="24">  
              <el-form-item   size="small"  prop="json.word">
                  <template slot="label">
                     TON助词器： 
                  </template> 
                  <el-input :type="passwordInputType" placeholder="TON钱包助词器(24个单词)" v-model="setForm.json['word']" :rows="2" @input="handleInput"   @blur="handleBlur">
                    <!--<template slot="suffix">-->
                    <!--  <i class="el-input__icon el-icon-view" @click="togglePasswordVisibility"></i>-->
                    <!--</template> -->
                  </el-input>
              </el-form-item>
             </el-col> 
             
            
            <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     钱包地址V4： 
                  </template> 
                  <el-input type="textarea" :rows="1" placeholder="TON钱包地址" v-model="setForm.json['address']"  :readonly="true" :disabled="true"> </el-input>  
              </el-form-item>
             </el-col>     
                
            
            <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     3个月售价
                    <el-tooltip content="电报会员开通3个月售价">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-input v-model="setForm.json['3个月']" placeholder="USDT"  style="width:140px">
                    <template slot="suffix"><span class="molv myico myico-USDT"></span></template> 
                  </el-input>
              </el-form-item>
             </el-col> 
             
             <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     6个月售价
                    <el-tooltip content="电报会员开通6个月售价">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-input v-model="setForm.json['6个月']" placeholder="USDT"  style="width:140px">
                    <template slot="suffix"><span class="molv myico myico-USDT"></span></template> 
                  </el-input>
              </el-form-item>
             </el-col>
             
             <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     1年售价
                    <el-tooltip content="电报会员开通1年售价">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-input v-model="setForm.json['12个月']" placeholder="USDT"  style="width:140px">
                    <template slot="suffix"><span class="molv myico myico-USDT"></span></template> 
                  </el-input>
              </el-form-item>
             </el-col>
             
             
            <el-col :lg="12" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     赠送他人会员
                    <el-tooltip content="是否允许给他人开通">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-switch v-model="setForm.json['给他人开通']"   :active-value="1" :inactive-value="0" >
                 </el-switch>
                  
                  
              </el-form-item>
             </el-col>
             
             
            <el-col :lg="12" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     配置Cookie
                    <el-tooltip content="2024-4-20后必须配置对应钱包的COOKIE 否则无法自动开通">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-switch v-model="gaoji"  >
                 </el-switch>
                  
                  
              </el-form-item>
             </el-col> 
             
               
            </el-row>  

              <el-form-item>
                  <!--<el-button  @click="load()">刷新</el-button> -->
                <el-button type="primary" @click="update()">保存设置</el-button>  
              </el-form-item>
              
              
              
              
              <div class="juzuo">
                  <span class="hong shuiyin ">如何配置TON服务？</span><br>
                  <span class="cu lan ">1.在上面填写TON助词器即可获得钱包地址</span><br>
                  <span class="cu lan ">2.你需要给自己的TON钱包地址(V4) 存入相应的TON 虚拟币(交易所购买或其它途径)</span><br>
                  <br>
                  
                  <span class="hong shuiyin">如何获取助词器？</span><br>
                <span class="qianlan ">方法1：飞机搜索机器人：<b>@wallet</b>  (打开钱包》右上角...》设置 》显示TON空间 打开，然后返回：就可以看到TON空间 点击进去探索》手动备份 就能看到助词器了) </span> <br><br>
                <span class="qianlan ">方法2：苹果appStore(非大陆appid登录) 或 安卓谷歌应用商店  搜索：Tonkeeper 下载安装创建一个钱包即可（这是飞机官方钱包APP）</span>
                  <br>
                  <br>
                  <span class="hong shuiyin">如何知道开会员成本价格？</span><br>
                  <span class="cu lan ">访问飞机会员官网：<a href="https://fragment.com/premium/gift" target="_blank">https://fragment.com/premium/gift</a> 点进去登录后就看到3个月 6个月 12个月 分别的$价格了 </span><br><br>
                  
                   
              </div>

            </el-form>

          </el-card>
        </el-col>
        
        
        
        
        
        
 <el-col :lg="12" :xs="24" v-if="this.gaoji == true">
          <el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> cookie配置</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>

            <el-form ref="setform" :model="setForm" label-width="128px" label-suffix="：" style="padding:10px 0"> 
            <el-row :gutter="10" class="row-bg">
 
            
            <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     hash
                    <el-tooltip content="hash请按下方教程获得">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-input type="textarea" :rows="10" placeholder="hash请按下方教程获得 " v-model="setForm.json['hash']"> </el-input> 
              </el-form-item>
             </el-col>  
             
             
            <el-col :lg="24" :xs="24">  
              <el-form-item   size="small" >
                  <template slot="label">
                     cookie
                    <el-tooltip content="cookie请按下方教程获得">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template> 
                  <el-input type="textarea" :rows="10" placeholder="cookie请按下方教程获得 " v-model="setForm.json['cookie']"> </el-input> 
             
                  
                  
              </el-form-item>
             </el-col>
             
               
            </el-row>  

              <el-form-item>
                  <!--<el-button  @click="load()">刷新</el-button> -->
                <el-button type="primary" @click="update()">保存设置</el-button>  
              </el-form-item>
              
              
              
              
              <div class="juzhong">
                <span class="qianlan "> <a href="https://telegra.ph/%E7%94%B5%E6%8A%A5%E8%87%AA%E5%8A%A8%E5%BC%80%E4%BC%9A%E5%91%98---%E9%85%8D%E7%BD%AECookie%E6%95%99%E7%A8%8B-04-23" target="_blank">  <span class="jiacu hong ">Hash COOkie 获取教程 （提示：配置不正确将无法自动开通会员）</span> </a>  </span>
              </div>

            </el-form>

          </el-card>
        </el-col>
 
         
    
    
    
        
<!--*****************************代码结束*************************-->
</el-row>
</div>
</div>
</div>
</body> 
<!--系统js 你可以把js放到：/bot/res/xxx/目录下然后在下引用 引用时只需要：/xxx/你的.js-->
<script src="/assets/js/vue.js"></script>  
<script src="/assets/js/axios.js"></script> 
<script src="/assets/js/clipboard.js"></script> 
 

<!--第三方js 如果你要加载其它js请填写到下面-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>  
 

<!--VUE js-->
<!--VUE js-->
<script>
var table = 'tgvips';
new Vue({
  el: '#app',
  //页面参数定义
  data() {
    return { 
        loading: true,  //加载状态 
        setForm:{json:{word:"",address:"请填写助词器"}}, 
        botlist:[],
        gaoji:true,
        tonaddress:'你没有配置TON钱包助词器',
        passwordInputType:'password',
        rules: {
            "json.word": [
                          { required: true, message: '必须填写助词器', trigger: 'blur' },
                          { pattern: /^([a-z]+ ){23}[a-z]+$/, message: '助词器已加密,若要修改请填新的助词器 24个单词(每个空格1个)', trigger: 'blur' }
                        ],
        }
    }
  },
  //数据监听
  watch: {
      
  },
  
  
  
  //初始化1·渲染前
  created() {
      this.load() 
  },
  
  
  
  //初始化2·渲染后
  mounted() { 
      this.loading = true;
        axios.get("/app/appStore/bot/index ",{}).then((res) => {
            this.loading = false;
            this.botlist = res.data.data
            this.botlist.unshift("全局设置")
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        }); 
       
      
     
  }, 
  
  
  
  //按钮点击事件方法
  methods: { 
    copy(obj,text){
        const clipboard = new ClipboardJS(obj, {
        text: function (trigger) {
                return text;
            }
        });
        clipboard.on('success', e => { 
            clipboard.destroy()
            this.$message.success('复制成功');
            return ;
        });
        clipboard.on('error', e => {
            clipboard.destroy()
            this.$message.error('复制失败'); 
            return ;
        });
    },   
    //以上按钮事件必备
    load(){ 
        axios.get("/web/"+table+"/setget",{params:{bot:this.setForm.bot}}).then((res) => { 
            if(!res.data.data){
                this.$message.error("该机器人没有单独设定,默认使用全局设置");
                return false
            }
            
            
            if (res.data.data.bot == 0 || res.data.data.bot == '0') {
                res.data.data.bot = "全局设置"  
            }
            this.setForm = res.data.data
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        }); 
        
        // axios.get("/web/"+table+"/getaddress",{params:{bot:this.setForm.bot}}).then((res) => {  
        //     if (res.data.data.address) {
        //         this.tonaddress = res.data.data.address;
        //     }
             
        // }).catch((e) => {
        //     this.loading = false;
        //     this.$message.error(e.message);
        // });
 
        
    },
    update(){
        this.$refs['setform'].validate((valid) => {
        if (!valid) { return false }
        //-----------form start 
            axios.post("/web/"+table+"/update",{type:"set",data:this.setForm}).then((res) => { 
                
                if(res.data.code == 1){
                    this.$message.success(res.data.msg);
                }else{
                    this.$message.error(res.data.msg);
                }
                
                 
                
            }).catch((e) => {
                this.loading = false;
                this.$message.error(e.message);
            });  
        //-----------form end
        });
        
    },
    remoteSeleok(){
       this.load() 
    },
    togglePasswordVisibility() {
      this.passwordInputType = this.passwordInputType == 'password' ? 'text' : 'password';
    },
    handleInput(){
        if(this.passwordInputType == 'password'){
            this.setForm['json']['word'] = "";
            this.passwordInputType =  'text';
        }
         
    },
    handleBlur(){
        const regex = /^([a-z]+ ){23}[a-z]+$/;
        if(!regex.test(this.setForm['json']['word'])){ 
            return false;
        }
        
            axios.post("/web/"+table+"/update",{type:"setword",word:this.setForm['json']['word']}).then((res) => { 
                
                this.setForm.json['address'] = res.data.address;
                this.tonaddress = res.data.address; 
                
            }).catch((e) => {
                this.loading = false;
                this.$message.error(e.message);
            });
    }
     
  }, 
  
  
  
  //离开页面前事件
  beforeDestroy () {
  },
})
</script>
</html>