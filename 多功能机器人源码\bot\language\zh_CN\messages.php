<?php
return [
    //这几个按钮 多语言时前面的图标不可动
    '切换语言' => "🌐 切换语言",
    '删除消息' => "⛔️ 删除消息",
    '返回上级' => "🔙 返回上级",
    '删除键盘' => "⛔️ 删除键盘",
    '返回' => "🔙 返回", 
    
    //前面是键 => 后面是值  (不要修改键名 只可以修改键值)
    '启动机器人'=>"启动机器人",
    '显示ID'=>"显示电报ID",
    '未设置用户名' => "未设置用户名", 
    '消息模块过期删除' => "模块消息过期删除\n请重新联系机器人", 
    '上一步模块重载成功' => "上一步·模块重载成功", 
    '模块重载成功' => "模块重载成功", 
    '键盘模块消息' => "键盘模块消息", 
    '键盘删除成功' => "键盘删除成功", 
    '模块加载成功' => "模块加载成功",  
    '发送模块消息' => "发送模块消息",  
    '未找到对应模块事件' => "未找到对应模块事件",  
    '机器人过期提示'=>"ㅤ\n<b>很抱歉·该机器人已过期</b>\n\n<code>到期时间：%date%</code>\n\n请老板 %tg% 私聊机器人进行自助续费",
    '模块无权操作提示'=>"消息归属：%toUser% 你无权操作",
    '未设置start消息提示'=>"机器人未设置启动消息内容\n\n发送命令：\n<code>设置启动消息</code><b>后面</b>写任意内容文字以设置启动消息内容\n\n举例发送以下内容：\n<code>设置启动消息 老板你好,请问你需要什么服务？</code>",
    '用户修改昵称提示'=>"用户修改昵称提示",
    '用户名'=>"用户名",
    '原昵称'=>"原昵称",
    '新昵称'=>"新昵称",
    '规避风险提示'=>"请注意规避风险,谨防假冒管理员",
    '未设置用户名'=>"未设置用户名",
];