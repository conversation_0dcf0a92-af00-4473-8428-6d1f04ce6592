[2025-03-11 11:04:01] default.ERROR: 162.158.114.230 GET bot.tgymw.net/app/appStore/index/appuserinfo
GuzzleHttp\Exception\ConnectException: cURL error 28: Operation timed out after 30001 milliseconds with 0 bytes received (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://www.97bot.com/index.php?s=app&c=Mlist&m=userinfo&local=38.47.221.217 in phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/CurlFactory.php:210
Stack trace:
#0 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/CurlFactory.php(158): GuzzleHttp\Handler\CurlFactory::createRejection(Object(GuzzleHttp\Handler\EasyHandle), Array)
#1 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/CurlFactory.php(110): GuzzleHttp\Handler\CurlFactory::finishError(Object(GuzzleHttp\Handler\CurlHandler), Object(GuzzleHttp\Handler\EasyHandle), Object(GuzzleHttp\Handler\CurlFactory))
#2 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/CurlHandler.php(47): GuzzleHttp\Handler\CurlFactory::finish(Object(GuzzleHttp\Handler\CurlHandler), Object(GuzzleHttp\Handler\EasyHandle), Object(GuzzleHttp\Handler\CurlFactory))
#3 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/Proxy.php(28): GuzzleHttp\Handler\CurlHandler->__invoke(Object(GuzzleHttp\Psr7\Request), Array)
#4 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Handler/Proxy.php(48): GuzzleHttp\Handler\Proxy::GuzzleHttp\Handler\{closure}(Object(GuzzleHttp\Psr7\Request), Array)
#5 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php(35): GuzzleHttp\Handler\Proxy::GuzzleHttp\Handler\{closure}(Object(GuzzleHttp\Psr7\Request), Array)
#6 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Middleware.php(31): GuzzleHttp\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\Psr7\Request), Array)
#7 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/RedirectMiddleware.php(71): GuzzleHttp\Middleware::GuzzleHttp\{closure}(Object(GuzzleHttp\Psr7\Request), Array)
#8 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Middleware.php(61): GuzzleHttp\RedirectMiddleware->__invoke(Object(GuzzleHttp\Psr7\Request), Array)
#9 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/HandlerStack.php(75): GuzzleHttp\Middleware::GuzzleHttp\{closure}(Object(GuzzleHttp\Psr7\Request), Array)
#10 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Client.php(331): GuzzleHttp\HandlerStack->__invoke(Object(GuzzleHttp\Psr7\Request), Array)
#11 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Client.php(168): GuzzleHttp\Client->transfer(Object(GuzzleHttp\Psr7\Request), Array)
#12 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/Client.php(187): GuzzleHttp\Client->requestAsync('GET', Object(GuzzleHttp\Psr7\Uri), Array)
#13 phar:///www/wwwroot/tgbot/97bot/vendor/guzzlehttp/guzzle/src/ClientTrait.php(44): GuzzleHttp\Client->request('GET', '/index.php?s=ap...', Array)
#14 phar:///www/wwwroot/tgbot/97bot/plugin/appStore/app/controller/IndexController.php(736): GuzzleHttp\Client->get('/index.php?s=ap...')
#15 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(319): plugin\appStore\app\controller\IndexController->appuserinfo(Object(support\Request))
#16 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(349): Webman\App::Webman\{closure}(Object(support\Request))
#17 phar:///www/wwwroot/tgbot/97bot/plugin/appStore/app/middleware/Jwt.php(112): Webman\App::Webman\{closure}(Object(support\Request))
#18 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(341): plugin\appStore\app\middleware\Jwt->process(Object(support\Request), Object(Closure))
#19 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(168): Webman\App::Webman\{closure}(Object(support\Request))
#20 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Connection/TcpConnection.php(646): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#21 [internal function]: Workerman\Connection\TcpConnection->baseRead(Resource id #2305, 2, Resource id #2305)
#22 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Events/Event.php(144): EventBase->loop()
#23 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1629): Workerman\Events\Event->loop()
#24 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1423): Workerman\Worker::forkOneWorkerForLinux(Object(Workerman\Worker))
#25 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1397): Workerman\Worker::forkWorkersForLinux()
#26 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(560): Workerman\Worker::forkWorkers()
#27 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/support/App.php(131): Workerman\Worker::runAll()
#28 phar:///www/wwwroot/tgbot/97bot/vendor/webman/console/src/Commands/StartCommand.php(29): support\App::run()
#29 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Command/Command.php(291): Webman\Console\Commands\StartCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#30 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(1014): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#31 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand(Object(Webman\Console\Commands\StartCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#32 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#33 phar:///www/wwwroot/tgbot/97bot/webman(45): Symfony\Component\Console\Application->run()
#34 /www/wwwroot/tgbot/97bot(5): require('phar:///www/www...')
#35 {main} [] []
