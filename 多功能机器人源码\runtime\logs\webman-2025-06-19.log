[2025-06-19 03:11:09] default.ERROR: 127.0.0.1 POST 127.0.0.1/app/tgbot/Telegram?bot=tgymwkfbot
ErrorException: Undefined array key "username" in phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php:508
Stack trace:
#0 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php(508): support\App::{closure}(2, 'Undefined array...', 'phar:///www/www...', 508)
#1 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(319): plugin\tgbot\app\controller\Telegram->index(Object(support\Request))
#2 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(349): Webman\App::Webman\{closure}(Object(support\Request))
#3 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/middleware/casbin.php(22): Webman\App::Webman\{closure}(Object(support\Request))
#4 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(341): plugin\tgbot\app\middleware\casbin->process(Object(support\Request), Object(Closure))
#5 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(141): Webman\App::Webman\{closure}(Object(support\Request))
#6 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Connection/TcpConnection.php(646): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#7 [internal function]: Workerman\Connection\TcpConnection->baseRead(Resource id #2445, 2, Resource id #2445)
#8 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Events/Event.php(144): EventBase->loop()
#9 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1629): Workerman\Events\Event->loop()
#10 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1423): Workerman\Worker::forkOneWorkerForLinux(Object(Workerman\Worker))
#11 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1397): Workerman\Worker::forkWorkersForLinux()
#12 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(560): Workerman\Worker::forkWorkers()
#13 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/support/App.php(131): Workerman\Worker::runAll()
#14 phar:///www/wwwroot/tgbot/97bot/vendor/webman/console/src/Commands/StartCommand.php(29): support\App::run()
#15 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Command/Command.php(291): Webman\Console\Commands\StartCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(1014): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand(Object(Webman\Console\Commands\StartCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#19 phar:///www/wwwroot/tgbot/97bot/webman(45): Symfony\Component\Console\Application->run()
#20 /www/wwwroot/tgbot/97bot(5): require('phar:///www/www...')
#21 {main} [] []
[2025-06-19 03:11:44] default.ERROR: 127.0.0.1 POST 127.0.0.1/app/tgbot/Telegram?bot=tgymwkfbot
ErrorException: Undefined array key "username" in phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php:508
Stack trace:
#0 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php(508): support\App::{closure}(2, 'Undefined array...', 'phar:///www/www...', 508)
#1 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(319): plugin\tgbot\app\controller\Telegram->index(Object(support\Request))
#2 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(349): Webman\App::Webman\{closure}(Object(support\Request))
#3 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/middleware/casbin.php(22): Webman\App::Webman\{closure}(Object(support\Request))
#4 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(341): plugin\tgbot\app\middleware\casbin->process(Object(support\Request), Object(Closure))
#5 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(141): Webman\App::Webman\{closure}(Object(support\Request))
#6 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Connection/TcpConnection.php(646): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#7 [internal function]: Workerman\Connection\TcpConnection->baseRead(Resource id #2445, 2, Resource id #2445)
#8 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Events/Event.php(144): EventBase->loop()
#9 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1629): Workerman\Events\Event->loop()
#10 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1423): Workerman\Worker::forkOneWorkerForLinux(Object(Workerman\Worker))
#11 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1397): Workerman\Worker::forkWorkersForLinux()
#12 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(560): Workerman\Worker::forkWorkers()
#13 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/support/App.php(131): Workerman\Worker::runAll()
#14 phar:///www/wwwroot/tgbot/97bot/vendor/webman/console/src/Commands/StartCommand.php(29): support\App::run()
#15 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Command/Command.php(291): Webman\Console\Commands\StartCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(1014): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand(Object(Webman\Console\Commands\StartCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#19 phar:///www/wwwroot/tgbot/97bot/webman(45): Symfony\Component\Console\Application->run()
#20 /www/wwwroot/tgbot/97bot(5): require('phar:///www/www...')
#21 {main} [] []
[2025-06-19 03:12:39] default.ERROR: 127.0.0.1 POST 127.0.0.1/app/tgbot/Telegram?bot=tgymwkfbot
ErrorException: Undefined array key "username" in phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php:508
Stack trace:
#0 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/controller/Telegram.php(508): support\App::{closure}(2, 'Undefined array...', 'phar:///www/www...', 508)
#1 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(319): plugin\tgbot\app\controller\Telegram->index(Object(support\Request))
#2 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(349): Webman\App::Webman\{closure}(Object(support\Request))
#3 phar:///www/wwwroot/tgbot/97bot/plugin/tgbot/app/middleware/casbin.php(22): Webman\App::Webman\{closure}(Object(support\Request))
#4 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(341): plugin\tgbot\app\middleware\casbin->process(Object(support\Request), Object(Closure))
#5 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/App.php(141): Webman\App::Webman\{closure}(Object(support\Request))
#6 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Connection/TcpConnection.php(646): Webman\App->onMessage(Object(Workerman\Connection\TcpConnection), Object(support\Request))
#7 [internal function]: Workerman\Connection\TcpConnection->baseRead(Resource id #2445, 2, Resource id #2445)
#8 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Events/Event.php(144): EventBase->loop()
#9 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1629): Workerman\Events\Event->loop()
#10 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1423): Workerman\Worker::forkOneWorkerForLinux(Object(Workerman\Worker))
#11 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(1397): Workerman\Worker::forkWorkersForLinux()
#12 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/workerman/Worker.php(560): Workerman\Worker::forkWorkers()
#13 phar:///www/wwwroot/tgbot/97bot/vendor/workerman/webman-framework/src/support/App.php(131): Workerman\Worker::runAll()
#14 phar:///www/wwwroot/tgbot/97bot/vendor/webman/console/src/Commands/StartCommand.php(29): support\App::run()
#15 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Command/Command.php(291): Webman\Console\Commands\StartCommand->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(1014): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(301): Symfony\Component\Console\Application->doRunCommand(Object(Webman\Console\Commands\StartCommand), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 phar:///www/wwwroot/tgbot/97bot/vendor/symfony/console/Application.php(171): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#19 phar:///www/wwwroot/tgbot/97bot/webman(45): Symfony\Component\Console\Application->run()
#20 /www/wwwroot/tgbot/97bot(5): require('phar:///www/www...')
#21 {main} [] []
