<?php /*a:2:{s:43:"/www/wwwroot/tgbot/bot/web/jizhang/set.html";i:1724126677;s:43:"/www/wwwroot/tgbot/bot/web/jizhang/btn.html";i:1724125172;}*/ ?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>plugin</title>
  <!--系统CSS 你可以把css放到：/bot/res/xxx/目录下然后在下引用 引用只需要：/xxx/你的.css -->
  <link rel="stylesheet" href="/assets/css/diy.css">
  <link rel="stylesheet" href="/assets/css/list2.css">
  <link rel="stylesheet" href="/assets/font/iconfont.css">
  <!--第三方css-->
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css"> 
  <style type="text/css"> 
    [v-cloak] { 
      display: none; 
    }  
    .my-autocomplete {
      li {
        line-height: normal;
        padding: 7px;
    
        .name {
          text-overflow: ellipsis;
          overflow: hidden;
        }
        .addr {
          font-size: 12px;
          color: #b4b4b4;
        }
    
        .highlighted .addr {
          color: #ddd;
        }
      }
    } 
  </style>  
</head>
<body>
<div id="app" v-cloak>
<div class="loca"> 
<div class="ele-body">
<el-row :gutter="10" class="row-bg">
<!--********以上代码不懂不要随意修改,下面开始写element vue代码*******-->


    <el-card class="box-card" shadow="never">
            <template slot="header">
              <i class="el-icon-s-tools hui" ></i>
              <span class="left2 qianlan"> 记账设置</span>
              <el-button style="float: right; padding: 3px 0" type="text"></el-button>
            </template>
        <el-form ref="formRef" :rules="setRules"  :model="setForm" label-width="128px"   label-suffix="：" style="padding:10px 0">     
            
             

        <el-col :lg="12" :xs="24"> 
            
                <el-form-item label="设置机器人" size="small" prop="bot">
                  <el-select v-model="setForm.bot" allow-create filterable placeholder="请选择机器人" @change="remoteSeleok" style="width:180px">
                    <el-option v-for="item in botlist" :key="item" :label="item" :value="item">
                    </el-option>
                  </el-select>     &nbsp;<el-button type="danger" size="mini" v-if="setForm.bot != '全局设置'" @click="deleted">删除设置</el-button>
                </el-form-item> 
                
                  
                
                
                <el-form-item   size="small" >
                  <template slot="label">
                    进群提示
                    <el-tooltip content="当机器人进群时提示记账功能消息">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                 
                    <el-radio v-model="setForm.json['进群提示']" :label="0">默认(记账说明图)</el-radio> 
                    <el-radio v-model="setForm.json['进群提示']" :label="1">自定义文字</el-radio>
                    <el-radio v-model="setForm.json['进群提示']" :label="2">不提示</el-radio>
                  
                </el-form-item>   
                
                <el-form-item   size="small" v-if="setForm.json['进群提示'] == 1">
                  <template slot="label">
                    自定义提示
                    <el-tooltip content="机器人加入群时自动发送的文字内容,支持：电报html语法 (a u i b code标签)">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="进群时提示的文字内容" v-model="setForm.json['进群提示内容']"> </el-input> 
              </el-form-item>
                  
                
                 
                
                
              <el-form-item label="账单风格" size="small">
                <el-radio v-model="setForm.json['账单风格']" :label="0">风格1(带外框)</el-radio>
                <el-radio v-model="setForm.json['账单风格']" :label="1">风格2(纯文字)</el-radio>
              </el-form-item>
                
              
              <el-form-item   size="small" >
                  <template slot="label">
                    账单广告
                    <el-tooltip content="账单下方的文字广告 ，支持：电报html语法 (a u i b code标签)">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="3" placeholder="广告文字内容" v-model="setForm.value"> </el-input> 
              </el-form-item>
              
              
              
              <el-form-item   size="small" >
                  <template slot="label">
                    开始记账
                    <el-tooltip content="输入开始记账时提示的文字讯息,支持：电报html语法 (a u i b code标签)">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="18" placeholder="开始记账时提示的文字" v-model="setForm.json['开始记账说明']"> </el-input> 
              </el-form-item>
              
              
              <el-form-item>
                  <template slot="label">
                      <span class="lan shuiying">账单按钮</span>
                  </template>
               <div class='local'>
    <el-row v-for="(item, index) in anniu" :key="index" class="row-bg" :gutter="2">
      <el-col v-for="(items, indexs) in item" :key="indexs" :span="item.length==1?18:item.length==2?9:6" class="sort-handle  ">
        <el-tag style="border-color:rgb(0 0 0 / 12%);border-radius: 4px;text-align: center;width: 100%;" closable
          @close="delbtn(items.id,index,indexs,items.comId)" @click.native="editbtn(index,indexs,items)" :type="items.text == '新按钮'?'info':''"
          class="custom-tag shoushi">
          {{items.text}}
        </el-tag>

      </el-col>
      <el-col :span="6">
        <el-button plain icon="el-icon-plus" @click="addbtn(index)" v-if="item.length <4" size="mini">新增</el-button>
      </el-col>
    </el-row>
    <el-row class="row-bg ">
      <el-button plain icon="el-icon-circle-plus-outline" @click="newHang()" size="mini">新增一行按钮</el-button>
      <el-button type="danger" plain icon="el-icon-delete" @click="delHang()" size="mini">删除最后一行按钮</el-button>
    </el-row>

    <el-dialog :visible.sync="editShow" :close-on-click-modal="true" title="编辑按钮" width="420px"   class="CSSmodal" :append-to-body="true" center>
      <el-form ref="editBtnForm" :model="editBtnForm" label-width="100px" :rules="ruled">
        <el-row :gutter="15">

          <el-col :sm="24">
            <el-form-item prop="text">
              <template slot="label">
                按钮名称
              </template>
              <el-input placeholder="按钮名称" v-model="editBtnForm.text" clearable maxlength="24" show-word-limit />
            </el-form-item>
          </el-col>

          <el-col :sm="24">
            <el-form-item prop="type">
              <template slot="label">
                按钮类型
              </template>
              <el-select clearable v-model="editBtnForm.type" placeholder="请选择按钮类型" @change="btnsele">
                <el-option label="打开网址" value="url" ></el-option> 
                <el-option label="按钮事件" value="callback_data"></el-option> 
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :sm="24" v-if="editBtnForm.type == 'url'">
            <el-form-item prop="url">
              <template slot="label">
                url地址
                <el-tooltip content="比如：https://t.me/phpTRON  可以打开网址,联系指定电报,打开群链接,打开频道链接等一切URL,">
                  <template slot="content">
                    可以打开网址,联系指定电报,进入群组,进入频道链接等<br><br>
                    比如打开电报群：<span class="shenlan   url shoushi">https://t.me/phpTRON</span><br><br>
                    比如联系作者TG：<span class="shenlan   url shoushi">https://t.me/gd801</span><br><br>
                    比如打开网址：<span class="shenlan   url shoushi">https://www.97bot.com</span><br>

                  </template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-input placeholder="请输入打开地址" v-model="editBtnForm.url" clearable ></el-input>
            </el-form-item>
          </el-col>
          <el-col :sm="24" v-if="editBtnForm.type == 'callback_data'">
            <el-form-item prop="callback_data">
              <template slot="label">
                按钮数据
                <el-tooltip>
                  <template slot="content">
                    自己输入事件则需配合：api_callback文件夹内的按钮事件case使用（ <a href="https://www.97bot.com/jiaocheng/107.html" target="_blank" rel="noopener noreferrer" class="lan">使用教程</a> ）
                  </template>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-autocomplete popper-class="my-autocomplete" placeholder="api_callback按钮事件" v-model="editBtnForm.callback_data" :fetch-suggestions="querySearch" clearable >
                <template slot-scope="{ item }">
                <div class="name">{{ item.value }}</div>
                <span class="addr">{{ item.address }}</span>
              </template>
              </el-autocomplete>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div slot="footer">
        <el-button @click="editShow = false">取消</el-button>
        <el-button type="primary" @click="editbtnOK()">确认修改</el-button>
      </div>
    </el-dialog>

  </div>
  
  
               
               </el-form-item>
               

               
              
              
              
              
              <div class="juzhong">
                <!--<span class="qianlan ">昵称或用户名存在对应 <span class="jiacu hong ">关键词</span> 则需管理员手动同意入群</span>-->
              </div>
              
              
               <el-form-item>
                  <!--<el-button  @click="load()">刷新</el-button> -->
                <el-button type="primary" @click="update()">保存设置</el-button>  
              </el-form-item>
              
               

             

           
        </el-col> 
        
        
        
        
        <el-col :lg="12" :xs="24"> 
        
                <el-form-item   size="small" >
                  <template slot="label">
                    键盘按钮说明
                    <el-tooltip content="当私聊机器人或群内点击：🧮记账功能 时提示的文字">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                 
                    <el-radio v-model="setForm.json['btntips']" :label="0">默认(记账说明图)</el-radio> 
                    <el-radio v-model="setForm.json['btntips']" :label="1">自定义文字</el-radio> 
                    <el-radio v-model="setForm.json['btntips']" :label="2">2样都要</el-radio> 
                  
                </el-form-item>  
                
                
                <el-form-item   size="small" v-if="setForm.json['btntips'] > 0">
                  <template slot="label">
                    提示文字
                    <el-tooltip content="点击键盘按钮：🧮记账功能 时提示的文字">
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <el-input type="textarea" :rows="10" placeholder="点击键盘按钮：🧮记账功能 时提示的文字" v-model="setForm.json['btntext']"> </el-input> 
              </el-form-item>
              
        
                <!--<el-form-item   size="small" prop="json.riqie">-->
                <!--  <template slot="label">-->
                <!--    禁止日切-->
                <!--    <el-tooltip content="勾选后不进行日切,也就是每日账单统计不清空">-->
                <!--      <i class="el-icon-question"></i>-->
                <!--    </el-tooltip>-->
                     
                <!--  </template>-->
                <!--  <el-checkbox v-model="setForm.json['riqie']" :true-label="1" :false-label="0">禁止日切</el-checkbox>-->
                 
                <!--</el-form-item>  -->
                
                
        
                <!--<el-form-item   size="small"  >-->
                <!--  <template slot="label">-->
                <!--    下次日切-->
                <!--    <el-tooltip content="下一次日切日期：年月日 (系统自动无需修改)">-->
                <!--      <i class="el-icon-question"></i>-->
                <!--    </el-tooltip>-->
                     
                <!--  </template>-->
                <!--  <el-input  type="text" style="width: 180px;"   v-model="setForm.json['date']"    ></el-input> <span class="qianlan cu size12">* 系统自动 如非必要勿改</span>-->
                <!--</el-form-item>  -->
        
        
                <!--<el-form-item   size="small" prop="json.qie">-->
                <!--  <template slot="label">-->
                <!--    日切时间-->
                <!--    <el-tooltip content="默认0000(代表每天00:00分)可在群内使用命令：设置日切0300">-->
                <!--      <i class="el-icon-question"></i>-->
                <!--    </el-tooltip>-->
                     
                <!--  </template>-->
                <!--  <el-input  type="text" style="width: 180px;" placeholder="0000"  v-model="setForm.json['qie']"  maxlength="4"  ></el-input>-->
                <!--</el-form-item>  -->
        
        </el-col> 
        
        
        
        </el-form> 
 </el-card>
 
         
    
    
    
        
<!--*****************************代码结束*************************-->
</el-row>
</div>
</div>
</div>
 
<!--系统js 你可以把js放到：/bot/res/xxx/目录下然后在下引用 引用时只需要：/xxx/你的.js-->
<script src="/assets/js/vue.js"></script>  
<script src="/assets/js/axios.js"></script> 
<script src="/assets/js/clipboard.js"></script>  
 

<!--第三方js 如果你要加载其它js请填写到下面-->
<script src="https://unpkg.com/element-ui/lib/index.js"></script>  
 

<!--VUE js-->
<!--VUE js-->
<script> 
var table = 'jizhang';

new Vue({
  el: '#app',
  //页面参数定义
  data() {
    return { 
        loading: true,  //加载状态 
        setForm:{
            bot:"全局设置",
            json:{
                anniu:[],  
                }
            }, 
        botlist:[],
        
        
      //btn a  
      editShow: false,
      editBtnForm: {},
      anniu: [],
      eeedit: { index: 0, indexs: 0 },
      setRules:{
          "json.qie": [
              { required: true, message: '必须输入', trigger: 'blur' },
              { min: 4, max: 4, message: '4位数长度错误', trigger: 'blur' },
              { pattern: /^[0-9]+$/, message: '请输入4位数字', trigger: 'blur' }
            ],
      },
      ruled: {
        text: [
          { required: true, message: '按钮名称不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择按钮类型', trigger: 'blur' }
        ],
        url: [
          { required: true, message: '输入点击按钮打开的地址', trigger: 'blur' }
        ],
        callback_data: [
          { required: true, message: '输入点击按钮的回调消息', trigger: 'blur' }
        ],
      }
      //btn b
        
        
    }
  },
  //数据监听
  watch: {
      
  },
  
  
  
  //初始化1·渲染前
  created() {
      this.load() 
  },
  
  
  
  //初始化2·渲染后
  mounted() { 
      this.loading = true;
        axios.get("/app/appStore/bot/index ",{}).then((res) => {
            this.loading = false;
            this.botlist = res.data.data
            this.botlist.unshift("全局设置")
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        }); 
      
     
  }, 
  
  
  
  //按钮点击事件方法
  methods: { 
    copy(obj,text){
        const clipboard = new ClipboardJS(obj, {
        text: function (trigger) {
                return text;
            }
        });
        clipboard.on('success', e => { 
            clipboard.destroy()
            this.$message.success('复制成功');
            return ;
        });
        clipboard.on('error', e => {
            clipboard.destroy()
            this.$message.error('复制失败'); 
            return ;
        });
    },   
    //以上按钮事件必备
    load(){ 
        axios.get("/web/"+table+"/setget",{params:{bot:this.setForm.bot}}).then((res) => { 
            if(!res.data.data){
                this.$message.error("该机器人没有单独设定,默认使用全局设置");
                return false
            }
            
            
            if (res.data.data.bot == 0 || res.data.data.bot == '0') {
                res.data.data.bot = "全局设置"  
            }
             
            
            
            this.setForm = res.data.data;
            
            this.anniu = this.setForm.json.anniu;
            
            
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });    
    },
    update(){  
        this.$refs['formRef'].validate((valid) => {
            if (valid) {
                this.setForm.json.anniu = this.anniu 
                
                axios.post("/web/"+table+"/update",{type:"set",data:this.setForm}).then((res) => { 
                    
                    this.$message.success(res.data.msg);
                    
                }).catch((e) => {
                    this.loading = false;
                    this.$message.error(e.message);
                });  
            }
        return false;  
        })
    },
    deleted(){
        axios.post("/web/"+table+"/del",{type:"del",data:this.setForm}).then((res) => {  
            if(res.data.code != 1){
                this.$message.error(res.data.msg);
                return false;  
            }       
            this.$message.success(res.data.msg);
            this.setForm.bot = "全局设置"
            this.load() 
            
        }).catch((e) => {
            this.loading = false;
            this.$message.error(e.message);
        });  
    },
    remoteSeleok(){
       this.load() 
    },
    
    
    //按钮事件开始
    btnsele(val) {
      if (val == "url") {
        delete this.editBtnForm.callback_data
      } else if (val == "callback_data") {
        delete this.editBtnForm.url
      } 

    },
    querySearch(queryString, cb) {
       var  results = [
           { "value": "下载账单", "address": "管理员下载本群今日账单" },
           { "value": "入款详情", "address": "查看今日每个用户入款详情" }, 
           ];
       cb(results); 
    },

    addbtn(index) {
      this.anniu[index].push({ text: '新按钮', url: "https://www.97bot.com" });
    },
    editbtn(index, indexs, items) {
      this.editBtnForm = { ...items }
      this.editShow = true
      //记录按钮偏移  后续修改
      this.eeedit.index = index
      this.eeedit.indexs = indexs

    },
    delbtn(id, index, indexs, comId) {
      this.anniu[index].splice(indexs, 1);//删除指定obj 内 第x个元素   
    },
    newHang() {
      this.anniu.push([])
    },
    delHang(index) {
      this.anniu.splice(this.anniu.length - 1, 1);
    },
    editbtnOK() {
      this.$refs['editBtnForm'].validate((valid, obj) => {
        if (valid) {
          this.anniu[this.eeedit.index][this.eeedit.indexs] = this.editBtnForm
          this.editShow = false
        }
      })

    },
    //按钮事件结束
    
     
  }, 
  
  
  
  //离开页面前事件
  beforeDestroy () {
  },
})

</script>
</body> 
</html>