-- 表结构：tb_moneylog
CREATE TABLE `tb_moneylog` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) NOT NULL COMMENT '1充值 2提现 3转账 4消费',
  `bot` varchar(32) NOT NULL,
  `zt` int(1) NOT NULL,
  `uid` int(10) NOT NULL,
  `tgid` bigint(12) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(16) NOT NULL,
  `coin` varchar(8) NOT NULL,
  `lmoney` decimal(12,2) NOT NULL,
  `money` decimal(12,2) NOT NULL,
  `nmoney` decimal(12,2) NOT NULL,
  `value` varchar(88) NOT NULL,
  `addr1` varchar(34) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `addr2` varchar(34) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `zt` (`zt`),
  KEY `tgid` (`tgid`),
  <PERSON>EY `update_time` (`update_time`),
  KEY `create_time` (`create_time`),
  KEY `bot` (`bot`),
  KEY `type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
