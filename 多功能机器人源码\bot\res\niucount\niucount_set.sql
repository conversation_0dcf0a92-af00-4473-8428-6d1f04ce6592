-- 表结构：tb_niucount_set
CREATE TABLE `tb_niucount_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(888) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
-- 表数据：tb_niucount_set
INSERT INTO `tb_niucount_set` VALUES ('1','0','','1','-1001804699164\n-4189254136\n-4102278685\n-4186420237\n-4170238703','{\"qunlist\": [123456, 456789]}','0','1705484311');
