<?php
namespace bot\api_callback;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
 
class tgvips  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["callId"]       =   点击唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $message["text"]         =   消息文字内容
     * $message["time"]         =   消息到达-服务器时间戳
     * $message["msgTime"]      =   消息发布时间戳  电报官方时间
     * $message["editTime"]     =   消息最后编辑时间戳 0未编辑过
     * 
     * $message["btnData"]      =   消息按钮对应 消息事件
     * 
     * $message["gamaId"]       =   游戏标识ID   
     * $message["gameName"]     =   游戏唯一标识 游戏才有
     
     * $message["photo"]        =   点击按钮时消息内容中-如果有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   点击按钮时消息内容中-有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   点击按钮时消息内容中-有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   点击按钮时消息内容中-有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $ret返回参数：
     * alert (最高优先级 - 下面参数无效)
     * sendText(发送文字讯息) editText(编辑消息内容)  必须2选1
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(键盘按钮格式和anniu一样) + jianpanText(文本消息) 或 jianpanPhoto(发送照片) （第2优先级 - 下面参数无效）
     * huifu=1(强制用户回复) huifuTips(回复提示文字)  
     * next (不强制回复模式·用户下一条文本消息将作为本次交互所需要的回复内容)
     * delMessage=1 代表点击按钮后删除原消息 back=0 代表编辑按钮时禁止增加返回按钮 nodel=1代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){ 
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        #------------------------以上代码默认请勿修改 level 视情况调整改动--------------------- 
        
        if($type = pipei($message['btnData'],"^开通电报会员\|([0-9]+)$")){
            $tgvip = new model\tgvips_set;
            $vipset = $tgvip->getJson($message['bot']['API_BOT']);
            $ret['editText'] = trans('开通时长', [], 'tgvips')."：<b>".$type[1].trans('个月', [], 'tgvips')."</b>";
            $ret['editText'] .= "\n".trans('订单金额', [], 'tgvips')."：<b>{$vipset['json']["{$type[1]}个月"]} USDT</b>";
            $ret["anniu"] = [
                        [
                             
                            [
                                "text" => "🎁".trans('给自己开通', [], 'tgvips')."",
                                "callback_data" => "开通电报会员|{$type[1]}|1"
                            ],
                            [
                                "text" => "📤".trans('赠送给他人', [], 'tgvips')."",
                                "callback_data" => "开通电报会员|{$type[1]}|2"
                            ],
                        ], 
                    ]; 
            return $ret;        
        }
        
        if($type = pipei($message['btnData'],"^开通电报会员\|([0-9]+)\|([0-9])$")){  
            $tgvip = new model\tgvips_set;
            $vipset = $tgvip->getJson($message['bot']['API_BOT']); 
            $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();
            if($user['usdt'] < $vipset['json']["{$type[1]}个月"]){
                $ret['editText'] = trans('开通时长', [], 'tgvips')."：<b>{$type[1]}".trans('个月', [], 'tgvips')."</b>\n".trans('订单金额', [], 'tgvips')."：<b>{$vipset['json']["{$type[1]}个月"]} USDT</b> \n\n".trans('余额不足请充值', [], 'tgvips');
                $ret['anniu'] = [
                        [ 
                            [
                                "text" => "📥 立即充值USDT",
                                "callback_data" => "充值余额USDT"
                            ]
                        ],
                    ]; 
                return $ret;
            }
            if($type[2] == 1){ 
                if($message['formUser'] == '未设置用户名'){
                    $ret['alert'] = trans('未设置用户名无法开通', [], 'tgvips');
                    return $ret;
                }
                $tgvip = new model\tgvips_set;
                $vipset = $tgvip->getJson($message['bot']['API_BOT']); 
                $touser = $message['formUser']; 
                $numt = $type[1];  
                $httpVip = httpVip($vipset['json']['cookie']);
                $response = $httpVip->post("/api?hash={$vipset['json']['hash']}", [
                        'form_params' => [
                            'query' => $touser,
                            "months"=> $numt,
                            "method"=> "searchPremiumGiftRecipient"
                            ]
                    ]); 
                $json = json_decode($response->getBody()->getContents(), true);
                if(empty($json['found']['recipient'])){
                    $ret['alert'] = "rec数据获取失败,请重试";
                    return $ret; 
                }
                
              
                $tgvip_user = model\tgvips_user::where("rec",$json['found']['recipient'])->find();
                if(empty($tgvip_user)){
                   $sql['user'] =  $touser;
                   $sql['name'] =  $json['found']['name'];
                   $sql['rec'] =  $json['found']['recipient'];
                   $tgvip_user = model\tgvips_user::create($sql);
                }else{ 
                   $tgvip_user['user'] =  $touser;
                   $tgvip_user['name'] =  $json['found']['name']; 
                   $tgvip_user->save();
                }   
              
                
                $ret['editText'] = "<code>".trans('你正在开通电报会员', [], 'tgvips')."</code>\n\n".trans('开通时长', [], 'tgvips')."：<b>".$type[1].trans('个月', [], 'tgvips')."</b>";
                $ret['editText'] .= "\n".trans('订单金额', [], 'tgvips')."：<b>{$vipset['json']["{$type[1]}个月"]} USDT</b>";
                $ret['editText'] .= "\n\n<b>【".trans('给自己开通', [], 'tgvips')."】</b>";
                $ret['editText'] .= "\n\n".trans('用户名', [], 'tgvips')."：@{$message["formUser"]}";
                $ret['editText'] .= "\n".trans('昵称名', [], 'tgvips')."：<b>{$message["formName"]}</b>";
                $ret["anniu"] = [
                        [
                             
                            [
                                "text" => "👌 ".trans('确认支付', [], 'tgvips'),
                                "callback_data" => "确认开通电报会员|{$message["formUser"]}|{$type[1]}"
                            ]
                        ]
                ]; 
                
            }else{ 
                if(empty($vipset['json']['给他人开通'])){
                    $ret['alert'] = trans('暂时不允许给他人开通会员', [], 'tgvips');
                    return $ret;
                } 
                $ret['editText'] = "<code>".trans('你正在赠送电报会员', [], 'tgvips')."</code>\n\n".trans('开通时长', [], 'tgvips')."：<b>".$type[1].trans('个月', [], 'tgvips')."</b>";
                $ret['editText'] .= "\n".trans('订单金额', [], 'tgvips')."：<b>{$vipset['json']["{$type[1]}个月"]} USDT</b>";
                $ret['editText'] .= "\n\n".trans('输入它的电报用户名', [], 'tgvips');
                $ret['next'] = '给他人开通tgvip会员';
                $ret['back'] = 0;
                
                
            }
            
 
            return $ret;        
        }
        
        
        
        
        if($type = pipei($message['btnData'],"^确认开通电报会员\|([\w]+)\|([0-9]+)$")){  
            $tgvip = new model\tgvips_set;
            $vipset = $tgvip->getJson($message['bot']['API_BOT']);
             
            
             
            $user_info = model\tgvips_user::where("user",$type[1])->find();  
            if(empty($user_info['rec'])){
                $ret['delMessage'] =1;
                $ret['alert'] = "rec数据异常请重新下单";
                return $ret;
            }
            
            
            $hash = $vipset['json']['hash'];
            $cookie= $vipset['json']['cookie'];
            $numt = $type[2];
            $recipient = $user_info['rec'];
            if(empty($hash) || $hash == '39a4705879d0df9457'){
                $ret['alert'] = "错误\n请联系管理员登录后台正确配置开会员cookie";
                return $ret;
            } 
            #构建sql
            $sql['bot'] = $message['bot']['API_BOT'];
            $sql['from'] = $message['formUser'];
            $sql['to'] = $user_info['user'];
            $sql['day'] = $numt;
            $sql['usdt'] = $vipset['json']["{$numt}个月"];
            
            $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();
            if($user['usdt'] < $sql['usdt']){
                $ret['editText'] = "<b>【开通失败】</b>\n\n你的账户余额不足:{$sql['usdt']} USDT";
                $ret['back'] = 0;//删除返回按钮
                $ret['anniu'] = [
                        [ 
                            [
                                "text" => "📥 立即充值USDT",
                                "callback_data" => "充值余额USDT"
                            ]
                        ],
                    ]; 
                $sql['payzt']=2;
                $sql['msg'] = "该用户账户余额不足";
                $tgvip_list = model\tgvips_list::create($sql);    
                return $ret;  
            }
            
            
            $httpVip = httpVip($vipset['json']['cookie']);
            
            
            #创建订单 
            $response = $httpVip->post("/api?hash={$vipset['json']['hash']}", [
                    'form_params' => [
                        'recipient' => $recipient,
                        "months"=> $numt,
                        "method"=> "initGiftPremiumRequest"
                        ]
                ]); 
            $json = json_decode($response->getBody()->getContents(), true);  
            if(empty($json['req_id'])){
                $sql['payzt']=2;
                $ret['alert'] = "创建订单失败,登录后台更新cookie";
                $sql['msg'] = "创建order订单失败";
                $tgvip_list = model\tgvips_list::create($sql);
                return $ret;    
            }
            $req_id = $json['req_id'];  
            
            
            
            // #确认订单
            // $response = $httpVip->post("/api?hash={$vipset['json']['hash']}", [
            //         'form_params' => [
            //             'id' => $req_id,
            //             "show_sender"=> 1,
            //             "method"=> "getGiftPremiumLink"
            //             ]
            //     ]); 
            // $json = json_decode($response->getBody()->getContents(), true); 
            
            // dump($json);
            
            
            
            #解码支付数据 
            $client2 = new Guzz_Client(['timeout' => 6,'connect_timeout' => 5,'verify' => false,'http_errors' => false]);   
            $json = json_decode($client2->request('GET', "https://fragment.com/tonkeeper/rawRequest?id={$req_id}")->getBody()->getContents(),true);   
            //https://app.tonkeeper.com/v1/txrequest-url/fragment.com/tonkeeper/rawRequest?id=AsLR7v3xn_9ClOeKhSq_Yai9&qr=1
            if(empty($json['body']['params']['messages'])){
                $sql['payzt']=2;
                $ret['alert'] = "解码order数据失败,请联系管理员";
                $sql['msg'] = "解码order数据失败";
                $tgvip_list = model\tgvips_list::create($sql);
                return $ret;  
            }  
             
            
            $money =  $json['body']['params']['messages'][0]['amount']; //最终支付金额(精度9) 也就是 amount * 1000000000
            $base32 = base64_decode(substr($json['body']['params']['messages'][0]['payload'],24)); //不是完整正确的解码    
            $cleanString = preg_replace('/[\x00-\x1F\x7F]/', '', $base32); 
            $base32 = explode("Ref#",$cleanString);  
            
            if(empty($base32[1]) || strlen($base32[1]) < 8 ){  
                $sql['payzt']=2;
                $ret['alert'] = "解码order数据不存在或为空,请联系管理员";
                $sql['msg'] = "解码order数据不存在";
                $tgvip_list = model\tgvips_list::create($sql);
                return $ret;  
            }
             
            
            $base32 = "T{$base32[0]}\n\nRef#".$base32[1];#最终(支付网关)订单数据 需要传递给golang 支付网关
            
             
            $sql['json']['req_id'] = $req_id;
            $sql['json']['amout'] = $money;
            $sql['json']['code'] = $base32;
            $sql['json']['payload'] = $json['body']['params']['messages'][0]['payload'];
            $sql['json']['expires'] = $json['body']['expires_sec'];
            
            $app['amout'] = "{$sql['json']['amout']}";//正式支付时  
            
            #$app['amout'] = "0.00021";//测试交易时
            $app['address'] = $json['body']['params']['messages'][0]['address']; //"UQBAjaOyi2wGWlk-EDkSabqqnF-MrrwMadnwqrurKpkla4QB" //官方开会员地址
            $app['text'] = $base32;
            $pay = $GLOBALS['Gate']->call("Gate.Tonpay",json_encode($app));  
            if(empty($pay['code'])){
                $sql['payzt']=2;
                $ret['back'] = 0; //删除返回按钮
                $sql['msg'] = $pay['message'] ?? "支付失败原因未知";
                $sql['json']['payinfo'] = $pay;
                $tgvip_list = model\tgvips_list::create($sql);
                $ret['editText'] = $message['text']."\n\n".trans('开通结果', [], 'tgvips')."\n<b>".trans('失败原因', [], 'tgvips')."：</b>{$sql['msg']},".trans('联系管理员反馈', [], 'tgvips');
                return $ret;  
            }else{
               
                Db::name("bot_delurl")->insert(["time"=>$message['time']+15,"url"=>"https://fragment.com/tonkeeper/rawResponse?id={$req_id}&qr=1"]); 
                Db::name("bot_delurl")->insert(["time"=>$message['time']+60,"url"=>"https://fragment.com/tonkeeper/rawResponse?id={$req_id}&qr=1"]);  
                
                $user['usdt'] = $user['usdt']-$sql['usdt'];
                $user->save();
                $sql['payzt']=1;
                $sql['msg']="支付成功";
                $sql['json']['payinfo'] = $pay;
                $tgvip_list = model\tgvips_list::create($sql);
                $ret['back'] = 0; //删除返回按钮
                $ret['nodel'] = 1;//禁止删除 
                $ret['editText'] = $message['text']."\n\n".trans('开通结果', [], 'tgvips')."\n".trans('交易凭证', [], 'tgvips')."：<code>{$pay['hex']}</code>\n".trans('开通成功', [], 'tgvips');
                
                    //---------------------------
                    $buylog['type']=4; //1充值 2提现 3转账 4消费 5收款 6红包
                    $buylog['bot']=$message['bot']['API_BOT'];
                    $buylog['zt']=1;
                    $buylog['uid']=$user['id'];
                    $buylog['user']=$user['user'];
                    $buylog['tgid']=$user['tgid'];
                    $buylog['name']=$user['name'];  
                    $buylog['coin']="USDT";
                    $buylog['lmoney']=$user['usdt'];
                    $buylog['money']= -$sql['usdt']; 
                    $buylog['nmoney']=$buylog['lmoney']-$sql['usdt'];
                    $buylog['value']="<span class='molv'>开通电报会员{$numt}个月：</span>@{$sql['to']}";
                    $buylog['create_time']=$message["msgTime"];
                    $buylog['id']=Db::name("moneylog")->insertGetId($buylog);
                    
                return $ret; 
            } 
             
              
            return $ret;        
        }
 
 
        return $ret;
    }
}
