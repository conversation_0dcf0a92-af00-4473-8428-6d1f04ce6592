<?php
namespace bot\api_callback;

use app\model;                          #模型  
use bot\mdb;                          #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 
 
class usdtjt  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["callId"]       =   点击唯一ID
     * $message['msgId']        =   消息ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *   
     * $message["time"]         =   消息到达-服务器时间戳
     * $message["msgTime"]      =   消息发布时间戳  电报官方时间
     * $message["editTime"]     =   消息最后编辑时间戳 0未编辑过
     * 
     * $message["btnData"]      =   消息按钮对应 消息事件
     * 
     * $message["gamaId"]       =   游戏标识ID   
     * $message["gameName"]     =   游戏唯一标识 游戏才有
     * 
     *  ■■ $ret ■■ 返回参数说明：
     *  alert (最高优先级 - 下面参数无效) back=0 代表编辑按钮时禁止增加返回按钮
     *  delMessage=1 代表点击按钮后删除原消息
     *  jianpan(键盘按钮) + jianpanText(文本消息) 或 jianpanPhoto(发送照片) （第2优先级 - 下面参数无效）
     *  sendText(发送文本消息),sendPhoto(发送照片),sendVideo(发送视频)  ||  editText(编辑消息),editPhoto(编辑照片(文本消息时无效)) ||  anniu(消息按钮)
     * 
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){ 
        $ret['key']=pathinfo(basename(__FILE__), PATHINFO_FILENAME); 
        $ret['level']=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message['bot']['appStore'][$ret['key']])){
            return $ret;
        }
        #-----------------以上核心代码勿动 level 视情况调整改动--------------------------
        
        switch ($message['btnData']) { 
            default:  
                break;
                
            case '设置监听通知地址POST':
                if($message['chatType'] != "private"){
                    $ret['alert'] = "该功能仅支持私聊机器人使用";
                    return $ret;  
                    break;
                }
                $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                
                $__text = "<pre><code class='language-通知参数说明'>type = in收入 out支出\ncoin = 币种(trx,usdt)\nblock = 区块编号\nhash = 哈希订单(如有必要可进行反查)\noutaddress = 出款钱包地址\ninaddress = 收款钱包地址\nmoney = 交易数量(原始金额 精度6)\namount = 交易数量(人性化金额)\ntime = 交易时间戳(10位)</code></pre>";
                
                if(empty($user['json']['url'])){
                   $ret['editText'] = "<blockquote>开发者对接用·不懂的无视</blockquote>\n{$__text}\n\n<b>你正在设定钱包监听通知地址</b>\n当你监听的地址收到USDT TRX 时会向你设定的地址发送POST请求通知\n\n<b>请输入你接收通知的URL地址：</b>"; 
                }else{
                   $ret['editText'] = "<blockquote>开发者对接用·不懂的无视</blockquote>\n{$__text}\n\n<blockquote>你当前的POST通知地址：\n<u>{$user['json']['url']}</u></blockquote>\n当你监听的地址收到USDT TRX 时会向你设定的地址发送POST请求通知\n\n<b>请输入新的URL通知地址：</b>";  
                } 
                $ret['next'] = "设置监听通知地址POST"; 
                $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "❌ 取消操作",
                                            "callback_data" => "取消操作"
                                        ]
                                    ],
                                ];
                return $ret;  
                break;
                
            case '新增监听地址':  
                $ret['editText'] = "<b>请选择监听通知类型</b>\n\n【<b>电报通知</>】\n地址发生交易时机器人消息通知\n\n【<b>POST通知</b>】\n地址发生交易时POST异步通知";
                $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "🔔机器人消息通知",
                                            "callback_data" => "新增TRC20监听地址"
                                        ]
                                         
                                    ],
                                    [
                                    [
                                            "text" => "🌐POST请求通知 ",
                                            "callback_data" => "新增TRC20监听地址POST"
                                        ]
                                        ]
                                ];
                return $ret;  
                break;
                
            case '新增TRC20监听地址':    
                if($message["chatType"] == "supergroup" ){
                    $group = model\bot_group::where("qunid",$message['chatId'])->find();
                    if(empty($group['admin'])){
                        $ret['alert'] = "获取群管理员列表失败,请把机器人踢出群重新拉入";
                        return $ret;
                    }
                    
                    if(empty($group['admin'][$message["formId"]])){ 
                        $ret['alert'] = "只有管理员才可以为群添加监听地址";
                        return $ret;
                    } 
                }
                $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                if(empty($usdtjt_set)){
                   $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                }
                if(empty($usdtjt_set['json']['免费监听地址数量'])){
                    $ret['alert'] = "请管理员在后台填写地址监听配置";
                    return $ret;
                }
                $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                $jtnum = $usdtjt_set['json']['免费监听地址数量'];
                if($userdz){  
                    $jtnum  =  $usdtjt_set['json']['免费监听地址数量'] + $userdz['num'];
                }
                $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",1)->where("tgid",$message['chatId'])->select();//取得用户的所有监听地址
                if(count($address) >= $jtnum){
                    $ret['editText'] = "免费监听地址数量达到上限：{$jtnum} 个\n\n<b>[你可以选择付费监听更多地址]</b>\n\n<b>{$usdtjt_set['json']['单个地址收费']} TRX</b> 永久增加1个监听地址\n\n<b>{$usdtjt_set['json']['无限地址收费']} USDT</b> 永久开通无限个监听地址";
                    $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "付费 开通1个地址",
                                            "callback_data" => "购买增加1个地址监听"
                                        ]
                                         
                                    ],
                                    [
                                        [
                                            "text" => "付费 开通无限个地址",
                                            "callback_data" => "开通无限个地址监听"
                                        ]
                                         
                                    ]
                                ]; 
                    return $ret;   
                }
                 
                $ret['editText'] =  "\n\n<b>发生交易时机器人会发消息通知你\n\n请输入监听的钱包地址：</b>";  
                $ret['next'] = "新增TRC20监听地址"; 
                return $ret;  
                break; 
                
                
            case '新增TRC20监听地址POST':    
                if($message["chatType"] != "private" ){
                    $ret['alert'] = "POST通知模式仅支持私聊机器人使用";
                    return $ret; 
                    break;
                }
                
                $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                if(empty($user['json']['url'])){
                    $ret['alert'] = "请先设置POST通知地址";
                    return $ret; 
                    break;
                }
                
                // if($message["chatType"] == "supergroup" ){
                //     $group = model\bot_group::where("qunid",$message['chatId'])->find();
                //     if(empty($group['admin'])){
                //         $ret['alert'] = "获取群管理员列表失败,请把机器人踢出群重新拉入";
                //         return $ret;
                //     }
                    
                //     if(empty($group['admin'][$message["formId"]])){ 
                //         $ret['alert'] = "只有管理员才可以为群添加监听地址";
                //         return $ret;
                //     } 
                // } 
                
                
                $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                if(empty($usdtjt_set)){
                   $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                }
                if(empty($usdtjt_set['json']['免费监听地址数量'])){
                    $ret['alert'] = "请管理员在后台填写地址监听配置";
                    return $ret;
                }
                $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                $jtnum = $usdtjt_set['json']['免费监听地址数量'];
                if($userdz){  
                    $jtnum  =  $usdtjt_set['json']['免费监听地址数量'] + $userdz['num'];
                }
                $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",2)->where("tgid",$message['chatId'])->select();//取得用户的所有监听地址
                if(count($address) >= $jtnum){
                    $ret['editText'] = "免费监听地址数量达到上限：{$jtnum} 个\n\n<b>[你可以选择付费监听更多地址]</b>\n\n<b>{$usdtjt_set['json']['单个地址收费']} TRX</b> 永久增加1个监听地址\n\n<b>{$usdtjt_set['json']['无限地址收费']} USDT</b> 永久开通无限个监听地址";
                    $ret['anniu'] = [
                                    [
                                        [
                                            "text" => "付费 开通1个地址",
                                            "callback_data" => "购买增加1个地址监听"
                                        ]
                                         
                                    ],
                                    [
                                        [
                                            "text" => "付费 开通无限个地址",
                                            "callback_data" => "开通无限个地址监听"
                                        ]
                                         
                                    ]
                                ]; 
                    return $ret;   
                }
                 
                $ret['editText'] =  "\n\n<blockquote>当前通知地址：{$user['json']['url']}</blockquote>\n\n<b>新增POST监听地址,请输入钱包地址：</b>";  
                $ret['next'] = "新增TRC20监听地址POST"; 
                return $ret;  
                break; 
                
                
            case '购买增加1个地址监听':
                $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                if(empty($usdtjt_set)){
                   $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                }
                if(empty($user['trx']) || $user['trx'] < $usdtjt_set['json']['单个地址收费']){
                    $ret['editText'] = "你的账户余额不足：{$usdtjt_set['json']['单个地址收费']} TRX";
                    return $ret; 
                }
                 
                $sk['type']=4; 
                $sk['bot']=$message['bot']['API_BOT'];
                $sk['zt']=1;
                $sk['uid']=$user['id'];
                $sk['user']=$user['user'];
                $sk['tgid']=$user['tgid'];
                $sk['name']=$user['name'];  
                $sk['coin']="TRX";
                $sk['lmoney']=$user['trx'];
                $sk['money']= -$usdtjt_set['json']['单个地址收费']; 
                $sk['nmoney']=$user['trx']-$usdtjt_set['json']['单个地址收费'];
                $sk['value']="<span class='hong'>购买增加1个地址监听";
                $sk['create_time']=$message["msgTime"];
                Db::name("moneylog")->insert($sk); 
                
                $user['trx'] -= $usdtjt_set['json']['单个地址收费'];
                $user->save();
                
                $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                if(empty($userdz)){
                    $userdz['bot'] = $message['bot']['API_BOT'];
                    $userdz['tgid'] = $message['chatId'];
                    $userdz['user'] = $message['formUser'];
                    $userdz['name'] = $message['formName'];
                    $userdz['num'] = 1;
                    Db::name("usdtjt_user_set")->insert($userdz); 
                }else{
                    Db::name("usdtjt_user_set")->where("id",$userdz['id'])->inc("num",1)->update(['user'=>$message['formUser'],"name"=>$message['formName']]); 
                }
                $ret['sendText'] =  "恭喜您，花费：<b>{$usdtjt_set['json']['单个地址收费']}</b>\n成功购买增加1个监听地址数量✅";
                return $ret; 
                break; 
                
                
            
            case '开通无限个地址监听':
                                $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find();
                $usdtjt_set = model\usdtjt::where("bot",$message['bot']['API_BOT'])->cache("usdtjt_{$message['bot']['API_BOT']}")->find();
                if(empty($usdtjt_set)){
                   $usdtjt_set = model\usdtjt::where("bot",0)->cache("usdtjt_0")->find();
                }
                if(empty($user['usdt']) || $user['usdt'] < $usdtjt_set['json']['无限地址收费']){
                    $ret['editText'] = "你的账户余额不足：{$usdtjt_set['json']['无限地址收费']} USDT";
                    return $ret; 
                }
                 
                $sk['type']=4; 
                $sk['bot']=$message['bot']['API_BOT'];
                $sk['zt']=1;
                $sk['uid']=$user['id'];
                $sk['user']=$user['user'];
                $sk['tgid']=$user['tgid'];
                $sk['name']=$user['name'];  
                $sk['coin']="USDT";
                $sk['lmoney']=$user['usdt'];
                $sk['money']= -$usdtjt_set['json']['无限地址收费']; 
                $sk['nmoney']=$user['usdt']-$usdtjt_set['json']['无限地址收费'];
                $sk['value']="<span class='hong'>购买无限数量地址监听";
                $sk['create_time']=$message["msgTime"];
                Db::name("moneylog")->insert($sk);  
                $user['usdt'] -= $usdtjt_set['json']['无限地址收费'];
                $user->save();
                
                $userdz = Db::name("usdtjt_user_set")->where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->find();
                if(empty($userdz)){
                    $userdz['bot'] = $message['bot']['API_BOT'];
                    $userdz['tgid'] = $message['chatId'];
                    $userdz['user'] = $message['formUser'];
                    $userdz['name'] = $message['formName'];
                    $userdz['num'] = 999;
                    Db::name("usdtjt_user_set")->insert($userdz); 
                }else{
                    Db::name("usdtjt_user_set")->where("id",$userdz['id'])->inc("num",999)->update(['user'=>$message['formUser'],"name"=>$message['formName']]); 
                }
                $ret['delMessage'] = 1;
                $ret['sendText'] =  "恭喜您，花费：<b>{$usdtjt_set['json']['无限地址收费']} USDT</b>\n成功开通无限制地址数量监听功能✅";
                return $ret; 
                break; 
                
                
                
            case '我的TRC20监听地址': 
                $ret["editText"] = "📮 <b>[USDT监控]</b>  <b>[TRX监控]</b>  <b>[多签监控]</b>\n";
                $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("tgid",$message['chatId'])->select();
                if($address->isEmpty()){
                    $ret['editText'] .= "尚未添加任何监听地址";
                }else{
                    $ret['editText'] .= "\n🧾 <b>监听地址列表：</b>\n"; 
                    foreach ($address as $value) { 
                        if($value['type'] == 1){
                            $ret["editText"] .= "\n🔔 "."<code>{$value['address']}</code>";  
                        }else if($value['type'] == 2){
                            $ret["editText"] .= "\n🌐 "."<code>{$value['address']}</code>";
                        }  
                    }
                }
                 
                $ret['anniu'] = [
                                    [
                                        [
                                            "text" =>"➕" .trans('新增地址', [], 'usdtjt'),
                                            "callback_data" => "新增TRC20监听地址"
                                        ], 
                                        [
                                            "text" =>"⛔️". trans('删除地址', [], 'usdtjt'),
                                            "callback_data" => "删除TRC20监听地址"
                                        ]
                                    ]
                                ]; 
                return $ret; 
                break; 
                
            case '删除TRC20监听地址':
                $ret['next'] = '删除TRC20监听地址'; 
                $ret['editText'] = $message['text']. "\n\n<b>请输入要删除监听的钱包地址:</b>";  
                return $ret;  
                break; 
                
     
   
        }
        
        
        if($ss = pipei($message['btnData'],"^监听钱包地址\|(.+)")){ 
            $user = model\bot_account::where('tgid',$message['chatId'])->where("bot",$message['bot']['API_BOT'])->find(); 
            if($message["chatType"] != "private"){ 
                if(empty($user)){ 
                    $ret["editText"] = "机器人：@{$message['bot']['API_BOT']} \n\n<b>监听失败,你未启用或已停用本机器人</b>"; 
                    return $ret;
                } 
            }
            $hexaddress = $GLOBALS['Gate']->call("Gate.TronDecode",$ss[1]);//地址转换成hex 
            $address = model\bot_address_jt::where("bot",$message['bot']['API_BOT'])->where("type",1)->where("tgid",$message['chatId'])->select();//取得用户的所有监听地址 
            $address = array_column($address->toArray(), null, 'address');  
            if(isset($address[$ss[1]])){ 
                $ret['editText'] = "<pre><code class='language-监听目标地址'>{$ss[1]}</code></pre>\n".trans('你已监听该地址,无需重复添加', [], 'usdtjt');
                return $ret; 
            } 
            
            $sql['bot']=$message['bot']['API_BOT'];
            $sql['type']=1;
            $sql['coin']="TRC";
            $sql['username']=$message['formUser'];
            $sql['tgid']=$message['chatId'];
            $sql['address']=$ss[1];
            $sql['addressHex']=$hexaddress;  
            $insert = model\bot_address_jt::create($sql);
            if($insert){
                Redis::HSETNX("address",$hexaddress,1);
            } 
            $ret['editText'] = "<pre><code class='language-监听目标地址'>{$ss[1]}</code></pre>\n<b>监听地址成功</b>✅\n\n当该地址发生权限变更和收支交易时你会收到消息通知！";
            return $ret;
            
            
        }
 
        
 
        
          
        return $ret;   
    }
       
    
    
    
 
}