<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class money  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容
     * 
     * $ret支持回调参数：sendText(发送文字讯息) sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件) huifu=1(强制用户回复) huifuTips(回复提示文字)   anniu(消息按钮)   [ jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)] back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     * @param $id
     * @return array
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
        
        #下面开始写代码
        
        //你定义了启动按钮：🌐账户余额 请在下面写消息触发事件
        if(pipei($message["text"],"我的余额$")){
            $user = Db::name("bot_account")->where("del",0)->where("bot",$message['bot']['API_BOT'])->where("tgid",$message["formId"])->find();
            if(empty($user)){
               $ret["sendText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
               $ret["anniu"] = [ 
                        [
                            [
                                "text" => "🔴 点击启用机器人",
                                "url" => "https://t.me/{$message['bot']['API_BOT']}"
                            ]
                        ],
                    ];
               return $ret;  
            }
            $ret["sendText"] = "👤 用  户  名：@{$message["formUser"]}\n";
            $ret["sendText"] .= "💠 电报ＩＤ：<code>{$message["formId"]}</code>\n";
            $ret["sendText"] .= "🗓 注册日期：<code>".date("Y-m-d H:i:s",$user['create_time'])."</code>\n\n";
            $ret["sendText"] .= "T R X 余额：<b>{$user['trx']}</b>\n";
            $ret["sendText"] .= "USDT余额：<b>{$user['usdt']}</b>\n";
            $ret["anniu"] = [
                        [
                            [
                                "text" => "💰余额充值",
                                "callback_data" => "余额充值"
                            ],
                            [
                                "text" => "📊流水记录",
                                "callback_data" => "流水记录|1"
                            ]
                        ],
                        [
                            [
                                "text" => "🧧发送红包",
                                "callback_data" => "发送红包"
                            ]
                        ],
                        [
                            [
                                "text" => "♻️余额转账",
                                "callback_data" => "余额转账"
                            ],
                            [
                                "text" => "🧳余额提现",
                                "callback_data" => "余额提现"
                            ]
                        ],
                        
                    ]; 
            if(!is_mokuai("hongbao")){
                unset($ret["anniu"][1]); 
            }
            if($message['formId'] == $message['bot']['Admin']){
                $aa[] = ["text" => "⚙️ 设置充值地址","callback_data" => "设置充值地址"]; 
                $ret['anniu'][] =$aa;
            }
            return $ret;  
        }
       
        
        if(pipei($message["text"],"充值余额$")){
            $set = model\money_set::where('bot',$message['bot']['API_BOT'])->find();   
            if(empty($set)){
                $set = model\money_set::where('bot',0)->find();
            }
            if(empty($set['json']['充值地址'])){
                $ret['sendText'] = "充值功能未开启\n管理员没有设置收款地址！";
                return $ret;
            } 
            
            $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->where("del",0)->find(); 
            if(empty($user)){  
               $ret["sendText"] = "<b>你未启用或已停用本机器人</b>\n请私信本机器人以恢复使用";
               $ret["anniu"] = [ 
                    [
                        [
                            "text" => "🔴 点击启用机器人",
                            "url" => "https://t.me/{$message['bot']['API_BOT']}"
                        ]
                    ],
                ];
               return $ret;
            }
            $ret['sendText'] = "<code>💰你正在进行充值...</code>\n\nT R X 余额：<b>{$user['trx']}</b>\nUSDT余额：<b>{$user['usdt']}</b>\n\n<b>请选择充值余额类型：</b>";
            $ret['anniu'] = [
                        [
                            [
                                "text" => "充值TRX",
                                "callback_data" => "充值余额TRX"
                            ],
                            [
                                "text" => "充值USDT",
                                "callback_data" => "充值余额USDT"
                            ]
                        ],
                    ]; 
            
            return $ret;
            
        }
        
        #充值TRX
        if(!empty($message['action'])){
            if($message['action'] == "充值余额TRX"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<b>充值余额TRX...</b>\n你输入的充值数量：<b><u>{$message["actionText"]}</u></b> 错误,请重新输入："; 
                    return $ret;  
                }
                $coin = "TRX";
                Cache::tag("{$message['formId']}")->clear();
                $Lock = Cache::get("{$coin}_{$message["actionText"]}"); 
                if($Lock){
                    $ret['delMessage'] = 1; 
                    $ret['sendText'] = "<b>非常抱歉,该金额有用户正在充值...</b>\n\n<code>请您尝试充值其它金额支持2位小数</code>\n请重新输入要充值的USDT数量："; 
                    return $ret;
                } 
                Cache::tag($message['formId'])->set("{$coin}_{$message["actionText"]}",$message['formId'],300); 
                $base = new Base ();
                
                //300秒内该用户 存在其它订单消息干掉
                $lishi = Db::name("pay_list")->where("tgid",$message['formId'])->where("zt",0)->where("create_time",">",time()-300)->find();
                if($lishi){
                    $base->sendBotUrl($lishi['bot'],"/deleteMessage?chat_id={$lishi['tgid']}&message_id={$lishi['msgid']}",0,1);//因为可能不是同一个机器人创建的订单，因此需要根据机器人用户名发送api url  删除充值订单
                    Db::name("pay_list")->where("id",$lishi['id'])->update(["zt"=>-1]);
                } 
                
                $set = model\money_set::where('bot',$message['bot']['API_BOT'])->find();   
                if(empty($set)){
                    $set = model\money_set::where('bot',0)->find();
                }  
                if($message["actionText"] < $set['json']["最低充值TRX"] ){
                    $ret['delMessage'] = 1; 
                    $ret['sendText'] = "最低充值TRX数量：<b><u>{$set['json']["最低充值TRX"]}</u></b>\n请重新输入要充值的TRX数量："; 
                    return $ret; 
                } 
                
                 $ret['nextdel'] = 1; //删除动作
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                //------------------------
                $pay['bot']=$message['bot']['API_BOT'];
                $pay['tgid']=$user['tgid'];
                $pay['user']=$user['user'];
                $pay['name']=$user['name'];
                $pay['coin']="TRX";
                $pay['value']=$message["actionText"]; 
                $pay['create_time']=$message["tgTime"];
                $pay['id'] = Db::name("pay_list")->insertGetId($pay); 
                
                $anniu['inline_keyboard'] = [
                            [
                                [
                                    "text" => "🔄查看状态",
                                    "callback_data" => "充值订单查询|{$pay['id']}"
                                ],
                                [
                                    "text" => "📊流水记录",
                                    "callback_data" => "流水记录"
                                ]
                            ],
                        ]; 
                $anniu = json_encode($anniu);
                if(is_file("/97bot/{$set['value']}.png")){
                   $addresspng =  "/97bot/{$set['value']}.png";
                } 
                
                if(!empty($addresspng) && is_file($addresspng)){
                    $res = $base->get("/sendPhoto?chat_id={$message['chatId']}&photo=file://{$addresspng}&caption=<code>🔂你正在进行充值...</code>\n\n订单编号：<b>{$pay['id']}</b>\n充值用户：@{$user['user']}\n充值货币：<b>TRX</b>\n收款地址：\n<code class='language-请给以下地址转账'>{$set['json']['充值地址']}</code>\n转账数量：<code>{$message["actionText"]}</code> <b>TRX</b>\n\n<code>请一定要按照以上金额进行转账充值,否则无法自动到账！</code>[<b>有效期：5分钟</b>]&reply_markup={$anniu}&reply_to_message_id={$message['msgId']}",1);  
                }else{
                    $res = $base->get("/sendMessage?chat_id={$message['chatId']}&text=<code>🔂你正在进行充值...</code>\n\n订单编号：<b>{$pay['id']}</b>\n充值用户：@{$user['user']}\n充值货币：<b>TRX</b>\n收款地址：\n<code class='language-请给以下地址转账'>{$set['json']['充值地址']}</code>\n转账数量：<code>{$message["actionText"]}</code> <b>TRX</b>\n\n<code>请一定要按照以上金额进行转账充值,否则无法自动到账！</code>[<b>有效期：5分钟</b>]&reply_markup={$anniu}&reply_to_message_id={$message['msgId']}",1);  
                 }
                 
                $base->task(time() + 288 , "/deleteMessage?chat_id={$message['chatId']}&message_id={$res['message_id']}");//大约260秒后删除消息

           
                Db::name("pay_list")->where("id",$pay['id'])->update(["msgid"=>$res['message_id']]); 
                
                return $ret;
                
            #充值USDT    
            }else if($message['action'] == "充值余额USDT"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<b>充值余额USDT...</b>\n你输入的充值数量：<b><u>{$message["actionText"]}</u></b> 错误,请重新输入："; 
                    return $ret;  
                }
                Cache::tag("{$message['formId']}")->clear();
                $Lock = Cache::get("{$message['action']}_{$message["actionText"]}"); 
                if($Lock){
                    $ret['delMessage'] = 1; 
                    $ret['sendText'] = "<b>非常抱歉,该金额有用户正在充值...</b>\n\n<code>请您尝试充值其它金额支持2位小数</code>\n请重新输入要充值的USDT数量："; 
                    return $ret;
                } 
                Cache::tag($message['formId'])->set("{$message['action']}_{$message["actionText"]}",$message['formId'],300); 
                $base = new Base ();
                
                //300秒内该用户 存在其它订单消息干掉
                $lishi = Db::name("pay_list")->where("tgid",$message['formId'])->where("zt",0)->where("create_time",">",time()-300)->find();
                if($lishi){
                    $base->sendBotUrl($lishi['bot'],"/deleteMessage?chat_id={$lishi['tgid']}&message_id={$lishi['msgid']}",0,1);//因为可能不是同一个机器人创建的订单，因此需要根据机器人用户名发送api url  删除充值订单
                    Db::name("pay_list")->where("id",$lishi['id'])->update(["zt"=>-1]);
                } 
                
                $set = model\money_set::where('bot',$message['bot']['API_BOT'])->find();   
                if(empty($set)){
                    $set = model\money_set::where('bot',0)->find();
                }  
                if($message["actionText"] < $set['json']["最低充值USDT"] ){
                    $ret['delMessage'] = 1; 
                    $ret['sendText'] = "最低充值USDT数量：<b><u>{$set['json']["最低充值USDT"]}</u></b>\n请重新输入要充值的USDT数量："; 
                    return $ret; 
                } 
                
                 $ret['nextdel'] = 1; //删除动作
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                //------------------------
                $pay['bot']=$message['bot']['API_BOT'];
                $pay['tgid']=$user['tgid'];
                $pay['user']=$user['user'];
                $pay['name']=$user['name'];
                $pay['coin']="USDT";
                $pay['value']=$message["actionText"]; 
                $pay['create_time']=$message["tgTime"];
                $pay['id'] = Db::name("pay_list")->insertGetId($pay); 
                
                $anniu['inline_keyboard'] = [
                            [
                                [
                                    "text" => "🔄查看状态",
                                    "callback_data" => "充值订单查询|{$pay['id']}"
                                ],
                                [
                                    "text" => "📊流水记录",
                                    "callback_data" => "流水记录"
                                ]
                            ],
                        ]; 
                $anniu = json_encode($anniu);
                if(is_file("/97bot/{$set['value']}.png")){
                   $addresspng =  "/97bot/{$set['value']}.png";
                }else{
                   $addresspng =  "/97bot/address.png"; 
                }
                
                if(is_file($addresspng)){
                    $res = $base->get("/sendPhoto?chat_id={$message['chatId']}&photo=file://{$addresspng}&caption=<code>🔂你正在进行充值...</code>\n\n订单编号：<b>{$pay['id']}</b>\n充值用户：@{$user['user']}\n充值货币：<b>USDT</b>\n收款地址：\n<code class='language-请给以下地址转账'>{$set['json']['充值地址']}</code>\n转账数量：<code>{$message["actionText"]}</code> <b>USDT</b>\n\n<code>请一定要按照以上金额进行转账充值,否则无法自动到账！</code>[<b>有效期：5分钟</b>]&reply_markup={$anniu}&reply_to_message_id={$message['msgId']}",1);  
                }else{
                    $res = $base->get("/sendMessage?chat_id={$message['chatId']}&text=<code>🔂你正在进行充值...</code>\n\n订单编号：<b>{$pay['id']}</b>\n充值用户：@{$user['user']}\n充值货币：<b>USDT</b>\n收款地址：\n<code class='language-请给以下地址转账'>{$set['json']['充值地址']}</code>\n转账数量：<code>{$message["actionText"]}</code> <b>USDT</b>\n\n<code>请一定要按照以上金额进行转账充值,否则无法自动到账！</code>[<b>有效期：5分钟</b>]&reply_markup={$anniu}&reply_to_message_id={$message['msgId']}",1);  
                 }
                 
                $base->task(time() + 288 , "/deleteMessage?chat_id={$message['chatId']}&message_id={$res['message_id']}");//大约260秒后删除消息

           
                Db::name("pay_list")->where("id",$pay['id'])->update(["msgid"=>$res['message_id']]); 
                
                return $ret;
             
            #==============提现===================    
            }else if($message['action'] == "余额提现TRX"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<b>TRX余额提现...</b>\n你输入的提现金额：<b><u>{$message["actionText"]}</u></b> 错误,请重新输入："; 
                    return $ret;  
                }
                $coin = "TRX";
                $coin2 = "trx";
                $set = model\money_set::where('bot',0)->find();
                if($message["actionText"] < $set['json']['最低提现'.$coin]){ 
                    $ret['sendText'] = "<b>最低{$coin}提现数量：{$set['json']["最低提现{$coin}"]}</b>\n请重新输入要提现的{$coin}数量：";
                    return $ret;
                }
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
                if($user[$coin2] < $message["actionText"]){ 
                    $ret['sendText'] = "<b>余额不足：{$message["actionText"]}</b>\n\n<code>可用{$coin}余额：{$user[$coin2]}</code>\n\n请重新输入要提现的{$coin}数量：";
                    return $ret;
                }
                 
                $ret['sendText'] = "你正在进行{$coin}提现\n\n提现金额：<b>{$message["actionText"]}</b>\n\n<b>请输入收款地址：</b>";
                $ret['next'] = "请输入收款钱包地址";//二次动作
                return $ret;  
                
            }else if($message['action'] == "余额提现USDT"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<b>USDT余额提现...</b>\n你输入的提现金额：<b><u>{$message["actionText"]}</u></b> 错误,请重新输入："; 
                    return $ret;  
                }
                $coin = "USDT";
                $coin2 = "usdt";
                $set = model\money_set::where('bot',0)->find();
                if($message["actionText"] < $set['json']['最低提现'.$coin]){ 
                    $ret['sendText'] = "<b>最低{$coin}提现数量：{$set['json']["最低提现{$coin}"]}</b>\n请重新输入要提现的{$coin}数量：";
                    return $ret;
                }
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
                if($user[$coin2] < $message["actionText"]){ 
                    $ret['sendText'] = "<b>余额不足：{$message["actionText"]}</b>\n\n<code>可用{$coin}余额：{$user[$coin2]}</code>\n\n请重新输入要提现的{$coin}数量：";
                    return $ret;
                }
                 
                $ret['sendText'] = "你正在进行{$coin}提现\n\n提现金额：<b>{$message["actionText"]}</b>\n\n<b>请输入收款地址：</b>";
                $ret['next'] = "请输入收款钱包地址";//二次动作
                return $ret;  
                
            }else if($message['action'] == "请输入收款钱包地址"){
                if(!pipei($message['actionText'],"^T\w{33}$")){
                    $ret["sendText"] = "⚠️<b>提现失败,收款钱包地址错误,请重新输入：</b>"; 
                    return $ret;  
                } 
                $ss = pipei($message['oldText'],"动作消息缓存：余额提现([A-Z]+)([0-9.]+)");
                if(count($ss) != 3){
                    $ret["sendText"] = "⚠️<b>提现失败，请联系管理员，原因是：读取动作记录消息失败了../b>"; 
                    $ret['nextdel']=1;
                    return $ret;  
                }
                $ret['nextdel']=1;//删除动作
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();  
                $coin2 = $ss[1];
                $coin = strtolower($ss[1]);//转小写
                $num = $ss[2];
                
                $buylog['type']=2; //1充值 2提现 3转账 4消费
                $buylog['bot']=$message['bot']['API_BOT'];
                $buylog['zt']=0;
                $buylog['uid']=$user['id'];
                $buylog['user']=$user['user'];
                $buylog['tgid']=$user['tgid'];
                $buylog['name']=$user['name'];  
                $buylog['coin']=$coin2;
                $buylog['lmoney']=$user[$coin];
                $buylog['money']= -$num;
                $buylog['addr2']= $message["actionText"];
                $buylog['nmoney']=$user[$coin]-$num;
                $buylog['value']="<span class='hong'>{$coin2}提现至：</span>{$message["actionText"]}";
                $buylog['create_time']=$message["tgTime"];
                $buylog['id']=Db::name("moneylog")->insertGetId($buylog);
                $total = new model\total;
                $total->inc("tixian",$num);
                Db::name("bot_account")->where("bot",$buylog['bot'])->where("tgid",$buylog['tgid'])->dec($coin,$num)->update(); 
                $ret['delMessage'] = 1;
                $ret['sendText'] = "订单编号：<b>{$buylog['id']}</b>\n提现类型：<b>{$coin2}</b>\n提现数量：<b>{$num}</b>\n收款地址：<code>{$buylog['addr2']}</code>\n订单时间：".date("Y-m-d H:i:s",$buylog['create_time'])."\n<b>提现订单已提交成功</b>";
                return $ret;    
            }else if($message['action'] == "转出USDT"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<code>🔂你正在进行USDT转账...</code>\n你输入的转账金额：<b><u>{$message['actionText']}</u></b> 错误,<b>请重新输入：</b>"; 
                    return $ret;  
                }
                $set = model\money_set::where('bot',0)->find();
                if($message['actionText'] < $set['json']["最低转账USDT"]){ 
                    $ret['sendText'] = "<b>创建转账订单失败</b>\n最低转账USDT数量：".$set['json']["最低转账USDT"]."\n\n<b>请重新输入转账数量：</b>";
                    return $ret;
                }
                
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();
                if($user['usdt']< $message["actionText"]   ){
                   $ret['sendText'] = "<code>🔂你正在进行USDT转账...</code>\n\n⚠️很抱歉你的余额不足：{$message["actionText"]}\n\n你的可用USDT余额：<b>{$user['usdt']}</b>\n\n<b>请重新输入转账USDT金额：</b>";
                   return $ret;  
                }
                
                $ret['sendText'] = "<code>🔂你正在进行USDT转账...</code>\n\n转账USDT数量：<b><u>{$message['actionText']}</u></b>\n\n<b>请输入收款人电报用户名：</b>";  
                $ret['next'] = "请输入收款人用户名";//二次动作
                return $ret;  
                
            }else if($message['action'] == "转出TRX"){
                if(!is_numeric($message["actionText"]) || $message["actionText"] < 0.01){
                    $ret['sendText'] = "<code>🔂你正在进行TRX转账...</code>\n你输入的转账金额：<b><u>{$message['actionText']}</u></b> 错误,<b>请重新输入：</b>"; 
                    return $ret;  
                }
                $set = model\money_set::where('bot',0)->find();
                if($message['actionText'] < $set['json']["最低转账TRX"]){ 
                    $ret['sendText'] = "<b>创建转账订单失败</b>\n最低转账TRX数量：".$set['json']["最低转账TRX"]."\n\n<b>请重新输入转账数量：</b>";
                    return $ret;
                }
                
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find();
                if($user['trx']< $message["actionText"]   ){
                   $ret['sendText'] = "<code>🔂你正在进行TRX转账...</code>\n\n⚠️很抱歉你的余额不足：{$message["actionText"]}\n\n你的可用TRX余额：<b>{$user['usdt']}</b>\n\n<b>请重新输入转账TRX数量：</b>";
                   return $ret;  
                }
                
                $ret['sendText'] = "<code>🔂你正在进行TRX转账...</code>\n\n转账TRX数量：<b><u>{$message['actionText']}</u></b>\n\n<b>请输入收款人电报用户名：</b>";  
                $ret['next'] = "请输入收款人用户名";//二次动作
                return $ret;  
            }else if($message['action'] == "请输入收款人用户名"){
                $u = str_replace("@", "", $message['actionText']); 
                $u = str_replace("https://t.me/", "", $u);
                if(!pipei($u,"^\w{4,}$")){
                    $ret["sendText"] = "⚠️<b>转账失败,收款人电报用户名错误,请重新输入：</b>"; 
                    return $ret;  
                } 
                
                $ss = pipei($message['oldText'],"动作消息缓存：转出([A-Z]+)([0-9.]+)");
                if(count($ss) != 3){
                    $ret["sendText"] = "⚠️<b>转账失败，请联系管理员，原因是：读取动作记录消息失败了../b>"; 
                    $ret['nextdel']=1;
                    return $ret;  
                }
                $touser = model\bot_account::where('user',$u)->where("bot",$message['bot']['API_BOT'])->find(); 
                if(empty($touser)){ 
                    $ret['sendText'] = "转账类型：<b>{$ss[1]}</b>\n转账金额：<b>{$ss[2]}</b>\n收款用户：@{$u}\n\n⚠️转账失败了,收款用户没有关注本机器人或已停止使用！\n\n<b>请重新输入收款人电报用户名：</b>";
                    return $ret; 
                }
                if($message['formUser'] == $u  || $message['formId'] == $touser['tgid'] || $message['formUser'] == '未设置用户名'){
                   $ret["sendText"] = "⚠️<b>接收目标错误不能为自己,请重新输入：</b>"; 
                   return $ret;  
                }
                
                $user = model\bot_account::where('tgid',$message['formId'])->where("bot",$message['bot']['API_BOT'])->find(); 
                
                $ret['nextdel']=1;
                
                $coin[1] = $ss[1];
                $cin = strtolower($coin[1]);
                $message['text'] = $ss[2];
                $skuser[1] = $u;
                
                $buylog['type']=3; //1充值 2提现 3转账 4消费 5收款 6红包
                $buylog['bot']=$message['bot']['API_BOT'];
                $buylog['zt']=1;
                $buylog['uid']=$user['id'];
                $buylog['user']=$user['user'];
                $buylog['tgid']=$user['tgid'];
                $buylog['name']=$user['name'];  
                $buylog['coin']=$coin[1];
                $buylog['lmoney']=$user[$cin];
                $buylog['money']= -$message['text']; 
                $buylog['nmoney']=$user[$cin]-$message['text'];
                $buylog['value']="<span class='hong'>转账给：</span>@{$skuser[1]}";
                $buylog['create_time']=$message["tgTime"];
                $buylog['id']=Db::name("moneylog")->insertGetId($buylog);
                $total = new model\total;
                $total->inc("zhuan",$message['text']);
                
                $user[$cin] = $user[$cin] - $message['text'];
                $user->save();
                
                
                $sk['lmoney']=$touser[$cin];
                $touser[$cin] = $touser[$cin] + $message['text'];
                $touser->save();  
                $sk['type']=5; //1充值 2提现 3转账 4消费 5收款 6红包
                $sk['bot']=$message['bot']['API_BOT'];
                $sk['zt']=1;
                $sk['uid']=$touser['id'];
                $sk['user']=$touser['user'];
                $sk['tgid']=$touser['tgid'];
                $sk['name']=$touser['name'];  
                $sk['coin']=$coin[1];
                 
                $sk['money']= $message['text']; 
                $sk['nmoney']=$sk['lmoney']+$message['text'];
                $sk['value']="<span class='hong'>收到来自：</span>@{$user['user']} 的转账";
                $sk['create_time']=$message["tgTime"];
                Db::name("moneylog")->insert($sk); 
                
                
                
                $ret['delMessage'] = 1; 
                $ret['sendText'] = "<code>转账结果通知...</code>\n";
                $ret['sendText'] .= "\n订单编号：<b>{$buylog['id']}</b>";
                $ret['sendText'] .= "\n转账类型：<b>{$coin[1]}</b>";
                $ret['sendText'] .= "\n转账数量：<b>{$message['text']}</b>";
                $ret['sendText'] .= "\n收款用户：@{$skuser[1]}";
                $ret['sendText'] .= "\n转账时间：<code>".date("m-d H:i:s",$buylog['create_time'])."</code>";
                $ret['sendText'] .= "\n\n<b>恭喜您,转账成功!</b>✅";
                $ret["anniu"] = [
                        [
                             
                            [
                                "text" => "🔂继续转账",
                                "callback_data" => "余额转账"
                            ],
                            [
                                "text" => "📊转账记录",
                                "callback_data" => "流水记录"
                            ]
                        ]
                        
                    ]; 
                
                
                $shouqian = "<b>恭喜您,收到转账!</b>✅";
                $shouqian .= "\n\n订单编号：<b>{$buylog['id']}</b>";
                $shouqian .= "\n收款类型：<b>{$coin[1]}</b>";
                $shouqian .= "\n收款数量：%2B<b>{$message['text']}</b>";
                $shouqian .= "\n付款用户：@{$user['user']}";
                $shouqian .= "\n订单日期：<code>".date("m-d H:i:s",$buylog['create_time'])."</code>";
                
                $shouqian .= "\n\n【<b>新的余额</b>】";
                $shouqian .= "\nT R X 余额：<b>{$touser['trx']}</b>";
                $shouqian .= "\nUSDT余额：<b>{$touser['usdt']}</b>";
                
                
                $anniu["inline_keyboard"] = [
                        [
                             
                            [
                                "text" => "📊流水记录",
                                "callback_data" => "流水记录|1"
                            ],
                            [
                                "text" => "🧳余额提现",
                                "callback_data" => "余额提现"
                            ]
                        ]
                        
                    ]; 
                $anniu = json_encode($anniu);     
                $base = new Base ();
                $base->sendUrl("/sendMessage?chat_id={$touser['tgid']}&text={$shouqian}&reply_markup={$anniu}");
                
            }else if($message['action'] == "设置充值地址"){
                if(!pipei($message['actionText'],"^T\w{33}$")){
                    $ret["sendText"] = "⚠️<b>充值收款钱包地址错误,请重新输入：</b>";  
                    return $ret;  
                }
                
                $addressSel = Db::name("bot_address_jt")->where("address",$message['actionText'])->select(); 
                foreach ($addressSel as $addr) {
                    if($addr['type'] > 3){
                        $ret["sendText"] = "⚠️<b>该钱包地址似乎被用于其它模块的业务了,请重新输入：</b>";  
                        return $ret; 
                    }   
                }
                
                
                $czset = model\money_set::where('bot',$message['bot']['API_BOT'])->find();
                if(empty($czset)){
                    $sql['bot'] = $message['bot']['API_BOT'];
                    $sql['value'] = $message['bot']['API_BOT'];
                    $sql['json']['允许提现'] = 1;
                    $sql['json']['允许转账'] = 1;
                    $sql['json']['充值地址'] = $message['actionText'];
                    $sql['json']['最低充值TRX'] = 1;
                    $sql['json']['最低提现TRX'] = 3;
                    $sql['json']['最低转账TRX'] = 2;
                    $sql['json']['最低充值USDT'] = 1;
                    $sql['json']['最低提现USDT'] = 5;
                    $sql['json']['最低转账USDT'] = 1; 
                    model\money_set::create($sql);
                }else{
                    $aa = $czset['json']; 
                    $aa['充值地址'] = $message['actionText'];
                    model\money_set::where("id",$czset['id'])->update(['json'=>$aa]);
                }
                
                #更新给监听
                $pays = Db::name("bot_address_jt")->where("type",3)->where("bot",$message['bot']['API_BOT'])->find(); 
                if(empty($pays)){
                    $pays['bot'] = $message['bot']['API_BOT'];
                    $pays['username'] = $message['bot']['AdminName'];
                    $pays['tgid'] = $message['bot']['Admin'];
                    $pays['type'] = 3;
                    $pays['coin'] = "TRC";
                    $pays['address'] = $message['actionText'];
                    $pays['addressHex'] = $GLOBALS['Gate']->call("Gate.TronDecode",$pays['address']);
                    $pays['create_time'] = time();
                    Db::name("bot_address_jt")->insert($pays);
                    
                    $redisI = Redis::hget("address",$pays['addressHex']);
                    if(empty($redisI)){
                        Redis::hset("address",$pays['addressHex'],1);
                    } 
                }else{ 
                    $pays['username'] = $message['bot']['AdminName'];
                    $pays['tgid'] = $message['bot']['Admin'];
                    $pays['address'] = $message['actionText'];
                    $pays['addressHex'] = $GLOBALS['Gate']->call("Gate.TronDecode",$pays['address']);
                    $pays['update_time'] = time();
                    Db::name("bot_address_jt")->where("id",$pays['id'])->update($pays);
                }
                 
                
                
               $ret["sendText"] = "充值收款地址设置成功！";
               $ret['nextdel'] = 1; 
               return $ret;  
            }
            
            
        } 
        
        #代码结束
        return $ret;     
    }
} 
