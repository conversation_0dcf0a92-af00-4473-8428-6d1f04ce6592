<?php
namespace bot\_info; 

 
class acoin  {  
    public function index(){    
         
        $info['core']       = 231215;
        
        $info['lv']         = 34;
           
        $info['key']        = "acoin";
         
        $info['name']       = "1币价格查询-支持所有币";
        
        $info['text']       = "比如发送：<span class='hong cu'>1TON</span>可以查看对应币价值（<span class='molv miaobian '>支持所有币种</span>）"; 
         
        $info['version']    = "2.5";
        
        $info['new']        = "修复getjson报错问题";
        
        $info['menuId']     = [0];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["coin1.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "LT55lPyP9WoN+q/MCMqNpkY3fSlOMoUS5tS2wmsBvDdMVisdcFhS5rbQ8JrE0RgNe3sW7fLuz8aOKtwVBLisIZGoEmAHeY8cC6W4x6vM/LavoxQ0XoccHd5iaN7h7NiThLGWKWGLohkGPdBzsJG5anX+BkQRq5KCQ+3puN9Xj9Y=";
 
        $info['tables']     = [];
        
        $info['btn']       = "";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      