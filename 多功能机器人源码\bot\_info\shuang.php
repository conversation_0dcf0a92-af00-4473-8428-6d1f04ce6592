<?php
namespace bot\_info; 

 
class shuang  {  
    public function index(){    
         
        $info['core']       = 240331;
        
        $info['lv']         = 41;
           
        $info['key']        = "shuang";
         
        $info['name']       = "双向机器人(消息转发)";
        
        $info['text']       = "<span class='hong cu'>双向机器人</span> 当用户私聊机器人时会将消息发送至指定客服群(支持多个客服回复)"; 
         
        $info['version']    = "1.3";
        
        $info['new']        = "支持多机器人单独设定<br>优化错误chid问题";
        
        $info['menuId']     = [433,434];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 1;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 0;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["tu.png", "tu1.png", "tu2.png", "tu3.png", "tu4.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "aQjT5RF9VhLDGvKC7krasHs7gLmy76lHSS3jX7oJFi3zC56qR3Na48NFAr0jRCVoMQjDkTKd1kRH/TYCKFxQBtDNIn43OxLtyADg0kjC8Bvq4H9DuCIuclACzxojmN/LXy4m/SsqsGjE1PyyCz44r0f96X3bMSaIEQdwUrP1jbg=";
 
        $info['tables']     = ["shuang_set"];
        
        $info['btn']       = "";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      