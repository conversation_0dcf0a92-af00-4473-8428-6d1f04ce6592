<?php
namespace bot\_info; 

 
class usdtjt  {  
    public function index(){    
         
        $info['core']       = 240331;
        
        $info['lv']         = 12;
           
        $info['key']        = "usdtjt";
         
        $info['name']       = "TRC钱包地址监听";
        
        $info['text']       = "<span class='hong'>TRON波场</span>USDT和TRX币种监听，收入和支出毫秒级消息通知支持POST通知"; 
         
        $info['version']    = "2.6";
        
        $info['new']        = "更新支持POST异步通知，支持能量监听";
        
        $info['menuId']     = [180, 185, 259, 260];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 1;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["USDTJT11.png", "USDTJT12.png", "USDTJT13.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "OtJE3i+KA8kXxe2o/6dlIOR4O4+/wSMe3rGmB0B03gyA8nCrLBMIY9pOXV5bU87/FEbbWwjaN2TqldX/j3DB2bT2HGQK6znSkO9a3wiBkBopNzee+Q0lyl4psF+VgXCpoarJHxnBYI72BOf19dtIcAWPX199pLcc/0yuHjnbHP4=";
 
        $info['tables']     = ["usdtjt_set", "usdtjt_user_set"];
        
        $info['btn']       = "🔔地址监听";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      