-- 表结构：tb_usdtjt_set
CREATE TABLE `tb_usdtjt_set` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `bot` varchar(32) NOT NULL COMMENT '机器人0为全局',
  `u` varchar(64) NOT NULL COMMENT '中文备注',
  `type` int(2) NOT NULL DEFAULT '1' COMMENT '类',
  `value` varchar(32) NOT NULL COMMENT '文本配置',
  `json` json DEFAULT NULL COMMENT 'json配置',
  `create_time` int(10) NOT NULL COMMENT '增加时间',
  `update_time` int(10) NOT NULL COMMENT '最后修改',
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
-- 表数据：tb_usdtjt_set
INSERT INTO `tb_usdtjt_set` VALUES ('1','0','','0','','{\"style\": \"2\", \"通知按钮\": [], \"通知模板\": \"收款地址：{to}\\n付款地址：{from}\\n交易数量：{money}\\n交易哈希：{hash}\\n区块高度：{block}\\n订单时间：{time}\", \"单个地址收费\": \"2\", \"无限地址收费\": \"50\", \"免费监听地址数量\": 5}','0','1970');
