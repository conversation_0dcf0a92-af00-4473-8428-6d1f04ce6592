-- 表结构：tb_pay_list
CREATE TABLE `tb_pay_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `msgid` int(11) NOT NULL,
  `zt` int(1) NOT NULL,
  `bot` varchar(32) NOT NULL,
  `tgid` bigint(15) NOT NULL,
  `user` varchar(32) NOT NULL,
  `name` varchar(15) NOT NULL,
  `coin` varchar(6) NOT NULL,
  `value` decimal(12,2) NOT NULL,
  `hash` varchar(64) NOT NULL,
  `addr1` varchar(34) NOT NULL,
  `addr2` varchar(34) NOT NULL,
  `msg` varchar(64) NOT NULL,
  `create_time` int(10) NOT NULL,
  `update_time` int(10) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `bot` (`bot`),
  KEY `tgid` (`tgid`),
  KEY `coin` (`coin`),
  KEY `msgid` (`msgid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
