<?php
namespace bot\_info; 

 
class he444bot  {  
    public function index(){    
         
        $info['core']       = 240728;
        
        $info['lv']         = 50;
           
        $info['key']        = "he444bot";
         
        $info['name']       = "汇旺(专群)验群机器人";
        
        $info['text']       = "同款 <span class='lan cu'>@he444bot</span> 一样的专群验群机器人，群组管理处设置群编号发送即可自动回复."; 
         
        $info['version']    = "1.5";
        
        $info['new']        = "修复错误 value 问题,有问题的建议卸载重装";
        
        $info['menuId']     = [322,323,327];  
        
        $info['byuser']     = "gd801"; 
         
        $info['lang']       = 0;
         
        $info['usdt']       = 0.00; 
         
        $info['day']        = 31; 
        
        $info['autobuy']    = 1;
        
        $info['pic']        = "pic.png";
         
        $info['image']      = ["tu1.png", "tu2.png", "tu3.png"]; 
           
        $info['need']       = [];
         
        $info['ban']        = []; 

        $info['com1']       = "";
 
        $info['com2']       = "";
        
        $info['auth']       = "eEOla7t5/gaMeWDFJjGsmgj07pNXr9RTzEOd1nr2vxX+XvNyIrClVbCvi/Yq0qiKwcLGiY5uFGEdFuNAjC4wADZ88NueFNH87hjuKDAiAr3vHPpCf0L4VU6kXgFDU20EIaKRwWHB90x1K4utnBnytTgzVT2MAGWIUeWzQ8Wgc9k=";
 
        $info['tables']     = ["he444bot_set", "he444bot_list"];
        
        $info['btn']       = "";
        
        $info['btnType']   = 0;
        
        $info['btn1']     = []; 
        
        $info['btn2']     = []; 
        
        return $info;
        
    }
    
    
}    
      