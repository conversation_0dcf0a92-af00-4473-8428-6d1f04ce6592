<?php
namespace bot\api_message;

use app\model;                          #模型  
use bot\mdb;                            #模型2
use think\facade\Db;                    #数据库
use think\facade\Cache;                 #缓存
use support\Redis;                      #redis
use Webman\RedisQueue\Client as RQamsg; #异步队列
use Webman\RedisQueue\Redis as   RQmsg; #同步队列 

//对应类文件
use plugin\tgbot\app\controller\Base;
use plugin\tgbot\app\controller\Template;

use GuzzleHttp\Pool;
use GuzzleHttp\Client as Guzz_Client;
use GuzzleHttp\Psr7\Request as Guzz_Request; 
use GuzzleHttp\Promise as Guzz_Promise; 


class he444bot  {  
    /**
     * 【参数解答】
     * $message["bot"]          =   机器人配置信息
     * $message["msgId"]        =   聊天消息唯一ID
     * 
     * $message["chatType"]     =   聊天类型 群=supergroup  私聊=private
     * $message["chatId"]       =   聊天窗口ID
     * $message["chatUser"]     =   聊天窗口用户名(@xxx)
     * $message["chatName"]     =   聊天窗口标题 - 群组=群名称 ，用户=用户昵称
     * 
     * $message["formId"]       =   发消息的人ID
     * $message["formUser"]     =   发消息的人用户名
     * $message["formName"]     =   发消息的人昵称
     * $message["fromVip"]      =   发消息的人是否是电报VIP 0否 1是
     * $message["fromLang"]     =   发消息的人电报客户端语言(多语言需要)
     * 
     * $message["text"]         =   消息文本内容
     * $message["time"]         =   消息到达-服务器时间戳  
     * $message["tgTime"]       =   消息到达-电报官方时间戳 
     * 
     * $message["photo"]        =   有图片时为图片数据信息-自己打印查看     没有为0
     * $message["document"]     =   有文件时为文件数据信息-自己打印查看     没有为0
     * $message["video"]        =   有视频时为视频数据信息-自己打印查看     没有为0
     * $message["gif"]          =   有动画图片时为动画数据信息-自己打印查看 没有为0
     *
     * $message["action"]       =   上一条消息或按钮设定的(next)：动作交互  没有时为空
     * $message["actionText"]   =   用户回复的对应动作的->文本内容          没有时为空
     * 
     * $message["isHuiFu"]       =   是否属回复消息？1是 0否 ：↓ 属于回复消息时才有以下参数 
     *      $message["HuiFu"]["msgId"]  =   被回复的消息ID   
     *      $message["HuiFu"]["isBot"]  =   被回复的目标是否为机器人 1是0否
     *      $message["HuiFu"]["botUser"]=   被回复的目标是机器人时才有效,返回机器人用户名
     *      $message["HuiFu"]["toId"]   =   被回复消息的人ID
     *      $message["HuiFu"]["toUser"] =   被回复消息的人用户名
     *      $message["HuiFu"]["toVip"]  =   被回复消息的人是否是电报VIP 0否 1是
     *      $message["HuiFu"]["text"]   =   被回复消息的内容 
     *
     * $ret回调参数：
     * sendText(发送文字讯息 必须) 
     * sendPhoto(发送照片) sendVideo(发送视频)  sendFile(发送文件)  可选-3选1
     * anniu(消息按钮)
     * jianpan(回复键盘按钮数组和anniu一样格式) && jianpanText(文字消息) || jianpanPhoto(照片消息)
     * huifu=1(强制用户回复) huifuTips(回复提示文字)       
     * next (不强制回复·但下一条文本消息将作为本次交互所需要的回复内容)
     * back=0 代表禁止自动增加消息下方的删除按钮和多语言按钮 delMessage=1 代表删除被回复消息 nodel=1 代表即将发送的消息禁止被回收
     * 
     * 说明照片 视频 文件 支持填写服务器上的绝对路径比如照片：/etc/xxx.png  文件：/www/xxx/kami.zip  视频:/www/xxx/123.mp4
     *
     */
     
    
    #默认执行函数 
    public function index($message){   
        $ret["key"]=pathinfo(basename(__FILE__), PATHINFO_FILENAME);
        $ret["level"]=100; //优先级 (当存在多个模块都返回了文本消息或按钮时生效)数值大排上面 ，数值小排下面
        if(!isset($message["bot"]["appStore"][$ret["key"]])){
            return $ret;
        }
        if($message["isHuiFu"]){
            if($message["HuiFu"]["isBot"]){ 
                if($message["HuiFu"]["botUser"] != $message["bot"]["API_BOT"]){
                   return false; 
                }
            }
        }
        #------------------------以上代码默认 请勿修改 level 视情况调整改动---------------------  
        
       
        
        #下面开始写代码
        if($message['chatType'] == 'private'){
           if(pipei($message['text'],"^[a-zA-z]{1}[0-9]{4,5}$")){
               $set = new mdb\he444bot;
               $set = $set->getJson($message['bot']['API_BOT']);
               if(empty($set['json']['zhen']) || empty($set['json']['jia'])){
                   $ret['sendText'] = "<b>请先登录后台设定真 假 群回复内容</b>";
                   return $ret;  
               }
               $quninfo = mdb\he444bot_list::where("bianhao",$message['text'])->find();
                if(empty($quninfo)){
                  //假群
                  $ret['sendText'] = $set['json']['jia'];
                  return $ret; 
                  
                }else{
                  //真群
                  $ret['sendText'] = $set['json']['zhen']; 
                  return $ret; 
                }
           }else{
               $message['text'] = str_replace("@","", $message['text']);
               $set = new mdb\he444bot;
               $set = $set->getJson($message['bot']['API_BOT']);
               if(empty($set['json']['kefu'])){
                   $ret['sendText'] = '这不是汇旺担保官方人员，小心上当受骗，请立即拉黑。';
                   return $ret; 
               }else{
                   $kf = explode("\n",$set['json']['kefu']);
                   if(!empty($kf)){
                       $newArray = array_fill_keys($kf, 1); 
                       if (array_key_exists($message['text'], $newArray)) {
                            $ret['sendText'] = '这是真官方人员，请加为好友并备注个只有自己知道的名字。';
                            return $ret; 
                        } else {
                            $ret['sendText'] = '这不是汇旺担保官方人员，小心上当受骗，请立即拉黑。';
                            return $ret; 
                        }
                   }
 
               }
               
           } 
        }else if($message['chatType'] == 'supergroup'){ 
            if($message['text'] == "验群"){
                $quninfo = mdb\he444bot_list::where("qunid",$message['chatId'])->find();
                if($quninfo){
                    $set = new mdb\he444bot;
                    $set = $set->getJson($message['bot']['API_BOT']);
                    $text = str_replace("{qunname}", $message['chatName'], $set['json']['qunzhen']);//换行
                    
                     $ret['sendText'] = $text;
                     return $ret;  


                }
                
            }
            
            if($message['text'] == '初始化'){
                $set = new mdb\he444bot;
                $set = $set->getJson($message['bot']['API_BOT']);
                if(!pipei($set['json']['kefu'],$message['formUser'])){
                    return $ret;  
                }
                $quninfo = mdb\he444bot_list::where("qunid",$message['chatId'])->find();
                if($quninfo){
                    $ret['sendText'] = "初始化完成 该群是真群"; 
                    $base = new Base();
                    $base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text=您好，请先描述一下具体交易内容跟规则，交易员稍后将汇总编辑成交易详情给交易双方确认，然后开始交易。
交易过程中为了避免不必要的纠纷，请按照我们的流程和步骤进行，感谢各位配合！
担保流程：@dbliucheng 
安全防范：@HuioneAQ
汇旺担保核心群 @daqun 还没加群的老板可以加一下，有什么不清楚的地方可以随时问本群交易员

⚠️进群后请认准群内官方人员的管理员身份，不是官方管理员身份发的上押地址，都是假冒的骗子，切勿相信！群内交易详情未确认，押金未核实到账，禁止交易，否则造成损失，自行承担责任，平台概不负责。

⚠️汇旺担保工作人员作息时间：🕙早上上班时间：北京时间9点！ 🕙晚上下班时间：北京时间3点！

⚠️专群担保交易为一对一交易，所有交易记录需要在担保群内体现出来，禁止交易双方私下拉群交易，私下拉群交易不在本群担保范围内，特殊事项请联系本群交易员对接。

温馨提示：
1、交易方进交易群后，可以先上押再谈交易内容、规则。一个上押下押周期内，佣金不足20u的，以20u结算扣除手续费，上押前请交易双方务必斟酌好，是否已经协商交易内容规则。
2、即日起，凡是车队（跑分、代收代付）专群跑分类交易开群上押要求必须上押800u起，普通交易不限制最低上押金额。
3、请尽量使用冷钱包上押,不要用交易所直接提u上押,使用交易所提u上押的请上押时候说明是交易所提的u,并同时说明下押地址。
4、由于群资源紧张，如本群当天无上押，即被回收；后续如需交易，请联系 @hwdb 开新群。

⚠️请供需双方确定一下各方负责人，以后是否下押以及下押到哪，需要交易详情上的供需双方负责人确认，决定权在负责人手里，本群为私群，只能对应一个供方负责人和一个需方负责人。请不要拉无关人员进群，谁拉进来的人谁负责。人进齐后请通知交易员锁群",1);

$base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text={$set['json']['chushihua2']}",2);

 

 $base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text={$set['json']['chushihua3']}",3);

$base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text={$set['json']['chushihua4']}",4);

$base->sendUrl("/sendPhoto?chat_id={$message['chatId']}&photo={$set['json']['chushihua5']}",5);

$base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text={$set['json']['chushihua6']}",6);


$base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text={$set['json']['chushihua7']}",7);


// $base->sendUrl("/sendMessage?chat_id={$message['chatId']}&text=TL8TBpubVzBr1UWPXBXU8Pci5ZAip9SwEf");


                    return $ret; 
                }else{
                   $ret['sendText'] = "初始化完成 该群是假群";
                    return $ret; 
                }
            }
            
            if($ss = pipei($message['text'],"^设置编号\s?(\w+)$")){
                $set = new mdb\he444bot;
                $set = $set->getJson($message['bot']['API_BOT']);
                if(pipei($set['json']['kefu'],$message['formUser'])){
                    $quninfo = mdb\he444bot_list::where("qunid",$message['chatId'])->find();
                    if(empty($quninfo)){
                       $sql['bianhao'] = $ss[1];
                       $sql['qunid'] = $message['chatId'];
                       $sql['qunname'] = $message['chatName'];
                       $sql['qunuser'] = $message['chatUser'];
                       $quninfo = new mdb\he444bot_list;
                       $quninfo->create($sql); 
                       $ret['sendText'] = "<b>设置群编号成功</b>";
                       return $ret; 
                    }else{
                       $sql['bianhao'] = $ss[1];
                       $sql['qunid'] = $message['chatId'];
                       $sql['qunname'] = $message['chatName'];
                       $sql['qunuser'] = $message['chatUser'];
                       mdb\he444bot_list::where("qunid",$sql['qunid'])->update($sql);
                       $ret['sendText'] = "<b>更新群编号成功</b>";
                       return $ret; 
                    }
                }
            } 
        }
        
        
        
        #代码结束
        return $ret;     
    }
} 
