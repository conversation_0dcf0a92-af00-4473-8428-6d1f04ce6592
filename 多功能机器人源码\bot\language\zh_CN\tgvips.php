<?php
return [
    '语言包键名' => "这里是对应的值,你可以增加N个键名对应N个值,调用时调用键名获得对应的值,开发完成后复制本文件到en目录下,然后把对应值翻译为其它语言即可,框架具备多语言系统会自动根据用户当前的语言读取对应键名获得值", 
    '启动按钮'=>"💎电报会员",
    '开通电报会员'=>"开通电报会员",
    '3个月'=>"3个月",
    '6个月'=>"6个月",
    '12个月'=>"12个月",
    '开通时长'=>"开通时长",
    '订单金额'=>"订单金额",
    '确认支付'=>"确认支付",
    '正在赠送错误'=>"<b>你正在赠送会员,目标用户名错误..</b>\n<code>请重新输入对方电报用户名</code>",
    '正在开通电报会员'=>" 你正在开通电报高级用户(会员)...",
    '给自己开通'=>"给自己开通",
    '赠送给他人'=>"赠送给他人",
    '个月'=>"个月", 
    '输入它的电报用户名'=>"输入Ta的电报用户名：", 
    '你正在赠送电报会员'=>"你正在赠送电报会员..", 
    '赠送目标'=>"赠送目标", 
    '用户名'=>"用户名", 
    '昵称名'=>"昵称名", 
    '你正在开通电报会员'=>"你正在开通电报会员...", 
    '暂时不允许给他人开通会员'=>"暂时不允许给他人开通会员！", 
    '未设置用户名无法开通'=>"<b>抱歉,你尚未设置用户名,无法开通VIP</b>", 
    '余额不足请充值'=>"<b>很抱歉,你的账户余额不足,请充值..</b>", 
    '开通结果'=>"<b>【开通结果】</b>", 
    '交易凭证'=>"交易凭证", 
    '失败原因'=>"失败原因", 
    '联系管理员反馈'=>"请联系管理员反馈此问题！", 
    '开通成功'=>"✅恭喜你,开通成功·请留意开通状态！", 
   ];